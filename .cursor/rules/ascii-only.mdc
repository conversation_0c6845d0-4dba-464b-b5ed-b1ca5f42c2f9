---
alwaysApply: true
---

# Cursor Rule: ASCII-Only Source and Docs

This rule ensures all source code and project documentation use only ASCII characters. Non-ASCII characters may be replaced during refactoring. If a non-ASCII character must be preserved, include a clear comment explaining why.

## Scope

- Source code (all languages)
- Markdown and text documentation
- Configuration files (YAML, JSON, TOML, etc.)
- Test data and fixtures (unless testing exact non-ASCII payload behavior; see Exceptions)

## Rationale

- Avoids encoding issues across tools, CI systems, and editors
- Keeps diffs clear and consistent
- Prevents accidental non-breaking spaces and special punctuation from slipping into code

## Replacement Guidance (Common Mappings)

- Em dash or En dash: use "-"
- Single quotes: use "'"
- Double quotes: use "\""
- Ellipsis: use "..."
- Arrow right: use "->"
- Arrow left: use "<-"
- Non-breaking space (U+00A0): replace with normal space " "
- Box-drawing/Unicode art: replace with plain ASCII bullets or code blocks

If you encounter a non-ASCII symbol not listed above, replace it with the closest ASCII equivalent.

## Exceptions (When Non-ASCII Is Intentional)

- Exact payload validation (for example, verifying an external API returns non-ASCII data)
- Localized strings in product UX (if explicitly required)
- Cryptographic material where bytes are represented literally
- Folder structure or hierarchy diagrams in documentation where non-ASCII box-drawing characters materially improve clarity (prefer ASCII alternatives; if non-ASCII is used, add an explanatory comment or limit usage to this rule file/examples)
- This rule file is an explicit exception to the ASCII-only policy so that "Before" examples can show non-ASCII characters clearly.
- Markdown documentation

When keeping a non-ASCII character is necessary, add a comment immediately above or inline, such as:

```text
// NON-ASCII INTENTIONAL: required to validate external API payload example
```

This notification is not necessary in this file, as it is an explicit exception.

## AI Instructions

- When writing or refactoring code or docs, normalize to ASCII.
- Before submitting edits, scan diffs for non-ASCII and replace with ASCII equivalents.
- If preserving a non-ASCII character is required, add a comment explaining why (see Exceptions) and keep usage minimal.
- Do not alter indentation style; only replace the glyphs themselves.
- For code blocks and examples, prefer ASCII punctuation and arrows.

## Developer Workflow

- Quick scan for non-ASCII in the repo:

```bash
rg "[^\x00-\x7F]" -n
```

- Scan staged changes only:

```bash
git diff --cached | rg "[^\x00-\x7F]"
```

- Example replacements to apply during review:

```text
"Quoted text"      -> "Quoted text"
FSA → Gateway      -> FSA -> Gateway
Status – pending   -> Status - pending
… and so on        -> ... and so on
```

## Examples (Before -> After)


- Markdown
```md
Before: FSA → RH → Gateway
After:  FSA -> RH -> Gateway
```

- Go code
```go
// Before:
log.Infof("Gateway authenticated — org=%s", orgID)

// After:
log.Infof("Gateway authenticated - org=%s", orgID)
```

- JSON
```json
// Before
{"message": "Success…"}

// After
{"message": "Success..."}
```

## Enforcement Notes

- Prefer replacements during regular refactors rather than large formatting-only PRs.
- For automated checks, consider a CI step that fails on new non-ASCII in diffs.
- Keep this rule balanced with real-world requirements (see Exceptions).
# Cursor Rule: ASCII-Only Source and Docs

This rule ensures all source code and project documentation use only ASCII characters. Non-ASCII characters may be replaced during refactoring. If a non-ASCII character must be preserved, include a clear comment explaining why.

## Scope

- Source code (all languages)
- Markdown and text documentation
- Configuration files (YAML, JSON, TOML, etc.)
- Test data and fixtures (unless testing exact non-ASCII payload behavior; see Exceptions)

## Rationale

- Avoids encoding issues across tools, CI systems, and editors
- Keeps diffs clear and consistent
- Prevents accidental non-breaking spaces and special punctuation from slipping into code

## Replacement Guidance (Common Mappings)

- Em dash or En dash: use "-"
- Single quotes: use "'"
- Double quotes: use "\""
- Ellipsis: use "..."
- Arrow right: use "->"
- Arrow left: use "<-"
- Non-breaking space (U+00A0): replace with normal space " "
- Box-drawing/Unicode art: replace with plain ASCII bullets or code blocks

If you encounter a non-ASCII symbol not listed above, replace it with the closest ASCII equivalent.

## Exceptions (When Non-ASCII Is Intentional)

- Exact payload validation (for example, verifying an external API returns non-ASCII data)
- Localized strings in product UX (if explicitly required)
- Cryptographic material where bytes are represented literally
- Folder structure or hierarchy diagrams in documentation where non-ASCII box-drawing characters materially improve clarity (prefer ASCII alternatives; if non-ASCII is used, add an explanatory comment or limit usage to this rule file/examples)
- This rule file is an explicit exception to the ASCII-only policy so that "Before" examples can show non-ASCII characters clearly.

When keeping a non-ASCII character is necessary, add a comment immediately above or inline, such as:

```text
// NON-ASCII INTENTIONAL: required to validate external API payload example
```

This notification is not necessary in this file, as it is an explicit exception.

## AI Instructions

- When writing or refactoring code or docs, normalize to ASCII.
- Before submitting edits, scan diffs for non-ASCII and replace with ASCII equivalents.
- If preserving a non-ASCII character is required, add a comment explaining why (see Exceptions) and keep usage minimal.
- Do not alter indentation style; only replace the glyphs themselves.
- For code blocks and examples, prefer ASCII punctuation and arrows.

## Developer Workflow

- Quick scan for non-ASCII in the repo:

```bash
rg "[^\x00-\x7F]" -n
```

- Scan staged changes only:

```bash
git diff --cached | rg "[^\x00-\x7F]"
```

- Example replacements to apply during review:

```text
"Quoted text"      -> "Quoted text"
FSA → Gateway      -> FSA -> Gateway
Status – pending   -> Status - pending
… and so on        -> ... and so on
```

## Examples (Before -> After)


- Markdown
```md
Before: FSA → RH → Gateway
After:  FSA -> RH -> Gateway
```

- Go code
```go
// Before:
log.Infof("Gateway authenticated — org=%s", orgID)

// After:
log.Infof("Gateway authenticated - org=%s", orgID)
```

- JSON
```json
// Before
{"message": "Success…"}

// After
{"message": "Success..."}
```

## Enforcement Notes

- Prefer replacements during regular refactors rather than large formatting-only PRs.
- For automated checks, consider a CI step that fails on new non-ASCII in diffs.
- Keep this rule balanced with real-world requirements (see Exceptions).
