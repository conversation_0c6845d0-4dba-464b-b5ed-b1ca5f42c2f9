# Swagger Documentation Standards

Guidelines for creating consistent and comprehensive Swagger/OpenAPI documentation for API endpoints.

## Overview

This project uses Swagger annotations in Go code to automatically generate OpenAPI documentation. The documentation is generated during deployment - **do not manually edit the OpenAPI YAML files**.

## Core Principles

1. **Auto-Generation**: Swagger docs are generated from Go annotations during deployment
2. **Consistency**: Follow established patterns for all endpoints
3. **Completeness**: Document all parameters, responses, and error cases
4. **Clarity**: Use clear, descriptive language for summaries and descriptions

## Required Setup

### Environment Configuration

**SWAGGER_SCHEME Environment Variable:**
- **Local Development**: Set `SWAGGER_SCHEME=HTTP` to use HTTP scheme
- **Production**: Leave unset or set to any other value to use HTTPS (default)

The Swagger UI will dynamically serve the correct scheme based on this environment variable.

### 1. Main Application Setup

The main.go file must include:

```go
// @title           [Service Name] API
// @version         1.0
// @description     [Service description]
// @BasePath        /api
// @schemes         https
// @securityDefinitions.apikey  JWTAuth
// @in                          header
// @name                        jwt-token

package main

import (
    // ... other imports
    _ "[module-path]/docs"  // Import generated docs
    // ... other imports
)
```

### 2. Swagger UI Configuration

Ensure the router uses the generated documentation:

```go
// Use generated docs automatically
uiHandler := httpSwagger.WrapHandler
router.PathPrefix("/docs/[service]/").Handler(docsSecurity(uiHandler))
```

## Annotation Standards

### Handler Function Documentation

Every API handler must include complete Swagger annotations:

```go
// Handler description goes here.
//
// @Summary      [Brief one-line description]
// @Description  [Detailed multi-line description explaining purpose, behavior, side effects]
// @Tags         [category]
// @Accept       [content-type]
// @Produce      [content-type]
// @Param        [param-name]  [location]  [type]  [required]  "[description]"
// @Success      [code]        {object}    [response-type]     "[success description]"
// @Failure      [code]        {object}    [error-type]        "[error description]"
// @Router       /api/[version]/[path] [method]
func Handler(w http.ResponseWriter, r *http.Request) {
```

### Parameter Documentation Patterns

#### Headers (Required)
```go
// @Param        gateway-device-id  header  string  true   "Gateway device identifier (MachineKey)"
// @Param        message-type       header  string  true   "Message type (must be 'authenticate')"
// @Param        x-api-key          header  string  true   "API key for gateway authentication"
```

#### Headers (Optional)
```go
// @Param        gateway-version    header  string  false  "Gateway software version"
// @Param        tz                 header  string  false  "Gateway timezone"
```

#### Request Body
```go
// @Param        body  body  [package].[StructName]  true  "Request payload description"
```

#### Query Parameters
```go
// @Param        limit   query  int     false  "Maximum number of results (default: 100)"
// @Param        offset  query  int     false  "Number of results to skip (default: 0)"
// @Param        filter  query  string  false  "Filter criteria"
```

#### Path Parameters
```go
// @Param        id  path  string  true  "Resource identifier"
```

### Response Documentation

#### Success Responses
```go
// @Success      200  {object}  [package].[ResponseType]  "Operation successful"
// @Success      201  {object}  [package].[ResponseType]  "Resource created successfully"
// @Success      204  {string}  string                    "Operation completed successfully"
```

#### Error Responses (Standard)
```go
// @Failure      400  {object}  map[string]interface{}  "Bad Request - invalid input parameters"
// @Failure      401  {object}  map[string]interface{}  "Unauthorized - invalid credentials"
// @Failure      403  {object}  map[string]interface{}  "Forbidden - insufficient permissions"
// @Failure      404  {object}  map[string]interface{}  "Not Found - resource does not exist"
// @Failure      500  {object}  map[string]interface{}  "Internal Server Error"
```

### Content Type Specifications

#### Common Patterns
```go
// @Accept       json                    // For JSON APIs
// @Accept       application/json        // Explicit JSON
// @Accept       application/octet-stream // For binary data
// @Accept       multipart/form-data     // For file uploads

// @Produce      json                    // For JSON responses
// @Produce      application/json        // Explicit JSON
```

## Schema Documentation

### Request Body Schemas

Create dedicated struct types with proper JSON tags and examples:

```go
// requestBody represents the [operation] request payload
type requestBody struct {
    Token    string `json:"token" example:"abc123token" binding:"required"`        // Authentication token description
    DeviceID string `json:"device_id" example:"device-001" binding:"required"`    // Device identifier
    Optional string `json:"optional,omitempty" example:"value"`                   // Optional field description
}
```

### Response Schemas

Use existing shared types when possible, or create response-specific types:

```go
// ResponsePayload represents the [operation] response
type ResponsePayload struct {
    Success bool                 `json:"success" example:"true"`                    // Operation success status
    Data    []SomeDataType      `json:"data"`                                      // Response data
    Message string              `json:"message,omitempty" example:"Success"`       // Optional message
}
```

## Environment-Based Documentation System

This codebase uses an environment-based OpenAPI documentation system that generates different API documentation for different environments with proper security isolation.

### Environment Tags

Use environment tags in your Swagger annotations to control endpoint visibility:

```go
// @Tags         env:dev, env:qa, user                    // Available in dev and qa
// @Tags         env:dev, env:qa, env:sandbox, data       // Available in dev, qa, and sandbox
// @Tags         env:prod, admin                          // Production only
```

**Environment Tags:**
- `env:dev` - Development environment (both cloud and local)
- `env:qa` - QA/testing environment
- `env:prod` - Production environment
- `env:sandbox` - Sandbox environment

### Local vs Cloud Development

The system supports both local and cloud development:
- **Local Development**: `SWAGGER_SCHEME=HTTP` - Uses HTTP scheme
- **Cloud Development**: `SWAGGER_SCHEME=HTTPS` or unset - Uses HTTPS scheme

Both show the same endpoints (tagged with `env:dev`) but with different schemes.

### Generated Files

| Environment | File Name | Purpose |
|-------------|-----------|---------|
| Development (Cloud) | `broker-dev.yaml` | HTTPS development endpoints |
| Development (Local) | `broker-dev-local.yaml` | HTTP development endpoints |
| QA | `broker-qa.yaml` | QA endpoints |
| Production | `broker.yaml` | Production endpoints (generic name for security) |
| Sandbox | `broker-sandbox.yaml` | Sandbox endpoints |

### Security & Access Control

**Environment-Specific Access (Routes are only registered for allowed files):**

| Environment | Can Access | Cannot Access |
|-------------|------------|---------------|
| **Development** | dev, dev-local, qa, sandbox, broker.yaml (legacy) | ❌ |
| **QA** | qa, broker.yaml (legacy) | dev, dev-local, sandbox |
| **Production** | broker.yaml | dev, dev-local, qa, sandbox |
| **Sandbox** | sandbox, broker.yaml (legacy) | dev, dev-local, qa |

**Security Features:**
- Routes for unauthorized environments return 404 (don't exist)
- Production uses generic `broker.yaml` to hide environment pattern
- Environment tags are stripped from final documentation

### Current Broker Service Configuration

All broker endpoints have been configured with environment tags:

**Gateway Endpoints** (`/v3/gateway/*`): `env:dev, env:qa, gateway`
- `/v3/gateway/authenticate`
- `/v3/gateway/ingest` 
- `/v3/gateway/update`

**User Endpoints** (`/v3/user/*`): `env:dev, env:qa, env:sandbox, user`
- `/v3/user/authenticate`
- `/v3/user/profile`
- `/v3/user/instruction`
- `/v3/user/account/notifications`
- `/v3/user/account/close`

**Data Endpoints** (`/v3/data/*`): `env:dev, env:qa, env:sandbox, data`
- `/v3/data/device`
- `/v3/data/fault`

## Category/Tag Standards

### Established Categories
- **gateway**: Gateway-related endpoints (authenticate, ingest, update)
- **user**: User management endpoints (profile, settings)
- **device**: Device management endpoints
- **data**: Data retrieval endpoints

### Tag Usage with Environment Tags
```go
// @Tags         env:dev, env:qa, gateway              // Gateway endpoint for dev and qa
// @Tags         env:dev, env:qa, env:sandbox, user    // User endpoint for dev, qa, and sandbox
// @Tags         env:prod, admin                       // Production-only admin endpoint
// @Tags         env:dev, env:qa, env:sandbox, data    // Data endpoint for non-prod environments
```

**Important**: Always include both environment tags and functional tags. Environment tags are automatically stripped from the final documentation.

## Router Path Standards

### Path Format
All paths should NOT include the `/api` prefix since it's handled by the `@BasePath /api` setting:

```go
// @Router       /v3/gateway/authenticate [post]
// @Router       /v3/user/profile [get]
// @Router       /v3/data/device [get]
```

### HTTP Methods
Use lowercase method names:
- `[get]` for GET requests
- `[post]` for POST requests
- `[put]` for PUT requests
- `[patch]` for PATCH requests
- `[delete]` for DELETE requests

## Complete Examples

### Gateway Authentication Endpoint
```go
// Handler authenticates software gateways and returns configuration.
//
// @Summary      Authenticate software gateway
// @Description  Authenticates a software gateway and returns configuration settings, device information, and cloud credentials
// @Tags         env:dev, env:qa, gateway
// @Accept       json
// @Produce      json
// @Param        gateway-device-id  header    string                           true   "Gateway device identifier (MachineKey)"
// @Param        message-type       header    string                           true   "Message type (must be 'authenticate')"
// @Param        gateway-version    header    string                           false  "Gateway software version"
// @Param        body               body      authenticate.requestBody         true   "Authentication request body"
// @Success      200                {object}  softwareGateway.GlobalSettings   "Authentication successful - returns gateway configuration"
// @Failure      401                {object}  map[string]interface{}           "Unauthorized - invalid credentials or gateway not found"
// @Failure      500                {object}  map[string]interface{}           "Internal Server Error"
// @Router       /v3/gateway/authenticate [post]
func Handler(w http.ResponseWriter, r *http.Request) {
```

### User Profile Endpoint
```go
// HandlerWithDeps returns user profile information.
//
// @Summary      Get current user profile
// @Description  Returns the authenticated user's profile and application version.
// @Tags         env:dev, env:qa, env:sandbox, user
// @Produce      json
// @Security     JWTAuth
// @Success      200  {object}  profile.ResponsePayload
// @Failure      401  {object}  map[string]interface{}  "Unauthorized"
// @Failure      500  {object}  map[string]interface{}  "Internal Server Error"
// @Router       /v3/user/profile [get]
func HandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
```

### Data Retrieval Endpoint
```go
// Handler retrieves device data with filtering and pagination.
//
// @Summary      Get device data
// @Description  Retrieves device data with optional filtering, sorting, and pagination
// @Tags         env:dev, env:qa, env:sandbox, data
// @Produce      json
// @Security     JWTAuth
// @Param        limit      query     int     false  "Maximum number of results (default: 100)"
// @Param        offset     query     int     false  "Number of results to skip (default: 0)"
// @Param        device_id  query     string  false  "Filter by specific device ID"
// @Param        start_date query     string  false  "Start date filter (ISO 8601 format)"
// @Param        end_date   query     string  false  "End date filter (ISO 8601 format)"
// @Success      200        {object}  data.DeviceDataResponse  "Device data retrieved successfully"
// @Failure      400        {object}  map[string]interface{}   "Bad Request - invalid query parameters"
// @Failure      401        {object}  map[string]interface{}   "Unauthorized"
// @Failure      500        {object}  map[string]interface{}   "Internal Server Error"
// @Router       /v3/data/device [get]
func Handler(w http.ResponseWriter, r *http.Request) {
```

## Common Mistakes to Avoid

### ❌ Don't Do This
```go
// @Tags         api                        // Too generic
// @Tags         broker                     // Old system - no longer used
// @Tags         gateway                    // Missing environment tags
// @Router       /api/v3/endpoint [post]    // Incorrect: includes /api prefix
// @Param        id  query  string          // Missing required flag and description
// @Success      200                        // Missing response type and description
```

### ✅ Do This Instead
```go
// @Tags         env:dev, env:qa, gateway              // Include environment tags
// @Tags         env:dev, env:qa, env:sandbox, user    // User endpoint with sandbox access
// @Router       /v3/gateway/endpoint [post]
// @Param        id  query  string  true  "Resource identifier"
// @Success      200  {object}  ResponseType  "Operation successful"
```

### Environment Tag Guidelines

**Choosing the Right Environment Tags:**
- `env:dev` - Always include for development and testing
- `env:qa` - Include for endpoints that need QA testing
- `env:sandbox` - Include for user-facing endpoints that should be available in sandbox
- `env:prod` - Only include when the endpoint is ready for production use

**Common Patterns:**
```go
// Internal/debugging endpoints (dev only)
// @Tags         env:dev, debug

// Gateway infrastructure (dev + qa only)  
// @Tags         env:dev, env:qa, gateway

// User-facing features (dev + qa + sandbox)
// @Tags         env:dev, env:qa, env:sandbox, user

// Production-ready endpoints (all environments)
// @Tags         env:dev, env:qa, env:sandbox, env:prod, user
```

## Swagger Parsing Errors to Avoid

### Interface{} Type Examples

**❌ Don't Use Null Examples with interface{}:**
```go
type ErrorResponse struct {
    Data interface{} `json:"data" example:"null"`  // Causes parsing error
}
```

**✅ Use interface{} Without Examples:**
```go
type ErrorResponse struct {
    Data interface{} `json:"data"`  // Let Swagger infer the type
}
```

**Reason:** The `swag` tool cannot parse `interface{}` types with `null` examples, resulting in errors like:
```
Error parsing type definition: [data]: any is unsupported type in example value null
```

### Boolean Array Examples

**❌ Don't Use Malformed Array Examples:**
```go
type ChannelStatus struct {
    ChannelRed []bool `json:"red" example:"[false"`  // Malformed JSON
}
```

**✅ Use Proper JSON Array Examples or No Examples:**
```go
type ChannelStatus struct {
    ChannelRed []bool `json:"red" example:"[false,true,false]"`  // Valid JSON array
    // OR
    ChannelRed []bool `json:"red"`  // No example, let Swagger infer
}
```

**Reason:** Malformed JSON in examples causes parsing errors like:
```
example value [false can't convert to boolean err: strconv.ParseBool: parsing "[false": invalid syntax
```

### Complex Type Examples

**Best Practices for Examples:**
1. **Simple types**: Use examples freely
   ```go
   Name string `json:"name" example:"John Doe"`
   Age  int    `json:"age" example:"25"`
   ```

2. **Complex types**: Avoid examples or use simple valid JSON
   ```go
   Data interface{} `json:"data"`  // No example
   Config map[string]interface{} `json:"config"`  // No example
   ```

3. **Arrays**: Use valid JSON array syntax
   ```go
   Tags []string `json:"tags" example:"[\"tag1\",\"tag2\"]"`
   IDs  []int    `json:"ids" example:"[1,2,3]"`
   ```

4. **Nested structs**: Let Swagger auto-generate examples
   ```go
   User UserProfile `json:"user"`  // Swagger will use UserProfile's examples
   ```

5. **json.RawMessage types**: Avoid examples completely
   ```go
   DeviceID json.RawMessage `json:"device_id"`  // No example - too complex for Swagger
   ```

## Authentication Standards

### JWT Authentication

This API uses JWT (JSON Web Token) authentication with a custom header format:

**Header Name:** `jwt-token` (not `Authorization`)
**Security Scheme:** `JWTAuth` (not `BearerAuth`)

#### Global Security Definition
```go
// @securityDefinitions.apikey  JWTAuth
// @in                          header
// @name                        jwt-token
```

#### Endpoint Security Annotation
```go
// @Security     JWTAuth
```

#### Authentication Flow
1. Client authenticates via `/v3/user/authenticate` endpoint with username/password
2. Server returns JWT token in the `token` field of the response data
3. Client extracts the token value from `response.data.token`
4. Client includes this token in the `jwt-token` header for protected endpoints
5. Server validates token and extracts user permissions

#### Authentication Response Structure
```json
{
  "status": "success",
  "data": {
    "user": { ... },
    "token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "Authentication successful",
  "code": 200
}
```

**Usage:** Extract `response.data.token` and send as:
```
jwt-token: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### Protected Endpoints
The following endpoints require JWT authentication:
- `/v3/data/device` - Device data retrieval
- `/v3/user/profile` - User profile information
- `/v3/data/fault` - Fault data retrieval
- `/v3/user/account/*` - User account management
- `/v3/user/instruction` - User instructions

#### Gateway Authentication (Separate)
Gateway endpoints use different authentication:
- `/v3/gateway/authenticate` - Uses gateway credentials
- `/v3/gateway/ingest` - Uses API key authentication
- `/v3/gateway/update` - Uses gateway token authentication

#### How to Use Authentication in Swagger UI

**Step 1: Get JWT Token**
1. Use the `/v3/user/authenticate` endpoint in Swagger UI
2. Enter your username and password in the request body:
   ```json
   {
     "username": "<EMAIL>",
     "password": "your_password"
   }
   ```
3. Execute the request
4. Copy the token from `response.data.token` in the response

**Step 2: Authorize Swagger UI**
1. Click the "Authorize" button (🔒) at the top right of Swagger UI
2. In the JWTAuth field, paste your JWT token
3. Click "Authorize"
4. All subsequent API calls will automatically include the `jwt-token` header

**Important:** Do not use `@Security BearerAuth` or `Authorization` header - this API uses the custom `jwt-token` header format.

## Documentation Generation

### Automatic Generation
- Documentation is generated automatically during deployment
- Do not run `swag init` manually  
- Do not edit generated files in `/docs/` directory
- Do not manually edit OpenAPI YAML files

### Build Process

The build automatically generates environment-specific files:

```dockerfile
# Generate base swagger.json
swag init -g ./main.go -o ./docs --parseDependency --parseInternal

# Generate environment-specific OpenAPI files
go run /tools/openapi-filter -in /app/docs/swagger.json -env dev -out /app/openapi/broker-dev.yaml
go run /tools/openapi-filter -in /app/docs/swagger.json -env dev -scheme http -out /app/openapi/broker-dev-local.yaml
go run /tools/openapi-filter -in /app/docs/swagger.json -env qa -out /app/openapi/broker-qa.yaml
go run /tools/openapi-filter -in /app/docs/swagger.json -env prod -out /app/openapi/broker.yaml
go run /tools/openapi-filter -in /app/docs/swagger.json -env sandbox -out /app/openapi/broker-sandbox.yaml
```

### OpenAPI Filter Tool

The `openapi-filter` tool (`/tools/openapi-filter/`) filters and modifies OpenAPI specs:

```bash
# Basic usage
go run . -in swagger.json -env dev -out filtered.yaml

# With scheme override for local development
go run . -in swagger.json -env dev -scheme http -out local.yaml
```

### Environment Variables

- `ENVIRONMENT` - Current environment (dev, qa, prod, sandbox)
- `SWAGGER_SCHEME` - HTTP for local development, HTTPS for cloud (default)

### Testing Documentation
After adding Swagger annotations:
1. Ensure the handler imports are correct
2. Verify all referenced types exist and are properly tagged
3. Check that the `/api` prefix is included in `@Router`
4. Confirm the `@Tags` category is appropriate
5. Test that the application builds successfully

## Troubleshooting

### Common Issues
1. **Missing endpoints in Swagger UI**: Check if docs package is imported in main.go
2. **Malformed annotations**: Ensure proper spacing and syntax in annotations
3. **Missing types**: Verify all referenced types are properly imported and tagged
4. **Wrong paths**: Ensure `/api` prefix is NOT included in `@Router` annotations (handled by BasePath)

### Validation Checklist
- [ ] Handler has complete Swagger annotations
- [ ] All parameters are documented with types and descriptions
- [ ] Success and error responses are specified
- [ ] Router path excludes `/api` prefix (handled by BasePath)
- [ ] **Environment tags are included** (e.g., `env:dev, env:qa`)
- [ ] **Functional tags are included** (e.g., `user`, `gateway`, `data`)
- [ ] Appropriate environment combination is chosen
- [ ] Request/response schemas are properly defined
- [ ] No `example:"null"` used with `interface{}` types
- [ ] Array examples use valid JSON syntax (e.g., `[false,true]` not `[false`)
- [ ] Complex types either have no examples or use simple valid JSON
- [ ] Application builds without errors
- [ ] `swag init` runs without parsing errors

## Maintenance

### When Adding New Endpoints
1. Follow the established patterns in this document
2. Use existing categories when appropriate
3. Create new schemas in dedicated files when needed
4. Ensure consistency with existing endpoint documentation
5. Test the application builds successfully

### When Updating Existing Endpoints
1. Update Swagger annotations to match code changes
2. Maintain backward compatibility in documentation when possible
3. Update request/response schemas as needed
4. Verify all parameter descriptions remain accurate
