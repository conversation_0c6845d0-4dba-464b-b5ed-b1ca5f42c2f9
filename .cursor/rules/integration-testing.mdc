---
globs: **/testing/integration/**/*.go
description: Comprehensive integration testing patterns and best practices for this codebase
---

# Integration Testing Rules

## 🎯 SUCCESS CASES ONLY - CRITICAL RULE

### 0.0. Focus Exclusively on Success Cases
```go
// ❌ WRONG - Don't test error cases in integration tests
func TestUserProfile_ValidationErrors(t *testing.T) {
    // Testing validation errors, unauthorized access, etc.
}

func TestUserProfile_Unauthorized(t *testing.T) {
    // Testing authentication failures
}

// ✅ CORRECT - Only test success scenarios
func TestUserProfileGet_Success(t *testing.T) {
    // Test successful profile retrieval
}

func TestUserProfileUpdate_Success(t *testing.T) {
    // Test successful profile update
}
```

**Rule**: **ONLY** write integration tests for success cases. Do not test error scenarios, validation failures, unauthorized access, or edge cases in integration tests. These belong in unit tests.

**Why**: Integration tests verify that the system works correctly when everything is set up properly. Error cases and edge cases should be tested at the unit level where you have full control over the test environment.

**What to Test in Integration Tests**:
- ✅ Successful API calls with valid data
- ✅ Successful data retrieval operations
- ✅ Successful data creation operations
- ✅ Successful data update operations
- ✅ Successful data deletion operations
- ✅ Proper response structure and data

**What NOT to Test in Integration Tests**:
- ❌ Validation errors (400 Bad Request)
- ❌ Authentication failures (401 Unauthorized)
- ❌ Authorization failures (403 Forbidden)
- ❌ Not found errors (404 Not Found)
- ❌ Server errors (500 Internal Server Error)
- ❌ Invalid input data
- ❌ Edge cases and boundary conditions

### Handling Expected Error Responses in Success Tests
```go
// ✅ ACCEPTABLE - Handle expected error responses as part of success flow
func TestUserMergeUser_Success(t *testing.T) {
    // ... test setup ...
    
    resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, mergeData)
    
    // Handle both success and expected error responses
    assert.True(t, resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusBadRequest,
        "Should return 200 or 400, got %d", resp.StatusCode)
}

// ✅ ACCEPTABLE - Handle expected not found as part of success flow
func TestUserInviteRedeem_Success(t *testing.T) {
    // ... test setup ...
    
    resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, redeemData)
    
    // Handle both success and expected not found responses
    assert.True(t, resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusNotFound,
        "Should return 200 or 404, got %d", resp.StatusCode)
}
```

**Rule**: When testing operations that may legitimately fail due to missing resources (like trying to merge non-existent users or redeem non-existent invites), it's acceptable to handle expected error responses as part of the success test flow. However, the test should still be named `_Success` and should not be testing the error condition itself.

## 🚨 Critical Rules (Must Follow)

### 0. CODE IMPLEMENTATION MUST NOT BE EDITED
```go
// ❌ WRONG - Never modify API implementation code
// Don't edit files in /workspace/microservices/onramp/
// Don't edit files in /workspace/shared/rest/onramp/

// ✅ CORRECT - Only modify test files
// Edit files in /workspace/microservices/testing/integration/
// Tests must adapt to existing API behavior
```

**Rule**: **NEVER** modify the actual API implementation code. **ONLY** modify the test files to work with the existing API. The tests must adapt to the existing API behavior, not the other way around. If the API returns unexpected data, update the test to handle it correctly.

**Why**: The API implementation is the source of truth. Tests should verify the actual behavior, not force the API to change.

### 0.1. Database Access for Verification
```go
// ✅ CORRECT - Use database to verify data and understand issues
connections := GetTestDatabaseConnectionPostgresOnly(t)
defer connections.Close()

// Check if tables exist
var tableExists struct { Count int `db:"count"` }
err := connections.Postgres.QueryRowStruct(&tableExists, `
    SELECT COUNT(*) as count 
    FROM information_schema.tables 
    WHERE table_name = 'LocationGroupRoleAssignments'
`)

// Verify test data setup
var userExists struct { Count int `db:"count"` }
err = connections.Postgres.QueryRowStruct(&userExists, `
    SELECT COUNT(*) as count 
    FROM {{User}} u
    JOIN {{AuthMethod}} am ON u.Id = am.UserId
    JOIN {{Memberships}} m ON am.Id = m.AuthMethodId AND m.OrganizationId = $1
    WHERE u.Id = $2 AND NOT u.IsDeleted AND am.IsEnabled
`, orgID, userID)
```

**Rule**: You can use PostgreSQL and database credentials to check data. Use the database to verify test data setup and cleanup, query the database to understand the actual data structure and relationships, and use database queries to debug test failures.

### 0.2. Container Environment
```go
// ❌ WRONG - Don't try to run commands in current container
// run_terminal_cmd("docker exec ...")
// run_terminal_cmd("cd /workspace && go build ...")

// ✅ CORRECT - Services run in separate containers
// The onramp server and PostgreSQL are running in other containers
// All testing is done against the running services
// Use provided database connection tools to access PostgreSQL
```

**Rule**: The onramp server and PostgreSQL are running in separate containers. **DO NOT** try to run any commands in the current container. All testing is done against the running services in other containers. Use the provided database connection tools to access PostgreSQL.

### 0.3. Never Skip Tests
```go
// ❌ WRONG - Never skip tests
// t.Skip("No existing data found - skipping test")
// t.Skip("Service not available - skipping test")

// ✅ CORRECT - Always create the data needed for the test
// If shared data doesn't exist, create test data
// If service is not available, fail the test with clear error message
// Tests must always run and provide meaningful results
```

**Rule**: **NEVER** skip tests. If shared data doesn't exist, create test data. If a service is not available, fail the test with a clear error message. Tests must always run and provide meaningful results. Skipping tests hides problems and reduces test coverage.

### 1. Database Table Names - NEVER Hardcode Table Names
```go
// ❌ WRONG - Hardcoded table names break template resolution
INSERT INTO "DEV__UserInvites" (id, email) VALUES ($1, $2)
SELECT * FROM "DEV__Organization" WHERE id = $1

// ✅ CORRECT - Use template syntax for automatic resolution
INSERT INTO {{UserInvites}} (id, email) VALUES ($1, $2)
SELECT * FROM {{Organization}} WHERE id = $1
```

**Rule**: Always use `{{TableName}}` template syntax. The database connection automatically resolves these to the correct table names based on the environment. Never hardcode table names like `"DEV__TableName"` or `"QA__TableName"`.

**Why**: Hardcoded table names break when running tests in different environments and cause template resolution failures.

### 2. Context Management - NEVER Cancel Immediately
```go
// ❌ WRONG - Causes "context canceled" errors and race conditions
ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
defer cancel() // This cancels context immediately!

// ✅ CORRECT - Use background context or store cancel function
ctx := context.Background()
// OR store cancel for later: connections.Cancel = cancel
```

**Critical Lesson**: Using `defer cancel()` immediately cancels the context, causing:
- "context canceled" errors in parallel tests
- Race conditions when multiple tests run together
- Database connection failures
- Inconsistent test results

**Rule**: Always use `ctx := context.Background()` for integration tests. The database connection and HTTP client will handle their own timeouts internally.

### 3. Database Cleanup - Create Fresh Connections
```go
// ❌ WRONG - Database closed before cleanup
pg, err := connect.Postgres(ctx, nil)
defer pg.Close()
// ... test code ...
t.Cleanup(func() {
    pg.Exec("DELETE FROM ...") // ERROR: database is closed
})

// ✅ CORRECT - Fresh connection in cleanup
t.Cleanup(func() {
    pg, err := connect.Postgres(ctx, nil)
    if err != nil {
        t.Logf("Warning: Failed to create database connection for cleanup: %v", err)
        return
    }
    defer pg.Close()
    pg.Exec("DELETE FROM ...")
})
```

**Rule**: Create fresh database connections in cleanup functions to avoid "sql: database is closed" warnings.

### 4. API Request Fields - ALWAYS Explicit Values
```go
// ❌ WRONG - Null values cause 500 Internal Server Error
request := UpdateRequest{
    Name:              stringPtr("Test"),
    Description:       nil,        // 500 error
    FlushConnectionMs: nil,        // 500 error
    EnableRealtime:    nil,        // 500 error
    IsEnabled:         nil,        // 500 error
}

// ✅ CORRECT - Explicit values prevent 500 errors
request := UpdateRequest{
    Name:              stringPtr("Test"),
    Description:       stringPtr("Test description"),
    FlushConnectionMs: intPtr(1000),
    EnableRealtime:    boolPtr(true),
    IsEnabled:         boolPtr(true),
}
```

**Rule**: Always provide explicit values for all request fields. Null values cause 500 Internal Server Error responses.

### 5. Test Data Strategy - Shared Data vs Test-Specific Data

#### 🏃‍♂️ Race Conditions Explained
Race conditions occur when multiple parallel tests modify the same data, causing unpredictable failures.

```go
// ❌ WRONG - Race condition example
func TestUpdateOrganization_Success(t *testing.T) {
    t.Parallel() // Runs in parallel
    
    // Both tests use the same organization
    orgID := uuid.MustParse("55d832bf-cd0d-5c8d-b06c-c0f73ae8b7cd")
    
    // Test 1 updates the organization
    updateData := map[string]interface{}{
        "name": "Updated by Test 1",
    }
    // ... API call to update organization
}

func TestDeleteOrganization_Success(t *testing.T) {
    t.Parallel() // Runs in parallel
    
    // Same organization! - RACE CONDITION
    orgID := uuid.MustParse("55d832bf-cd0d-5c8d-b06c-c0f73ae8b7cd")
    
    // Test 2 deletes the organization while Test 1 is updating it
    // ... API call to delete organization
}
```

**Problem**: Test 1 might update the organization while Test 2 is trying to delete it, causing unpredictable failures.

#### ✅ GET/READ Operations - Use Shared Master Data
```go
// ✅ CORRECT - Safe to use shared data for reading
func TestGetOrganization_Success(t *testing.T) {
    t.Parallel()
    
    // Use existing shared organization - SAFE because we're only reading
    orgID := uuid.MustParse("55d832bf-cd0d-5c8d-b06c-c0f73ae8b7cd")
    
    resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)
    assert.Equal(t, http.StatusOK, resp.StatusCode)
}

func TestListOrganizations_Success(t *testing.T) {
    t.Parallel()
    
    // Same shared data - SAFE because we're only reading
    resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, "/api/organizations", nil)
    assert.Equal(t, http.StatusOK, resp.StatusCode)
}
```

**Why it's safe**: Multiple tests can safely read the same data without interfering with each other.

#### ✅ CREATE/UPDATE/DELETE Operations - Create Test-Specific Data
```go
// ✅ CORRECT - Each test creates its own data
func TestCreateOrganization_Success(t *testing.T) {
    t.Parallel()
    
    // Create unique test organization for this test
    testOrgID := createTestOrganizationSQL(t, pg, "Test Org 1", "Description", "municipality")
    
    // Test creates its own data - no interference with other tests
    resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, createData)
    assert.Equal(t, http.StatusCreated, resp.StatusCode)
}

func TestUpdateOrganization_Success(t *testing.T) {
    t.Parallel()
    
    // Create different test organization for this test
    testOrgID := createTestOrganizationSQL(t, pg, "Test Org 2", "Description", "municipality")
    
    // Test updates its own data - no interference with other tests
    resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPatch, url, updateData)
    assert.Equal(t, http.StatusOK, resp.StatusCode)
}
```

**Why it's safe**: Each test modifies its own data, preventing race conditions.

#### 🎯 The Rule in Practice
```go
// ✅ CORRECT PATTERN

// For READ operations - use shared data (safe for parallel execution)
func TestGetOrganization_Success(t *testing.T) {
    t.Parallel()
    
    // Safe to use shared data for reading
    orgID := uuid.MustParse("55d832bf-cd0d-5c8d-b06c-c0f73ae8b7cd")
    
    resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)
    assert.Equal(t, http.StatusOK, resp.StatusCode)
}

// For CREATE/UPDATE/DELETE operations - create test-specific data (prevents race conditions)
func TestCreateOrganization_Success(t *testing.T) {
    t.Parallel()
    
    // Create unique test data for each test run
    testOrgID := createTestOrganizationSQL(t, pg, "Test Org", "Description", "municipality")
    
    // Test creates its own data - no interference
    resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, createData)
    assert.Equal(t, http.StatusCreated, resp.StatusCode)
}
```

**Rule**: 
- **READ operations**: Use shared master data (existing data) - safe for parallel execution
- **CREATE/UPDATE/DELETE operations**: Create test-specific data - prevents race conditions

**Key Principle**: Multiple tests can safely read the same data, but only one test should modify any piece of data.

### 6. Database Investigation - Always Check API Implementation First
```go
// ✅ CORRECT - Investigate API implementation before writing tests
// 1. Check if API uses soft delete (isdeleted flag) or hard delete
// 2. Verify what fields are required vs optional
// 3. Understand the data flow and dependencies
// 4. Check database schema and constraints

// Example: Before writing device tests, investigate:
// - Does device creation require organization to exist?
// - What fields are required vs optional?
// - Does deletion use soft delete or hard delete?
// - What are the foreign key constraints?
```

**Rule**: Always investigate the API implementation and database schema before writing tests. This prevents test failures due to missing required fields or incorrect assumptions.

### 7. Table Name Pattern - Use Template Syntax
```go
// ❌ WRONG - Hardcoded table names
SELECT * FROM "DEV__Device" WHERE OrganizationId = $1
INSERT INTO "QA__User" (id, name) VALUES ($1, $2)

// ✅ CORRECT - Use template syntax for automatic resolution
SELECT * FROM {{Device}} WHERE OrganizationId = $1
INSERT INTO {{User}} (id, name) VALUES ($1, $2)
```

**Rule**: Always use `{{TableName}}` template syntax. The database connection automatically resolves these to the correct table names based on the environment (DEV__, QA__, PROD__).

### 8. Database Connection with Environment Credentials
```go
// ✅ CORRECT - Database connection uses environment credentials
func getTestDatabaseConnection(t *testing.T) *connect.Connections {
    t.Helper()
    ctx := context.Background() // No timeout, no immediate cancellation
    connections := &connect.Connections{}
    postgresExecutor, err := connect.Postgres(ctx, nil)
    require.NoError(t, err, "Should be able to create database connection")
    connections.Postgres = postgresExecutor
    return connections
}
```

**Rule**: Database connections automatically use environment credentials. No need to manually specify connection strings - the `connect.Postgres(ctx, nil)` function handles this.

### 9. Struct Duplication Prevention - Use Existing API Structs
```go
// ❌ WRONG - Custom struct for database queries
var gateway struct {
    ID                    uuid.UUID  `db:"id"`
    Name                  string     `db:"name"`
    Description           string     `db:"description"`
    MachineKey            string     `db:"machinekey"`
    APIKey                string     `db:"apikey"`
    // ... many more fields
}

// ✅ CORRECT - Use existing API struct with proper db tags
var gateway softwaregatewayRest.SoftwareGateway
err := connections.Postgres.QueryRowStruct(&gateway, query, gatewayID)

// ✅ ACCEPTABLE - Minimal struct only when API structs don't include required fields
var device struct {
    ID        uuid.UUID `db:"id"`
    Name      string    `db:"name"`
    Type      string    `db:"type"`
    IsDeleted bool      `db:"isdeleted"` // Not in API struct
}
```

**Rule**: Use existing API structs when possible. Only create minimal custom structs when API structs don't include required fields.

## 🏗️ Test Structure Template

### Standard Success Test Structure
```go
func TestFeature_Success(t *testing.T) {
    t.Parallel()

    // 1. Wait for service to be ready
    ctx := context.Background()
    require.NoError(t, utils.AwaitOnramp(ctx, 5*time.Second), "onramp service should be ready")

    // 2. Setup database connection (only if needed for test data creation)
    pg, err := connect.Postgres(ctx, nil)
    require.NoError(t, err, "Should be able to create database connection")
    defer pg.Close()

    // 3. Setup test client and session
    client, sessionCookie := setupIntegrationTest(t)

    // 4. Use shared data for READ operations OR create test data for mutations
    // For READ operations:
    orgID := uuid.MustParse("55d832bf-cd0d-5c8d-b06c-c0f73ae8b7cd") // Existing org
    
    // For CREATE/UPDATE/DELETE operations:
    // testOrgID := createTestOrganizationSQL(t, pg, "Test Org", "Description", "municipality")

    // 5. Track created resources for cleanup
    var createdResources []uuid.UUID
    // if testOrgID != uuid.Nil {
    //     createdResources = append(createdResources, testOrgID)
    // }

    // 6. Make API request with detailed logging
    url := fmt.Sprintf("%s/organizations/%s/feature", baseURL, orgID)
    requestData := map[string]interface{}{
        "field1": "value1",
        "field2": "value2",
    }
    
    t.Logf("Request URL: %s", url)
    requestJSON, _ := json.Marshal(requestData)
    t.Logf("Request Body: %s", string(requestJSON))
    
    resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, requestData)

    // 7. Response logging and verification
    t.Logf("Response Status: %d", resp.StatusCode)
    body, _ := io.ReadAll(resp.Body)
    t.Logf("Response Body: %s", string(body))
    resp.Body = io.NopCloser(bytes.NewReader(body)) // Reset body for parsing

    // 8. Verify SUCCESS response (200 OK)
    assert.Equal(t, http.StatusOK, resp.StatusCode)

    // 9. Parse and validate SUCCESS response structure
    var responseStruct ResponseStruct
    apiResp := parseAPIResponse(t, resp, &responseStruct)
    assert.Equal(t, "success", apiResp.Status)
    assert.Equal(t, 200, apiResp.Code)
    assert.Equal(t, "Request Succeeded", apiResp.Message)

    // 10. Verify response data is valid
    assert.NotNil(t, apiResp.Data, "Response data should not be nil")
    // Add specific assertions for expected data structure

    // 11. Post-test cleanup
    t.Cleanup(func() {
        // Setup database connection for cleanup
        pg, err := connect.Postgres(ctx, nil)
        if err != nil {
            t.Logf("Warning: Failed to create database connection for cleanup: %v", err)
            return
        }
        defer pg.Close()
        
        // Hard delete created resources
        if len(createdResources) > 0 {
            deleteQuery := `DELETE FROM {{TableName}} WHERE id = ANY($1)`
            _, err := pg.Exec(deleteQuery, createdResources)
            if err != nil {
                t.Logf("Warning: Failed to cleanup test resources: %v", err)
            }
        }
    })
}
```

### Success Test Naming Convention
```go
// ✅ CORRECT - Success test naming
func TestUserProfileGet_Success(t *testing.T) { }
func TestUserProfileUpdate_Success(t *testing.T) { }
func TestOrganizationCreate_Success(t *testing.T) { }
func TestDeviceList_Success(t *testing.T) { }

// ❌ WRONG - Don't include error cases
func TestUserProfile_ValidationErrors(t *testing.T) { }
func TestUserProfile_Unauthorized(t *testing.T) { }
func TestUserProfile_NotFound(t *testing.T) { }
```

**Rule**: All integration test functions must end with `_Success` to clearly indicate they test only success scenarios.
```

## 🔧 Common Patterns

### Service Availability Check
```go
func isServiceAvailable(serviceName, healthEndpoint string) bool {
    client := &http.Client{Timeout: 5 * time.Second}
    resp, err := client.Get(healthEndpoint)
    if err != nil {
        return false
    }
    defer resp.Body.Close()
    return resp.StatusCode == http.StatusOK
}

// Usage
if !isServiceAvailable("onramp", "http://onramp:8081/readyz") {
    t.Skip("Service not available - skipping integration test")
}
```

**Rule**: Always check service availability before running tests and skip gracefully if services are not available.

### Test Data Naming Patterns
```go
// ✅ CORRECT - Use consistent naming patterns for easy identification and cleanup
const testDataPrefix = "TEST_"

// Generate unique identifiers to avoid conflicts
deviceName := fmt.Sprintf("%sDevice_%s", testDataPrefix, uuid.New().String()[:8])
userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String()[:8])
resourceID := uuid.New()

// Use descriptive names for different test scenarios
orgName := fmt.Sprintf("%sOrg_%s_%s", testDataPrefix, testScenario, uuid.New().String()[:8])
```

**Rule**: Use consistent naming patterns with `TEST_` prefix and unique identifiers for easy identification and cleanup.

### Database Investigation Patterns
```go
// ✅ CORRECT - Investigate API implementation before writing tests
func investigateAPIRequirements(t *testing.T) {
    t.Helper()
    
    // 1. Check API implementation for required fields
    // 2. Verify soft delete vs hard delete behavior
    // 3. Check foreign key constraints
    // 4. Understand data flow and dependencies
    
    connections := getTestDatabaseConnection(t)
    defer connections.Close()

    // Example: Check if organization exists and is not deleted
    orgQuery := `SELECT 1 FROM {{Organization}} WHERE Id = $1 AND NOT IsDeleted`
    var orgExists struct { Exists int `db:"?column?"` }
    err := connections.Postgres.QueryRowStruct(&orgExists, orgQuery, testOrgID)
    if err != nil {
        t.Skip("Required master data not available - skipping integration test")
    }
}
```

**Rule**: Always investigate the API implementation and database schema before writing tests to understand requirements and constraints.

### Test Data Independence - CRITICAL
```go
// 🚨 CRITICAL: Each test must be completely independent
// No test should affect master data or other tests

// ✅ CORRECT - Test data independence
func TestResourceOperation_Success(t *testing.T) {
    t.Parallel()

    // 1. Check service availability
    if !isServiceAvailable("onramp", "http://onramp:8081/readyz") {
        t.Skip("Service not available - skipping integration test")
    }
    
    // 2. Verify required master data exists
    verifyTestDataExists(t)
    
    // 3. Generate unique test identifier for this specific test run
    testID := uuid.New().String()[:8]
    
    // 4. Pre-test cleanup - ensure clean state for this specific test
    cleanupTestDataForTest(t, testOrgID, testID)
    verifyCleanTestState(t, testOrgID, testID)

    // 5. Generate unique test data with consistent naming
    const testDataPrefix = "TEST_"
    deviceName := fmt.Sprintf("%s%s_Device_%s", testDataPrefix, testID, uuid.New().String()[:8])
    
    // 6. Track ALL created resources for cleanup
    var createdResources []uuid.UUID
    
    // 7. Run test with unique data
    // ... test execution ...
    
    // 8. Post-test verification and cleanup
    t.Cleanup(func() {
        // Cleanup using same method as data creation (API or direct DB)
        for _, resourceID := range createdResources {
            cleanupTestDataViaAPI(t, client, sessionCookie, resourceID)
        }
        
        // Verify cleanup was successful
        for _, resourceID := range createdResources {
            verifyTestDataCleanup(t, resourceID)
        }
        
        // Final cleanup sweep for this specific test
        cleanupTestDataForTest(t, testOrgID, testID)
        verifyTestCleanupComplete(t, testOrgID, testID)
    })
}
```

**Rule**: Each test must be completely independent - no test should affect master data or other tests.

### Test Data Verification Patterns
```go
// Pre-test verification - ensure clean state
func verifyCleanTestState(t *testing.T, organizationID uuid.UUID) {
    t.Helper()
    connections := getTestDatabaseConnection(t)
    defer connections.Close()
    
    // Check no test data exists
    query := `SELECT COUNT(*) FROM {{Resource}} WHERE organizationid = $1 AND name LIKE 'TEST_%' AND isdeleted = false`
    var count int
    err := connections.Postgres.QueryRowStruct(&count, query, organizationID)
    require.NoError(t, err)
    assert.Equal(t, 0, count, "Test environment should be clean before test starts")
}

// Post-test verification - ensure cleanup was successful
func verifyTestCleanupComplete(t *testing.T, organizationID uuid.UUID) {
    t.Helper()
    connections := getTestDatabaseConnection(t)
    defer connections.Close()
    
    // Check no test data remains
    query := `SELECT COUNT(*) FROM {{Resource}} WHERE organizationid = $1 AND name LIKE 'TEST_%' AND isdeleted = false`
    var count int
    err := connections.Postgres.QueryRowStruct(&count, query, organizationID)
    require.NoError(t, err)
    assert.Equal(t, 0, count, "All test data should be cleaned up after test")
    
    // Log any remaining test data for debugging
    if count > 0 {
        query := `SELECT id, name FROM {{Resource}} WHERE organizationid = $1 AND name LIKE 'TEST_%' AND isdeleted = false`
        var resources []struct {
            ID   uuid.UUID `db:"id"`
            Name string    `db:"name"`
        }
        connections.Postgres.QueryGenericSlice(&resources, query, organizationID)
        t.Logf("Remaining test data: %+v", resources)
    }
}

// Master data protection - verify master data was not affected
func verifyMasterDataIntegrity(t *testing.T, masterDataIDs []uuid.UUID) {
    t.Helper()
    connections := getTestDatabaseConnection(t)
    defer connections.Close()
    
    for _, id := range masterDataIDs {
        query := `SELECT COUNT(*) FROM {{MasterTable}} WHERE id = $1 AND isdeleted = false`
        var count int
        err := connections.Postgres.QueryRowStruct(&count, query, id)
        require.NoError(t, err)
        assert.Equal(t, 1, count, "Master data should not be affected by tests")
    }
}
```

**Rule**: Always verify clean state before tests and successful cleanup after tests.

### Database Connection Pattern
```go
func getTestDatabaseConnection(t *testing.T) *connect.Connections {
    t.Helper()
    ctx := context.Background() // No timeout, no immediate cancellation
    connections := &connect.Connections{}
    postgresExecutor, err := connect.Postgres(ctx, nil)
    require.NoError(t, err, "Should be able to create database connection")
    connections.Postgres = postgresExecutor
    return connections
}
```

### Database Cleanup Pattern
```go
    t.Cleanup(func() {
    pg, err := connect.Postgres(ctx, nil)
            if err != nil {
        t.Logf("Warning: Failed to create database connection for cleanup: %v", err)
        return
    }
    defer pg.Close()
    
    // Cleanup operations here
    if len(createdResources) > 0 {
        deleteQuery := `DELETE FROM {{TableName}} WHERE id = ANY($1)`
        _, err := pg.Exec(deleteQuery, createdResources)
        if err != nil {
            t.Logf("Warning: Failed to cleanup test resources: %v", err)
        }
    }
})
```

### API Request Logging Pattern
```go
t.Logf("Request URL: %s", url)
requestJSON, _ := json.Marshal(requestData)
t.Logf("Request Body: %s", string(requestJSON))

resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, requestData)

t.Logf("Response Status: %d", resp.StatusCode)
body, _ := io.ReadAll(resp.Body)
t.Logf("Response Body: %s", string(body))
resp.Body = io.NopCloser(bytes.NewReader(body)) // Reset body for parsing
```

### Response Validation Pattern
```go
assert.Equal(t, http.StatusOK, resp.StatusCode)

var responseStruct ResponseStruct
apiResp := parseAPIResponse(t, resp, &responseStruct)
assert.Equal(t, "success", apiResp.Status)
assert.Equal(t, 200, apiResp.Code)
assert.Equal(t, "Request Succeeded", apiResp.Message)

// Verify specific response fields
assert.NotEmpty(t, responseStruct.ID, "Should return ID")
assert.Equal(t, "expected_value", responseStruct.Field, "Should return correct field value")
```

## 🚨 Common Issues & Solutions

### Issue: "sql: database is closed" warnings
**Cause**: Database connection closed before cleanup runs
**Solution**: Create fresh database connections in cleanup functions
```go
t.Cleanup(func() {
    pg, err := connect.Postgres(ctx, nil) // Fresh connection
    if err != nil {
        t.Logf("Warning: Failed to create database connection for cleanup: %v", err)
        return
    }
    defer pg.Close()
    // Cleanup operations
})
```

### Issue: "context canceled" errors
**Cause**: Using `defer cancel()` immediately cancels context
**Solution**: Use `ctx := context.Background()`
```go
// ❌ WRONG
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()

// ✅ CORRECT
ctx := context.Background()
```

### Issue: 500 Internal Server Error
**Cause**: Null values in API requests
**Solution**: Provide explicit values for all fields
```go
// ❌ WRONG
request := UpdateRequest{
    Name: stringPtr("Test"),
    Description: nil, // 500 error
}

// ✅ CORRECT
request := UpdateRequest{
    Name: stringPtr("Test"),
    Description: stringPtr("Test Description"),
}
```

### Issue: 400 Bad Request
**Cause**: Invalid request data or missing required fields
**Solution**: Check API documentation and provide all required fields with correct types

### Issue: 401 Unauthorized
**Cause**: Missing authentication or wrong endpoint
**Solution**: Use `makeAuthenticatedRequest` for protected endpoints or check if endpoint is public

### Issue: Race conditions in parallel tests
**Cause**: Tests modifying shared data
**Solution**: Use test-specific data for CREATE/UPDATE/DELETE operations

## 📋 Quick Checklist

- [ ] **🎯 SUCCESS ONLY** - Only test success cases, no error scenarios or edge cases
- [ ] **📋 Naming** - All test functions must end with `_Success`
- [ ] Use `{{TableName}}` for all database queries
- [ ] Use `ctx := context.Background()` (no defer cancel)
- [ ] Create fresh DB connections in cleanup functions
- [ ] Use shared data for READ operations
- [ ] Create test data for CREATE/UPDATE/DELETE operations
- [ ] Provide explicit values for all API request fields
- [ ] Log request/response details for debugging
- [ ] Verify SUCCESS response status (200 OK) and structure
- [ ] Clean up created resources
- [ ] Use existing API structs when possible
- [ ] Follow the standard success test structure template
- [ ] Investigate API implementation before writing tests
- [ ] Use consistent naming patterns with `TEST_` prefix
- [ ] Database connections use environment credentials automatically
- [ ] Check service availability before running tests
- [ ] Verify clean state before tests start
- [ ] Verify cleanup was successful after tests
- [ ] Use test-specific identifiers for complete data isolation
- [ ] Place shared helper functions in `utils.go`
- [ ] Never modify master data - only use existing master data
- [ ] **NEVER skip tests** - always create data needed for the test

## 🌐 Service Endpoints and Environment

### Common Service Endpoints
- **Onramp API**: `http://onramp:8080/api`
- **Onramp Health**: `http://onramp:8081/readyz`
- **Postgres**: `postgres:5432`
- **Redis**: `redis:6379`

### Test User Credentials
- **Email**: `<EMAIL>`
- **Password**: `puppies1234`
- **Permissions**: Full `syn_admin` permissions
- **Organization**: Synapse organization (ID: `55d832bf-cd0d-5c8d-b06c-c0f73ae8b7cd`)

### API Base URL
- **Onramp Service**: `http://onramp:8080/api`
- **Login Endpoint**: `http://onramp:8080/login`

### Environment Configuration
```go
// ✅ CORRECT - Environment credentials are automatically loaded
// No need to manually specify connection strings
// The connect.Postgres(ctx, nil) function handles:
// - Database host and port from environment
// - Username and password from environment
// - Database name from environment
// - SSL settings from environment

connections := getTestDatabaseConnection(t)
defer connections.Close()
```

**Rule**: Database connections automatically use environment credentials. No manual configuration needed.

## 🛠️ Helper Functions

### Shared Utilities in utils.go
All shared helper functions should be placed in `utils.go` to promote code reuse across test files:

```go
// Pointer helpers
func stringPtr(s string) *string { return &s }
func intPtr(i int) *int { return &i }
func boolPtr(b bool) *bool { return &b }
func uuidPtr(u uuid.UUID) *uuid.UUID { return &u }

// Service availability check
func isServiceAvailable(serviceName, healthEndpoint string) bool {
    client := &http.Client{Timeout: 5 * time.Second}
    resp, err := client.Get(healthEndpoint)
    if err != nil {
        return false
    }
    defer resp.Body.Close()
    return resp.StatusCode == http.StatusOK
}

// Test data verification
func verifyTestDataExists(t *testing.T) {
    t.Helper()
    connections := getTestDatabaseConnection(t)
    defer connections.Close()
    
    // Check required entities exist
    orgQuery := `SELECT 1 FROM {{Organization}} WHERE Id = $1 AND NOT IsDeleted`
    var orgExists struct { Exists int `db:"?column?"` }
    err := connections.Postgres.QueryRowStruct(&orgExists, orgQuery, testOrgID)
    if err != nil {
        t.Skip("Test data not available - skipping integration test")
    }
}

// HTTP request helper
func makeAuthenticatedRequest(t *testing.T, client *http.Client, sessionCookie *http.Cookie, method, url string, body interface{}) *http.Response {
    t.Helper()
    
    var bodyReader io.Reader
    if body != nil {
        bodyBytes, err := json.Marshal(body)
        require.NoError(t, err, "Should be able to marshal request body")
        bodyReader = bytes.NewReader(bodyBytes)
    }
    
    req, err := http.NewRequest(method, url, bodyReader)
    require.NoError(t, err, "Should be able to create request")
    
    if body != nil {
        req.Header.Set("Content-Type", "application/json")
    }
    req.AddCookie(sessionCookie)
    
    resp, err := client.Do(req)
    require.NoError(t, err, "Should be able to make request")
    
    return resp
}

// API response parsing
func parseAPIResponse(t *testing.T, resp *http.Response, target interface{}) *response.SuccessResponse {
    t.Helper()
    
    body, err := io.ReadAll(resp.Body)
    require.NoError(t, err, "Should be able to read response body")
    defer resp.Body.Close()
    
    var apiResp response.SuccessResponse
    err = json.Unmarshal(body, &apiResp)
    require.NoError(t, err, "Should be able to parse API response")
    
    if target != nil {
        if apiResp.Data != nil {
            dataBytes, err := json.Marshal(apiResp.Data)
            require.NoError(t, err, "Should be able to marshal data field")
            
            err = json.Unmarshal(dataBytes, target)
            require.NoError(t, err, "Should be able to unmarshal data into target")
        }
    }
    
    return &apiResp
}
```

**Rule**: Place all shared helper functions in `utils.go` for code reuse across test files.

## 🎯 Key Principles

1. **🎯 SUCCESS ONLY**: Only test success cases in integration tests - no error scenarios, validation failures, or edge cases
2. **🚨 Context**: Never cancel context immediately after creation
3. **🚨 Fields**: Always provide explicit values for optional fields, never `null`
4. **🚨 Independence**: Each test must be completely independent - no test should affect master data or other tests
5. **🚀 Parallel**: Use `t.Parallel()` for integration tests with proper data isolation
6. **🔄 Unique**: Use UUID-based unique identifiers for test data with test-specific prefixes
7. **✅ Available**: Check service availability before running tests
8. **🧹 Clean**: Always clean up test data after tests AND verify cleanup
9. **📝 Log**: Include comprehensive request/response logging
10. **🔍 Verify**: Verify test data exists before running tests
11. **🏗️ Structure**: Follow the standard success test structure template
12. **🔗 Reuse**: Reuse database connections within tests
13. **⚡ Skip**: Skip tests gracefully when prerequisites are not met
14. **🛡️ Protect**: Never modify master data - only use existing master data
15. **🔬 Investigate**: Always investigate API implementation before writing tests
16. **✅ Verify**: Verify cleanup was successful after each test
17. **📁 Utils**: Place all shared helper functions in `utils.go` for code reuse
18. **🔒 Isolate**: Use test-specific identifiers to ensure complete data isolation
19. **📋 Naming**: All test functions must end with `_Success` to indicate success-only testing

## 🎯 Key Lessons Learned

1. **Database connections must be fresh in cleanup** - prevents "database is closed" errors
2. **Context cancellation causes race conditions** - use background context
3. **Null values cause 500 errors** - always provide explicit values
4. **Hardcoded table names break template resolution** - use `{{TableName}}` syntax
5. **Shared data for READ, test data for mutations** - prevents race conditions
6. **Comprehensive logging helps debugging** - log requests and responses
7. **Use existing API structs** - prevents duplication and maintains consistency
8. **Always investigate API implementation first** - prevents test failures due to incorrect assumptions
9. **Use consistent naming patterns** - makes test data easy to identify and cleanup
10. **Test data independence is critical** - each test must be completely independent
11. **Always verify cleanup** - verify clean state before and after tests
12. **Service availability matters** - check services before running tests