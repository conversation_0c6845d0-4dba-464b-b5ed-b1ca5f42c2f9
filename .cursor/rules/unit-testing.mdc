---
description: Use this guideline when you do the task related to unit test
alwaysApply: false
---
# Unit Testing Guidelines

## Required Testing Framework

**ALWAYS use testify/assert for all test assertions.** Do not use Go's built-in testing package assertions or any other assertion libraries.

```go
import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
)
```

All test assertions must use testify assert methods:
- `assert.Equal()` - for equality checks
- `assert.NoError()` - for no error expectations
- `assert.Error()` - for error expectations
- `assert.Contains()` - for substring/element checks
- `assert.Len()` - for length assertions
- `assert.AnError` - for mock error expectations

## Table-Driven Testing Pattern

Always use table-driven testing for comprehensive test coverage. Structure your tests as follows:

```go
func TestFunctionName(t *testing.T) {
    tests := []struct {
        name           string
        input          InputType
        setupMock      func(*MockInterface)
        expectedResult ExpectedType
        expectedError  error
    }{
        {
            name:  "success_case",
            input: validInput,
            setupMock: func(m *MockInterface) {
                m.On("MethodName", mock.Anything).Return(expectedResult, nil)
            },
            expectedResult: expectedValue,
            expectedError:  nil,
        },
        {
            name:  "error_case",
            input: invalidInput,
            setupMock: func(m *MockInterface) {
                m.On("MethodName", mock.Anything).Return(nil, assert.AnError)
            },
            expectedResult: nil,
            expectedError:  assert.AnError,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Setup
            mockObj := new(MockInterface)
            tt.setupMock(mockObj)
            
            // Execute
            result, err := functionUnderTest(tt.input)
            
            // Assert
            if tt.expectedError != nil {
                assert.Error(t, err)
                assert.Equal(t, tt.expectedError, err)
            } else {
                assert.NoError(t, err)
                assert.Equal(t, tt.expectedResult, result)
            }
            
            // Verify mock expectations
            mockObj.AssertExpectations(t)
        })
    }
}
```

## Mock Implementation Patterns

### Creating Mocks for Interfaces
Use testify/mock to implement interface mocks:

```go
// mock_interface_name.go
package mock

import (
    "github.com/stretchr/testify/mock"
    "synapse-its.com/your/domain"
)

type MockInterfaceName struct {
    mock.Mock
}

func (m *MockInterfaceName) MethodName(param domain.Type) (domain.Result, error) {
    args := m.Called(param)
    if result, ok := args.Get(0).(domain.Result); ok {
        return result, args.Error(1)
    }
    return nil, args.Error(1)
}
```

### Repository Mock Pattern
**ALWAYS create repository mocks in the `@mocks/` directory with naming convention matching the package name.**

Repository mocks should be placed in `shared/rest/domain/mocks/` and follow this pattern:

```go
// shared/rest/domain/mocks/auth_repository.go
package mocks

import (
    "context"
    "github.com/google/uuid"
    "github.com/stretchr/testify/mock"
    authDomain "synapse-its.com/shared/rest/domain/auth"
)

// MockAuthRepository is a mock implementation of the auth.AuthRepository interface
type MockAuthRepository struct {
    mock.Mock
}

func (m *MockAuthRepository) GetByUsername(ctx context.Context, username string) (*authDomain.User, *authDomain.AuthMethod, error) {
    args := m.Called(ctx, username)
    if args.Get(0) == nil {
        return nil, nil, args.Error(2)
    }
    user := args.Get(0).(*authDomain.User)
    var authMethod *authDomain.AuthMethod
    if args.Get(1) != nil {
        authMethod = args.Get(1).(*authDomain.AuthMethod)
    }
    return user, authMethod, args.Error(2)
}

// ... implement all other interface methods
```

**Naming Convention:**
- File name: `{package_name}_repository.go` (e.g., `auth_repository.go`, `user_repository.go`)
- Type name: `Mock{PackageName}Repository` (e.g., `MockAuthRepository`, `MockUserRepository`)
- Package: `mocks`

**Usage in Tests:**
```go
import (
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    domainMocks "synapse-its.com/shared/rest/domain/mocks"
)

func TestHandler(t *testing.T) {
    mockRepo := &domainMocks.MockAuthRepository{}
    mockRepo.On("GetByUsername", mock.Anything, "testuser").Return(user, authMethod, nil)
    
    // Test implementation
    mockRepo.AssertExpectations(t)
}
```

**Import Alias Convention:**
- Use `domainMocks` as the alias for `synapse-its.com/shared/rest/domain/mocks` to avoid conflicts with testify/mock
- This prevents naming conflicts and makes the code more readable

### Mock Expectations
Use `mock.Anything` for UUID and dynamic parameters:

```go
// For UUID parameters
m.On("MethodName", mock.Anything).Return(result, nil)

// For multiple parameters
m.On("MethodName", mock.Anything, mock.Anything, mock.Anything).Return(result, nil)

// For specific values
m.On("MethodName", specificValue).Return(result, nil)
```

### Import Naming Convention
Always rename custom mock imports to avoid conflicts with testify/mock:

```go
import (
    "github.com/stretchr/testify/mock"
    "github.com/stretchr/testify/assert"
    onrampMock "synapse-its.com/onramp/mock"  // Use onrampMock alias
)
```

## HTTP Handler Testing

### Request Setup
```go
// Create request with context and parameters
req := httptest.NewRequest(http.MethodGet, "/path", nil)
ctx := context.WithValue(req.Context(), ContextKey{}, value)
req = req.WithContext(ctx)

// Set URL parameters for gorilla/mux
vars := map[string]string{"paramName": "value"}
req = mux.SetURLVars(req, vars)
```

### Response Assertions
For JSON responses, use partial matching:

```go
// For success responses
assert.Contains(t, rr.Body.String(), `"data":`)

// For error responses  
assert.Contains(t, rr.Body.String(), `"status":"error"`)

// For specific data
assert.Contains(t, rr.Body.String(), `"data":null`)
```

## Database Testing

### Using FakeDBExecutor
For database operations, use the shared `FakeDBExecutor`:

```go
import "synapse-its.com/shared/mocks"

func TestDatabaseFunction(t *testing.T) {
    fakeDB := &mocks.FakeDBExecutor{}
    fakeDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
        return &customSQLResult{}, nil
    }
    
    storage := &storage{db: fakeDB}
    // Test implementation
}
```
You can find the fake for connection in [mocks package](mdc:/shared/mocks)

### Custom SQL Result Mocks
Create custom SQL result implementations for specific test scenarios:

```go
type customSQLResult struct {
    rowsAffected int64
}

func (c *customSQLResult) LastInsertId() (int64, error) {
    return 0, nil
}

func (c *customSQLResult) RowsAffected() (int64, error) {
    return c.rowsAffected, nil
}

// For error scenarios
type errorSQLResult struct{}

func (e *errorSQLResult) RowsAffected() (int64, error) {
    return 0, errors.New("database error")
}
```

## Test Coverage Goals
- Aim for 100% coverage
- Test both success and error paths
- Test edge cases and boundary conditions
- Test all public methods and functions

## Common Patterns

### Dynamic Object Comparison
When comparing objects with generated UUIDs, compare content instead of exact objects:

```go
// Instead of exact comparison
assert.Equal(t, tt.expectedResult, result)

// Use content comparison
if tt.expectedResult != nil {
    assert.Len(t, result, len(tt.expectedResult))
    for i, expected := range tt.expectedResult {
        if i < len(result) {
            assert.Equal(t, expected.Field1, result[i].Field1)
            assert.Equal(t, expected.Field2, result[i].Field2)
        }
    }
}
```

### Error Testing
Always test error scenarios:

```go
{
    name: "error_case",
    setupMock: func(m *MockInterface) {
        m.On("MethodName", mock.Anything).Return(nil, assert.AnError)
    },
    expectedError: assert.AnError,
}
```
