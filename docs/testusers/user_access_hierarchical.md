# User Access and Testing Assignments (Hierarchical)

---

## User: Admin User

- **user_id:** 550e8400-e29b-41d4-a716-446655440001
- **user_orig_id:** 9001
- **user_first_name:** Admin
- **user_last_name:** User
- **user_mobile:** ******-555-1001
- **user_notification_sms_enabled:** false
- **user_iana_timezone:** America/Chicago
- **user_description:** Test admin user with full permissions
- **user_is_deleted:** false

### Auth Methods
- **authmethod_id:** 550e8400-e29b-41d4-a716-446655440101
  - **authmethod_user_id:** 550e8400-e29b-41d4-a716-446655440001
  - **authmethod_type:** USERNAME_PASSWORD
  - **authmethod_username:** <EMAIL>
  - **authmethod_email:** <EMAIL>
  - **authmethod_password:** puppies1234
  - **memberships:**
    - **membership_id:** 550e8400-e29b-41d4-a716-446655440201
      - **membership_authmethod_id:** 550e8400-e29b-41d4-a716-446655440101
      - **membership_organization_id:** 550e8400-e29b-41d4-a716-446655440010
      - **organization_name:** Test Organization 1
      - **org_role_assignments:**
        - **org_role_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440201
          - **org_role_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440301
          - **custom_role_name:** Test Org 1 Admin
          - **custom_role_description:** Admin role for test organization 1
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440201
          - **device_group_assignment_device_group_id:** 550e8400-e29b-41d4-a716-446655440030
          - **device_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440301
          - **device_group_name:** Test Device Group 1
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440201
          - **location_group_assignment_location_group_id:** 550e8400-e29b-41d4-a716-446655440050
          - **location_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440301
          - **location_group_name:** Test Location Group 1
    - **membership_id:** 550e8400-e29b-41d4-a716-446655440202
      - **membership_authmethod_id:** 550e8400-e29b-41d4-a716-446655440101
      - **membership_organization_id:** 550e8400-e29b-41d4-a716-446655440020
      - **organization_name:** Test Organization 2
      - **org_role_assignments:**
        - **org_role_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440202
          - **org_role_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440302
          - **custom_role_name:** Test Org 2 Admin
          - **custom_role_description:** Admin role for test organization 2
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440202
          - **device_group_assignment_device_group_id:** 550e8400-e29b-41d4-a716-446655440040
          - **device_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440302
          - **device_group_name:** Test Device Group 2
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440202
          - **location_group_assignment_location_group_id:** 550e8400-e29b-41d4-a716-446655440060
          - **location_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440302
          - **location_group_name:** Test Location Group 2

---

## User: Manager User

- **user_id:** 550e8400-e29b-41d4-a716-446655440002
- **user_orig_id:** 9002
- **user_first_name:** Manager
- **user_last_name:** User
- **user_mobile:** ******-555-1002
- **user_notification_sms_enabled:** false
- **user_iana_timezone:** America/Chicago
- **user_description:** Test manager user with limited permissions
- **user_is_deleted:** false

### Auth Methods
- **authmethod_id:** 550e8400-e29b-41d4-a716-446655440102
  - **authmethod_user_id:** 550e8400-e29b-41d4-a716-446655440002
  - **authmethod_type:** USERNAME_PASSWORD
  - **authmethod_username:** <EMAIL>
  - **authmethod_email:** <EMAIL>
  - **authmethod_password:** puppies1234
  - **memberships:**
    - **membership_id:** 550e8400-e29b-41d4-a716-446655440203
      - **membership_authmethod_id:** 550e8400-e29b-41d4-a716-446655440102
      - **membership_organization_id:** 550e8400-e29b-41d4-a716-446655440010
      - **organization_name:** Test Organization 1
      - **org_role_assignments:**
        - **org_role_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440203
          - **org_role_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440303
          - **custom_role_name:** Test Org 1 Manager
          - **custom_role_description:** Manager role for test organization 1
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440203
          - **device_group_assignment_device_group_id:** 550e8400-e29b-41d4-a716-446655440030
          - **device_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440303
          - **device_group_name:** Test Device Group 1
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440203
          - **location_group_assignment_location_group_id:** 550e8400-e29b-41d4-a716-446655440050
          - **location_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440303
          - **location_group_name:** Test Location Group 1

---

## User: Technician User

- **user_id:** 550e8400-e29b-41d4-a716-446655440003
- **user_orig_id:** 9003
- **user_first_name:** Technician
- **user_last_name:** User
- **user_mobile:** ******-555-1003
- **user_notification_sms_enabled:** false
- **user_iana_timezone:** America/Chicago
- **user_description:** Test technician user with device-only permissions
- **user_is_deleted:** false

### Auth Methods
- **authmethod_id:** 550e8400-e29b-41d4-a716-446655440103
  - **authmethod_user_id:** 550e8400-e29b-41d4-a716-446655440003
  - **authmethod_type:** USERNAME_PASSWORD
  - **authmethod_username:** <EMAIL>
  - **authmethod_email:** <EMAIL>
  - **authmethod_password:** puppies1234
  - **memberships:**
    - **membership_id:** 550e8400-e29b-41d4-a716-446655440204
      - **membership_authmethod_id:** 550e8400-e29b-41d4-a716-446655440103
      - **membership_organization_id:** 550e8400-e29b-41d4-a716-446655440010
      - **organization_name:** Test Organization 1
      - **org_role_assignments:**
        - **org_role_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440204
          - **org_role_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440305
          - **custom_role_name:** Test Org 1 Technician
          - **custom_role_description:** Technician role for test organization 1
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440204
          - **device_group_assignment_device_group_id:** 550e8400-e29b-41d4-a716-446655440030
          - **device_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440305
          - **device_group_name:** Test Device Group 1
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440204
          - **location_group_assignment_location_group_id:** 550e8400-e29b-41d4-a716-446655440050
          - **location_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440305
          - **location_group_name:** Test Location Group 1

---

## User: Custom User

- **user_id:** 550e8400-e29b-41d4-a716-446655440004
- **user_orig_id:** 9004
- **user_first_name:** Custom
- **user_last_name:** User
- **user_mobile:** ******-555-1004
- **user_notification_sms_enabled:** false
- **user_iana_timezone:** America/Chicago
- **user_description:** Test user with custom role permissions
- **user_is_deleted:** false

### Auth Methods
- **authmethod_id:** 550e8400-e29b-41d4-a716-446655440104
  - **authmethod_user_id:** 550e8400-e29b-41d4-a716-446655440004
  - **authmethod_type:** USERNAME_PASSWORD
  - **authmethod_username:** <EMAIL>
  - **authmethod_email:** <EMAIL>
  - **authmethod_password:** puppies1234
  - **memberships:**
    - **membership_id:** 550e8400-e29b-41d4-a716-446655440205
      - **membership_authmethod_id:** 550e8400-e29b-41d4-a716-446655440104
      - **membership_organization_id:** 550e8400-e29b-41d4-a716-446655440010
      - **organization_name:** Test Organization 1
      - **org_role_assignments:**
        - **org_role_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440205
          - **org_role_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440307
          - **custom_role_name:** Test Custom Role
          - **custom_role_description:** Custom role with permission overrides
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440205
          - **device_group_assignment_device_group_id:** 550e8400-e29b-41d4-a716-446655440030
          - **device_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440307
          - **device_group_name:** Test Device Group 1
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440205
          - **location_group_assignment_location_group_id:** 550e8400-e29b-41d4-a716-446655440050
          - **location_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440307
          - **location_group_name:** Test Location Group 1

---

## User: MultiOrg User

- **user_id:** 550e8400-e29b-41d4-a716-446655440005
- **user_orig_id:** 9005
- **user_first_name:** MultiOrg
- **user_last_name:** User
- **user_mobile:** ******-555-1005
- **user_notification_sms_enabled:** false
- **user_iana_timezone:** America/Chicago
- **user_description:** Test user with multiple organization access
- **user_is_deleted:** false

### Auth Methods
- **authmethod_id:** 550e8400-e29b-41d4-a716-446655440105
  - **authmethod_user_id:** 550e8400-e29b-41d4-a716-446655440005
  - **authmethod_type:** USERNAME_PASSWORD
  - **authmethod_username:** <EMAIL>
  - **authmethod_email:** <EMAIL>
  - **authmethod_password:** puppies1234
  - **memberships:**
    - **membership_id:** 550e8400-e29b-41d4-a716-446655440206
      - **membership_authmethod_id:** 550e8400-e29b-41d4-a716-446655440105
      - **membership_organization_id:** 550e8400-e29b-41d4-a716-446655440010
      - **organization_name:** Test Organization 1
      - **org_role_assignments:**
        - **org_role_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440206
          - **org_role_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440303
          - **custom_role_name:** Test Org 1 Manager
          - **custom_role_description:** Manager role for test organization 1
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440206
          - **device_group_assignment_device_group_id:** 550e8400-e29b-41d4-a716-446655440030
          - **device_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440303
          - **device_group_name:** Test Device Group 1
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440206
          - **location_group_assignment_location_group_id:** 550e8400-e29b-41d4-a716-446655440050
          - **location_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440303
          - **location_group_name:** Test Location Group 1
    - **membership_id:** 550e8400-e29b-41d4-a716-446655440207
      - **membership_authmethod_id:** 550e8400-e29b-41d4-a716-446655440105
      - **membership_organization_id:** 550e8400-e29b-41d4-a716-446655440020
      - **organization_name:** Test Organization 2
      - **org_role_assignments:**
        - *(none)*
      - **device_group_assignments:**
        - *(none)*
      - **location_group_assignments:**
        - *(none)*

---

## User: DeviceOnly User

- **user_id:** 550e8400-e29b-41d4-a716-446655440008
- **user_orig_id:** 9008
- **user_first_name:** DeviceOnly
- **user_last_name:** User
- **user_mobile:** ******-555-1008
- **user_notification_sms_enabled:** false
- **user_iana_timezone:** America/Chicago
- **user_description:** Test user with device group permissions only
- **user_is_deleted:** false

### Auth Methods
- **authmethod_id:** 550e8400-e29b-41d4-a716-446655440108
  - **authmethod_user_id:** 550e8400-e29b-41d4-a716-446655440008
  - **authmethod_type:** USERNAME_PASSWORD
  - **authmethod_username:** <EMAIL>
  - **authmethod_email:** <EMAIL>
  - **authmethod_password:** puppies1234
  - **memberships:**
    - **membership_id:** 550e8400-e29b-41d4-a716-446655440210
      - **membership_authmethod_id:** 550e8400-e29b-41d4-a716-446655440108
      - **membership_organization_id:** 550e8400-e29b-41d4-a716-446655440010
      - **organization_name:** Test Organization 1
      - **custom_roles:**
        - **custom_role_name:** Test Org 1 Technician
          - **custom_role_description:** Technician role for test organization 1
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440210
          - **device_group_assignment_device_group_id:** 550e8400-e29b-41d4-a716-446655440030
          - **device_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440305
          - **device_group_name:** Test Device Group 1
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440210
          - **location_group_assignment_location_group_id:** 550e8400-e29b-41d4-a716-446655440050
          - **location_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440305
          - **location_group_name:** Test Location Group 1
    - **membership_id:** 550e8400-e29b-41d4-a716-446655440211
      - **membership_authmethod_id:** 550e8400-e29b-41d4-a716-446655440108
      - **membership_organization_id:** 550e8400-e29b-41d4-a716-446655440020
      - **organization_name:** Test Organization 2
      - **custom_roles:**
        - **custom_role_name:** Test Org 2 Technician
          - **custom_role_description:** Technician role for test organization 2
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440211
          - **device_group_assignment_device_group_id:** 550e8400-e29b-41d4-a716-446655440040
          - **device_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440306
          - **device_group_name:** Test Device Group 2
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** 550e8400-e29b-41d4-a716-446655440211
          - **location_group_assignment_location_group_id:** 550e8400-e29b-41d4-a716-446655440060
          - **location_group_assignment_role_id:** 550e8400-e29b-41d4-a716-446655440306
          - **location_group_name:** Test Location Group 2

---

## User: Test User

- **user_id:** 27c68173-289a-5e53-92ae-c598d847038c
- **user_orig_id:** 1
- **user_first_name:** Test
- **user_last_name:** User
- **user_mobile:** ******-555-1234
- **user_notification_sms_enabled:** true
- **user_iana_timezone:** America/Chicago
- **user_description:** Primary test user
- **user_is_deleted:** false

### Auth Methods
- **authmethod_id:** 8e66f828-87a8-5bd6-a670-d3d50d9b5d71
  - **authmethod_user_id:** 27c68173-289a-5e53-92ae-c598d847038c
  - **authmethod_type:** USERNAME_PASSWORD
  - **authmethod_username:** <EMAIL>
  - **authmethod_email:** <EMAIL>
  - **authmethod_password:** puppies1234
  - **memberships:**
    - **membership_id:** afa28992-47ed-504b-aa6e-3a89d4289480
      - **membership_authmethod_id:** 8e66f828-87a8-5bd6-a670-d3d50d9b5d71
      - **membership_organization_id:** b6098079-54e7-5507-9fb7-972fff600a91
      - **organization_name:** Cornelius's Organization
      - **custom_roles:**
        - **custom_role_name:** Cornelius Org Admin
          - **custom_role_description:** Admin role for Cornelius organization
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** afa28992-47ed-504b-aa6e-3a89d4289480
          - **device_group_assignment_device_group_id:** 8b938364-e83a-5869-9196-06531b47a232
          - **device_group_assignment_role_id:** 49998574-3c34-5649-9e33-f0a21abb0b1a
          - **device_group_name:** Cornelius Dev Devices
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** afa28992-47ed-504b-aa6e-3a89d4289480
          - **location_group_assignment_location_group_id:** 1c46889b-e0dd-55f8-9dc1-d9abc522ec93
          - **location_group_assignment_role_id:** 49998574-3c34-5649-9e33-f0a21abb0b1a
          - **location_group_name:** Cornelius Westheimer Corridor
        - **location_group_assignment_membership_id:** afa28992-47ed-504b-aa6e-3a89d4289480
          - **location_group_assignment_location_group_id:** f008fa20-a9a2-5965-a292-5c5e920a54ec
          - **location_group_assignment_role_id:** 49998574-3c34-5649-9e33-f0a21abb0b1a
          - **location_group_name:** Cornelius Lab Locations
    - **membership_id:** 7efa34f1-2172-5268-81ef-79e28b263270
      - **membership_authmethod_id:** 8e66f828-87a8-5bd6-a670-d3d50d9b5d71
      - **membership_organization_id:** 147c9e71-ce1a-559d-85f3-960e9180880c
      - **organization_name:** Tam's Organization
      - **custom_roles:**
        - **custom_role_name:** Tam Org Admin
          - **custom_role_description:** Admin role for Tam organization
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** 7efa34f1-2172-5268-81ef-79e28b263270
          - **device_group_assignment_device_group_id:** 1b64d93c-8651-5667-88b3-0fd55f448135
          - **device_group_assignment_role_id:** 310e2001-ffe5-5e0a-901c-eb4e5bdfc703
          - **device_group_name:** Tam Dev Devices
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** 7efa34f1-2172-5268-81ef-79e28b263270
          - **location_group_assignment_location_group_id:** 3d5ade42-c9fa-5c9a-ab5e-1a90999af413
          - **location_group_assignment_role_id:** 310e2001-ffe5-5e0a-901c-eb4e5bdfc703
          - **location_group_name:** Tam Dev Locations
    - **membership_id:** a5f0e7e4-4303-5f34-8942-a227b24b9846
      - **membership_authmethod_id:** 8e66f828-87a8-5bd6-a670-d3d50d9b5d71
      - **membership_organization_id:** e2074a21-5a79-5220-b46a-3e9864c268bc
      - **organization_name:** Duc's Organization
      - **custom_roles:**
        - **custom_role_name:** Duc Org Admin
          - **custom_role_description:** Admin role for Duc organization
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** a5f0e7e4-4303-5f34-8942-a227b24b9846
          - **device_group_assignment_device_group_id:** 269eb4a8-bca8-5e22-988d-dd04060bf6f6
          - **device_group_assignment_role_id:** 3244dd58-1a47-5f49-ac67-0b29edd76d04
          - **device_group_name:** Duc Dev Devices
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** a5f0e7e4-4303-5f34-8942-a227b24b9846
          - **location_group_assignment_location_group_id:** f9dec2d5-e37d-5718-8738-07d87b2faab9
          - **location_group_assignment_role_id:** 3244dd58-1a47-5f49-ac67-0b29edd76d04
          - **location_group_name:** Duc Dev Locations
    - **membership_id:** 8bee8548-5b49-5c04-8e59-05c8e880db85
      - **membership_authmethod_id:** 8e66f828-87a8-5bd6-a670-d3d50d9b5d71
      - **membership_organization_id:** 6c9095e3-178a-5760-89df-0cbb094470a7
      - **organization_name:** Minh's Organization
      - **custom_roles:**
        - **custom_role_name:** Minh Org Admin
          - **custom_role_description:** Admin role for Minh organization
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** 8bee8548-5b49-5c04-8e59-05c8e880db85
          - **device_group_assignment_device_group_id:** 45e4f1da-ba75-5702-9adc-2af8019fa5d4
          - **device_group_assignment_role_id:** e61dd50b-e2ed-5e3b-a3ae-a2964af04d56
          - **device_group_name:** Minh Dev Devices
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** 8bee8548-5b49-5c04-8e59-05c8e880db85
          - **location_group_assignment_location_group_id:** fc07d6e5-cfad-5bfa-8803-82b8053f5030
          - **location_group_assignment_role_id:** e61dd50b-e2ed-5e3b-a3ae-a2964af04d56
          - **location_group_name:** Minh Dev Locations

---

## User: Test1 User

- **user_id:** 063f2596-9dea-57f8-84aa-5d693c59f4e4
- **user_orig_id:** 2
- **user_first_name:** Test1
- **user_last_name:** User
- **user_mobile:** ******-555-1234
- **user_notification_sms_enabled:** false
- **user_iana_timezone:** America/Chicago
- **user_description:** Secondary test user
- **user_is_deleted:** false

### Auth Methods
- **authmethod_id:** e33d7d8d-635e-59d1-86a6-308867d87c50
  - **authmethod_user_id:** 063f2596-9dea-57f8-84aa-5d693c59f4e4
  - **authmethod_type:** USERNAME_PASSWORD
  - **authmethod_username:** <EMAIL>
  - **authmethod_email:** <EMAIL>
  - **authmethod_password:** puppies1234
  - **memberships:**
    - **membership_id:** 48dacacd-d9f1-568d-b86e-fe779fe50234
      - **membership_authmethod_id:** e33d7d8d-635e-59d1-86a6-308867d87c50
      - **membership_organization_id:** b6098079-54e7-5507-9fb7-972fff600a91
      - **organization_name:** Cornelius's Organization
      - **custom_roles:**
        - **custom_role_name:** Cornelius Org Admin
          - **custom_role_description:** Admin role for Cornelius organization
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** 48dacacd-d9f1-568d-b86e-fe779fe50234
          - **device_group_assignment_device_group_id:** 8b938364-e83a-5869-9196-06531b47a232
          - **device_group_assignment_role_id:** 49998574-3c34-5649-9e33-f0a21abb0b1a
          - **device_group_name:** Cornelius Dev Devices
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** 48dacacd-d9f1-568d-b86e-fe779fe50234
          - **location_group_assignment_location_group_id:** 1c46889b-e0dd-55f8-9dc1-d9abc522ec93
          - **location_group_assignment_role_id:** 49998574-3c34-5649-9e33-f0a21abb0b1a
          - **location_group_name:** Cornelius Westheimer Corridor
        - **location_group_assignment_membership_id:** 48dacacd-d9f1-568d-b86e-fe779fe50234
          - **location_group_assignment_location_group_id:** f008fa20-a9a2-5965-a292-5c5e920a54ec
          - **location_group_assignment_role_id:** 49998574-3c34-5649-9e33-f0a21abb0b1a
          - **location_group_name:** Cornelius Lab Locations
    - **membership_id:** 63485a6d-690b-539e-a33b-02a9b480e3d7
      - **membership_authmethod_id:** e33d7d8d-635e-59d1-86a6-308867d87c50
      - **membership_organization_id:** 147c9e71-ce1a-559d-85f3-960e9180880c
      - **organization_name:** Tam's Organization
      - **custom_roles:**
        - **custom_role_name:** Tam Org Admin
          - **custom_role_description:** Admin role for Tam organization
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** 63485a6d-690b-539e-a33b-02a9b480e3d7
          - **device_group_assignment_device_group_id:** 1b64d93c-8651-5667-88b3-0fd55f448135
          - **device_group_assignment_role_id:** 310e2001-ffe5-5e0a-901c-eb4e5bdfc703
          - **device_group_name:** Tam Dev Devices
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** 63485a6d-690b-539e-a33b-02a9b480e3d7
          - **location_group_assignment_location_group_id:** 3d5ade42-c9fa-5c9a-ab5e-1a90999af413
          - **location_group_assignment_role_id:** 310e2001-ffe5-5e0a-901c-eb4e5bdfc703
          - **location_group_name:** Tam Dev Locations
    - **membership_id:** ae6312ac-6cba-5a41-ad44-ef4e53c101ae
      - **membership_authmethod_id:** e33d7d8d-635e-59d1-86a6-308867d87c50
      - **membership_organization_id:** e2074a21-5a79-5220-b46a-3e9864c268bc
      - **organization_name:** Duc's Organization
      - **custom_roles:**
        - **custom_role_name:** Duc Org Admin
          - **custom_role_description:** Admin role for Duc organization
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** ae6312ac-6cba-5a41-ad44-ef4e53c101ae
          - **device_group_assignment_device_group_id:** 269eb4a8-bca8-5e22-988d-dd04060bf6f6
          - **device_group_assignment_role_id:** 3244dd58-1a47-5f49-ac67-0b29edd76d04
          - **device_group_name:** Duc Dev Devices
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** ae6312ac-6cba-5a41-ad44-ef4e53c101ae
          - **location_group_assignment_location_group_id:** f9dec2d5-e37d-5718-8738-07d87b2faab9
          - **location_group_assignment_role_id:** 3244dd58-1a47-5f49-ac67-0b29edd76d04
          - **location_group_name:** Duc Dev Locations
    - **membership_id:** 9e2a56fe-40b3-5f66-bdbd-72d04691e34a
      - **membership_authmethod_id:** e33d7d8d-635e-59d1-86a6-308867d87c50
      - **membership_organization_id:** 6c9095e3-178a-5760-89df-0cbb094470a7
      - **organization_name:** Minh's Organization
      - **custom_roles:**
        - **custom_role_name:** Minh Org Admin
          - **custom_role_description:** Admin role for Minh organization
      - **device_group_assignments:**
        - **device_group_assignment_membership_id:** 9e2a56fe-40b3-5f66-bdbd-72d04691e34a
          - **device_group_assignment_device_group_id:** 45e4f1da-ba75-5702-9adc-2af8019fa5d4
          - **device_group_assignment_role_id:** e61dd50b-e2ed-5e3b-a3ae-a2964af04d56
          - **device_group_name:** Minh Dev Devices
      - **location_group_assignments:**
        - **location_group_assignment_membership_id:** 9e2a56fe-40b3-5f66-bdbd-72d04691e34a
          - **location_group_assignment_location_group_id:** fc07d6e5-cfad-5bfa-8803-82b8053f5030
          - **location_group_assignment_role_id:** e61dd50b-e2ed-5e3b-a3ae-a2964af04d56
          - **location_group_name:** Minh Dev Locations

---

## User: Anonymous User

- **user_id:** 550e8400-e29b-41d4-a716-446655440006
- **user_orig_id:** 9006
- **user_first_name:** Anonymous
- **user_last_name:** User
- **user_mobile:** ******-555-1006
- **user_notification_sms_enabled:** false
- **user_iana_timezone:** America/Chicago
- **user_description:** Test user with anonymous role (no permissions)
- **user_is_deleted:** false

### Auth Methods
- **authmethod_id:** 550e8400-e29b-41d4-a716-446655440106
  - **authmethod_user_id:** 550e8400-e29b-41d4-a716-446655440006
  - **authmethod_type:** USERNAME_PASSWORD
  - **authmethod_username:** <EMAIL>
  - **authmethod_email:** <EMAIL>
  - **authmethod_password:** puppies1234
  - **memberships:**
    - **membership_id:** 550e8400-e29b-41d4-a716-446655440208
      - **membership_authmethod_id:** 550e8400-e29b-41d4-a716-446655440106
      - **membership_organization_id:** 550e8400-e29b-41d4-a716-446655440010
      - **organization_name:** Test Organization 1
      - **org_role_assignments:**
        - *(none)*
      - **device_group_assignments:**
        - *(none)*
      - **location_group_assignments:**
        - *(none)*

---

## User: Admin Synapse

- **user_id:** f5854028-dfd1-50fe-9e31-83f0393cb4fe
- **user_orig_id:** 9999
- **user_first_name:** Admin
- **user_last_name:** Synapse
- **user_mobile:** 
- **user_notification_sms_enabled:** false
- **user_iana_timezone:** America/Chicago
- **user_description:** Keycloak admin user for Synapse organization
- **user_is_deleted:** false

### Auth Methods
- **authmethod_id:** c08cd672-0728-5582-ac40-e1e2942ede86
  - **authmethod_user_id:** f5854028-dfd1-50fe-9e31-83f0393cb4fe
  - **authmethod_type:** OIDC
  - **authmethod_username:** 
  - **authmethod_email:** <EMAIL>
  - **authmethod_password:** puppies1234
  - **memberships:**
    - **membership_id:** ce989c5b-5abd-55bc-b0db-9de51f250581
      - **membership_authmethod_id:** c08cd672-0728-5582-ac40-e1e2942ede86
      - **membership_organization_id:** 55d832bf-cd0d-5c8d-b06c-c0f73ae8b7cd
      - **organization_name:** Synapse
      - **device_group_assignments:**
        - *(none)*
      - **location_group_assignments:**
        - *(none)*

---

## User: Disabled User

- **user_id:** 550e8400-e29b-41d4-a716-446655440007
- **user_orig_id:** 9007
- **user_first_name:** Disabled
- **user_last_name:** User
- **user_mobile:** ******-555-1007
- **user_notification_sms_enabled:** false
- **user_iana_timezone:** America/Chicago
- **user_description:** Test disabled user
- **user_is_deleted:** true

### Auth Methods
- **authmethod_id:** 550e8400-e29b-41d4-a716-446655440107
  - **authmethod_user_id:** 550e8400-e29b-41d4-a716-446655440007
  - **authmethod_type:** USERNAME_PASSWORD
  - **authmethod_username:** <EMAIL>
  - **authmethod_email:** <EMAIL>
  - **authmethod_password:** puppies1234
  - **memberships:**
    - **membership_id:** 550e8400-e29b-41d4-a716-446655440209
      - **membership_authmethod_id:** 550e8400-e29b-41d4-a716-446655440107
      - **membership_organization_id:** 550e8400-e29b-41d4-a716-446655440010
      - **organization_name:** Test Organization 1
      - **device_group_assignments:**
        - *(none)*
      - **location_group_assignments:**
        - *(none)*

---

## User: Reports User

- **user_id:** 550e8400-e29b-41d4-a716-446655440009
- **user_orig_id:** 9009
- **user_first_name:** Reports
- **user_last_name:** User
- **user_mobile:** ******-555-1009
- **user_notification_sms_enabled:** false
- **user_iana_timezone:** America/Chicago
- **user_description:** Test user with reports permissions only
- **user_is_deleted:** false

### Auth Methods
- **authmethod_id:** 550e8400-e29b-41d4-a716-446655440109
  - **authmethod_user_id:** 550e8400-e29b-41d4-a716-446655440009
  - **authmethod_type:** USERNAME_PASSWORD
  - **authmethod_username:** <EMAIL>
  - **authmethod_email:** <EMAIL>
  - **authmethod_password:** puppies1234
  - **memberships:**
    - **membership_id:** 550e8400-e29b-41d4-a716-446655440212
      - **membership_authmethod_id:** 550e8400-e29b-41d4-a716-446655440109
      - **membership_organization_id:** 550e8400-e29b-41d4-a716-446655440010
      - **organization_name:** Test Organization 1
      - **device_group_assignments:**
        - *(none)*
      - **location_group_assignments:**
        - *(none)*

