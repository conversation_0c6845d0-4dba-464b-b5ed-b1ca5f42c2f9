# User Roles and Permissions

This document outlines the roles used by test users in the system and their associated permissions.

---

## Template Roles

All custom roles are based on these template roles:

### Municipality Admin (`mun_admin`)
**Full administrative access to municipality organizations**

#### Organization Permissions
- ✅ `org_view_users` - View all users in the organization
- ✅ `org_manage_users` - Invite and edit users in the organization  
- ✅ `org_delete_users` - Remove users from the organization
- ✅ `org_view_settings` - View organization-wide settings and preferences
- ✅ `org_manage_settings` - Change organization-wide settings and preferences
- ✅ `org_manage_device_groups` - Create, edit, and delete device groups
- ✅ `org_manage_location_groups` - Create, edit, and delete location groups
- ✅ `org_view_devices` - View all devices in the organization
- ✅ `org_manage_devices` - Update operational fields on all devices
- ✅ `org_delete_devices` - Delete devices in the organization
- ✅ `org_aps_factory_reset` - Get APS factory reset (added via override)

#### Device Group Permissions  
- ✅ `device_group_view_devices` - View devices within device groups
- ✅ `device_group_manage_devices` - Update operational fields on devices in device groups
- ✅ `device_group_delete_devices` - Delete devices within device groups

#### Reports Permissions
- ✅ `org_view_reports` - View reports in the organization
- ✅ `org_view_admin_reports` - View admin reports in the organization

---

### Municipality Manager (`mun_manager`)
**Management access with limited delete permissions**

#### Organization Permissions
- ✅ `org_view_users` - View all users in the organization
- ✅ `org_manage_users` - Invite and edit users in the organization
- ❌ `org_delete_users` - Remove users from the organization
- ✅ `org_view_settings` - View organization-wide settings and preferences  
- ❌ `org_manage_settings` - Change organization-wide settings and preferences
- ✅ `org_manage_device_groups` - Create, edit, and delete device groups
- ✅ `org_manage_location_groups` - Create, edit, and delete location groups
- ✅ `org_view_devices` - View all devices in the organization
- ✅ `org_manage_devices` - Update operational fields on all devices
- ❌ `org_delete_devices` - Delete devices in the organization

#### Device Group Permissions
- ✅ `device_group_view_devices` - View devices within device groups
- ✅ `device_group_manage_devices` - Update operational fields on devices in device groups
- ❌ `device_group_delete_devices` - Delete devices within device groups

#### Reports Permissions
- ✅ `org_view_reports` - View reports in the organization
- ✅ `org_view_admin_reports` - View admin reports in the organization

---

### Municipality Technician (`mun_technician`)
**Device-focused permissions for field technicians**

#### Organization Permissions
- ❌ `org_view_users` - View all users in the organization
- ❌ `org_manage_users` - Invite and edit users in the organization
- ❌ `org_delete_users` - Remove users from the organization
- ❌ `org_view_settings` - View organization-wide settings and preferences
- ❌ `org_manage_settings` - Change organization-wide settings and preferences
- ❌ `org_manage_device_groups` - Create, edit, and delete device groups
- ❌ `org_manage_location_groups` - Create, edit, and delete location groups
- ✅ `org_view_devices` - View all devices in the organization
- ❌ `org_manage_devices` - Update operational fields on all devices
- ❌ `org_delete_devices` - Delete devices in the organization

#### Device Group Permissions
- ✅ `device_group_view_devices` - View devices within device groups
- ✅ `device_group_manage_devices` - Update operational fields on devices in device groups
- ❌ `device_group_delete_devices` - Delete devices within device groups

#### Reports Permissions
- ✅ `org_view_reports` - View reports in the organization
- ❌ `org_view_admin_reports` - View admin reports in the organization

---

### Municipality Anonymous (`mun_anonymous`)
**No access - all permissions denied**

#### Organization Permissions
- ❌ All organization permissions denied

#### Device Group Permissions  
- ❌ All device group permissions denied

#### Reports Permissions
- ❌ All reports permissions denied

---

## Custom Roles Used by Test Users

### Test Org 1 Admin
- **Role ID:** `550e8400-e29b-41d4-a716-************`
- **Based on:** `mun_admin` template
- **Permissions:** Full admin access (see Municipality Admin above)
- **Special Overrides:** 
  - `org_aps_factory_reset` enabled
  - **Reports User Override:** For the Reports User, most admin permissions are disabled except reports permissions

### Test Org 2 Admin
- **Role ID:** `550e8400-e29b-41d4-a716-************`
- **Based on:** `mun_admin` template
- **Permissions:** Full admin access (see Municipality Admin above)

### Test Org 1 Manager  
- **Role ID:** `550e8400-e29b-41d4-a716-************`
- **Based on:** `mun_manager` template
- **Permissions:** Manager access (see Municipality Manager above)

### Test Org 1 Technician
- **Role ID:** `550e8400-e29b-41d4-a716-************`
- **Based on:** `mun_technician` template  
- **Permissions:** Device-focused access (see Municipality Technician above)

### Test Org 2 Technician
- **Role ID:** `550e8400-e29b-41d4-a716-************`
- **Based on:** `mun_technician` template  
- **Permissions:** Device-focused access (see Municipality Technician above)

### Test Custom Role
- **Role ID:** `550e8400-e29b-41d4-a716-************`
- **Based on:** `mun_technician` template
- **Special Overrides:**
  - ✅ `org_manage_devices` - **ENABLED** (normally disabled for technician)
  - ❌ `device_group_delete_devices` - **DISABLED** (normally disabled for technician, explicitly set)

### Cornelius Org Admin
- **Role ID:** `uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')` → `49998574-3c34-5649-9e33-f0a21abb0b1a`
- **Based on:** `mun_admin` template
- **Permissions:** Full admin access (see Municipality Admin above)

### Tam Org Admin
- **Role ID:** `uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin')` → `310e2001-ffe5-5e0a-901c-eb4e5bdfc703`
- **Based on:** `mun_admin` template
- **Permissions:** Full admin access (see Municipality Admin above)

### Duc Org Admin
- **Role ID:** `uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin')` → `3244dd58-1a47-5f49-ac67-0b29edd76d04`
- **Based on:** `mun_admin` template
- **Permissions:** Full admin access (see Municipality Admin above)

### Minh Org Admin
- **Role ID:** `uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin')` → `e61dd50b-e2ed-5e3b-a3ae-a2964af04d56`
- **Based on:** `mun_admin` template
- **Permissions:** Full admin access (see Municipality Admin above)

---

## Users and Their Role Assignments

| User | Role(s) | Role ID(s) | Template | Key Capabilities |
|------|---------|------------|----------|------------------|
| **Admin User** | Test Org 1 Admin, Test Org 2 Admin | `550e8400-e29b-41d4-a716-************`, `550e8400-e29b-41d4-a716-************` | `mun_admin` | Full admin access to both test organizations |
| **Manager User** | Test Org 1 Manager | `550e8400-e29b-41d4-a716-************` | `mun_manager` | Management access to Test Org 1, no delete permissions |
| **Technician User** | Test Org 1 Technician | `550e8400-e29b-41d4-a716-************` | `mun_technician` | Device group management in Test Org 1 |
| **Custom User** | Test Custom Role | `550e8400-e29b-41d4-a716-************` | `mun_technician` + overrides | Enhanced technician with org device management |
| **MultiOrg User** | Test Org 1 Manager | `550e8400-e29b-41d4-a716-************` | `mun_manager` | Management access to Test Org 1 |
| **DeviceOnly User** | Test Org 1 Technician, Test Org 2 Technician | `550e8400-e29b-41d4-a716-************`, `550e8400-e29b-41d4-a716-************` | `mun_technician` | Device group access in both test organizations |
| **Test User** | Cornelius/Tam/Duc/Minh Org Admin | `49998574-3c34-5649-9e33-f0a21abb0b1a`, `310e2001-ffe5-5e0a-901c-eb4e5bdfc703`, `3244dd58-1a47-5f49-ac67-0b29edd76d04`, `e61dd50b-e2ed-5e3b-a3ae-a2964af04d56` | `mun_admin` | Full admin access to all dev organizations |
| **Test1 User** | Cornelius/Tam/Duc/Minh Org Admin | `49998574-3c34-5649-9e33-f0a21abb0b1a`, `310e2001-ffe5-5e0a-901c-eb4e5bdfc703`, `3244dd58-1a47-5f49-ac67-0b29edd76d04`, `e61dd50b-e2ed-5e3b-a3ae-a2964af04d56` | `mun_admin` | Full admin access to all dev organizations |
| **Anonymous User** | (No role assignments) | N/A | N/A | No permissions |
| **Admin Synapse** | (OIDC user) | N/A | N/A | External authentication |
| **Disabled User** | Test Org 1 Admin | `550e8400-e29b-41d4-a716-************` | `mun_admin` | Full admin (but user is disabled) |
| **Reports User** | Test Org 1 Admin (with overrides) | `550e8400-e29b-41d4-a716-************` | `mun_admin` | Only reports permissions enabled |

---

## Permission Categories

### Organization Scope
Permissions that apply across the entire organization:
- User management (view, manage, delete users)
- Settings management  
- Device group and location group management
- Organization-wide device management
- APS factory reset capabilities

### Device Group Scope  
Permissions that apply to specific device groups assigned to users:
- View devices in assigned groups
- Manage devices in assigned groups  
- Delete devices in assigned groups

### Reports Scope
Permissions for viewing organizational data:
- Standard reports access
- Administrative reports access

---

## Notes

- **Custom Role Overrides:** Some roles have specific permission overrides that differ from their template
- **Reports User:** Uses admin template but has most permissions disabled except reports
- **Test Custom Role:** Enhanced technician role with additional device management capabilities
- **OIDC Users:** External authentication users may have different permission structures
- **Disabled Users:** May have role assignments but cannot authenticate
