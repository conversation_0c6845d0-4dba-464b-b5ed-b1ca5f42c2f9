# User Organizations

This document lists all organizations in the system and the users who have membership in each organization.

---

## Test Organizations

### Test Organization 1
- **Organization ID:** `550e8400-e29b-41d4-a716-446655440010`
- **Description:** First test organization for permissions
- **Type:** Municipality

#### Members:
1. **Admin User** (`<EMAIL>`)
   - **User ID:** `550e8400-e29b-41d4-a716-************`
   - **Role:** Test Org 1 Admin
   - **Access Level:** Full administrative access

2. **Manager User** (`<EMAIL>`)
   - **User ID:** `550e8400-e29b-41d4-a716-446655440002`
   - **Role:** Test Org 1 Manager
   - **Access Level:** Management access (limited delete permissions)

3. **Technician User** (`<EMAIL>`)
   - **User ID:** `550e8400-e29b-41d4-a716-446655440003`
   - **Role:** Test Org 1 Technician
   - **Access Level:** Device group management only

4. **Custom User** (`<EMAIL>`)
   - **User ID:** `550e8400-e29b-41d4-a716-446655440004`
   - **Role:** Test Custom Role
   - **Access Level:** Enhanced technician with organization device management

5. **MultiOrg User** (`<EMAIL>`)
   - **User ID:** `550e8400-e29b-41d4-a716-************`
   - **Role:** Test Org 1 Manager
   - **Access Level:** Management access (limited delete permissions)

6. **DeviceOnly User** (`<EMAIL>`)
   - **User ID:** `550e8400-e29b-41d4-a716-************`
   - **Role:** Test Org 1 Technician
   - **Access Level:** Device group management only

7. **Anonymous User** (`<EMAIL>`)
   - **User ID:** `550e8400-e29b-41d4-a716-************`
   - **Role:** None (membership only, no role assignments)
   - **Access Level:** No permissions

8. **Disabled User** (`<EMAIL>`)
   - **User ID:** `550e8400-e29b-41d4-a716-************`
   - **Role:** Test Org 1 Admin
   - **Access Level:** Full administrative access (but user account is disabled)

9. **Reports User** (`<EMAIL>`)
   - **User ID:** `550e8400-e29b-41d4-a716-************`
   - **Role:** Test Org 1 Admin (with permission overrides)
   - **Access Level:** Reports access only (all other admin permissions disabled)

---

### Test Organization 2
- **Organization ID:** `550e8400-e29b-41d4-a716-************`
- **Description:** Second test organization for permissions
- **Type:** Municipality

#### Members:
1. **Admin User** (`<EMAIL>`)
   - **User ID:** `550e8400-e29b-41d4-a716-************`
   - **Role:** Test Org 2 Admin
   - **Access Level:** Full administrative access

2. **MultiOrg User** (`<EMAIL>`)
   - **User ID:** `550e8400-e29b-41d4-a716-************`
   - **Role:** None (membership only, no role assignments)
   - **Access Level:** No permissions

3. **DeviceOnly User** (`<EMAIL>`)
   - **User ID:** `550e8400-e29b-41d4-a716-************`
   - **Role:** Test Org 2 Technician
   - **Access Level:** Device group management only

---

## Developer Organizations

### Cornelius's Organization
- **Organization ID:** `uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')` → `b6098079-54e7-5507-9fb7-972fff600a91`
- **Description:** Cornelius Dev Organization
- **Type:** Municipality

#### Members:
1. **Test User** (`<EMAIL>`)
   - **User ID:** `27c68173-289a-5e53-92ae-c598d847038c`
   - **Role:** Cornelius Org Admin
   - **Access Level:** Full administrative access

2. **Test1 User** (`<EMAIL>`)
   - **User ID:** `063f2596-9dea-57f8-84aa-5d693c59f4e4`
   - **Role:** Cornelius Org Admin
   - **Access Level:** Full administrative access

---

### Tam's Organization
- **Organization ID:** `uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam')` → `147c9e71-ce1a-559d-85f3-960e9180880c`
- **Description:** Tam Dev Organization
- **Type:** Municipality

#### Members:
1. **Test User** (`<EMAIL>`)
   - **User ID:** `27c68173-289a-5e53-92ae-c598d847038c`
   - **Role:** Tam Org Admin
   - **Access Level:** Full administrative access

2. **Test1 User** (`<EMAIL>`)
   - **User ID:** `063f2596-9dea-57f8-84aa-5d693c59f4e4`
   - **Role:** Tam Org Admin
   - **Access Level:** Full administrative access

---

### Duc's Organization
- **Organization ID:** `uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc')` → `e2074a21-5a79-5220-b46a-3e9864c268bc`
- **Description:** Duc Dev Organization
- **Type:** Municipality

#### Members:
1. **Test User** (`<EMAIL>`)
   - **User ID:** `27c68173-289a-5e53-92ae-c598d847038c`
   - **Role:** Duc Org Admin
   - **Access Level:** Full administrative access

2. **Test1 User** (`<EMAIL>`)
   - **User ID:** `063f2596-9dea-57f8-84aa-5d693c59f4e4`
   - **Role:** Duc Org Admin
   - **Access Level:** Full administrative access

---

### Minh's Organization
- **Organization ID:** `uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh')` → `6c9095e3-178a-5760-89df-0cbb094470a7`
- **Description:** Minh Dev Organization
- **Type:** Municipality

#### Members:
1. **Test User** (`<EMAIL>`)
   - **User ID:** `27c68173-289a-5e53-92ae-c598d847038c`
   - **Role:** Minh Org Admin
   - **Access Level:** Full administrative access

2. **Test1 User** (`<EMAIL>`)
   - **User ID:** `063f2596-9dea-57f8-84aa-5d693c59f4e4`
   - **Role:** Minh Org Admin
   - **Access Level:** Full administrative access

---

## Internal Organizations

### Synapse
- **Organization ID:** `uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse')` → `55d832bf-cd0d-5c8d-b06c-c0f73ae8b7cd`
- **Description:** Synapse-Its.com Organization
- **Type:** Synapse (Internal)

#### Members:
1. **Admin Synapse** (`<EMAIL>`)
   - **User ID:** `f5854028-dfd1-50fe-9e31-83f0393cb4fe`
   - **Authentication:** OIDC (External)
   - **Role:** Synapse Admin (via OIDC)
   - **Access Level:** Internal Synapse administrative access

---

## Organization Summary

| Organization | Type | User Count | Primary Purpose |
|-------------|------|------------|-----------------|
| **Test Organization 1** | Municipality | 9 users | Permission testing and validation |
| **Test Organization 2** | Municipality | 3 users | Multi-organization testing |
| **Cornelius's Organization** | Municipality | 2 users | Developer testing environment |
| **Tam's Organization** | Municipality | 2 users | Developer testing environment |
| **Duc's Organization** | Municipality | 2 users | Developer testing environment |
| **Minh's Organization** | Municipality | 2 users | Developer testing environment |
| **Synapse** | Internal | 1 user | Internal Synapse operations |

---

## User Distribution

### Users with Multiple Organization Access:
- **Admin User**: Test Organization 1, Test Organization 2 (2 orgs)
- **MultiOrg User**: Test Organization 1, Test Organization 2 (2 orgs)
- **DeviceOnly User**: Test Organization 1, Test Organization 2 (2 orgs)
- **Test User**: All 4 developer organizations (4 orgs)
- **Test1 User**: All 4 developer organizations (4 orgs)

### Users with Single Organization Access:
- **Manager User**: Test Organization 1 only
- **Technician User**: Test Organization 1 only
- **Custom User**: Test Organization 1 only
- **Anonymous User**: Test Organization 1 only
- **Disabled User**: Test Organization 1 only
- **Reports User**: Test Organization 1 only
- **Admin Synapse**: Synapse only

---

## Notes

- **Test Organizations**: Used for permission testing and validation scenarios
- **Developer Organizations**: Individual development environments for team members
- **Synapse Organization**: Internal organization for Synapse-specific operations
- **OIDC Authentication**: Admin Synapse user uses external OIDC authentication
- **Disabled Users**: Disabled User has role assignments but cannot authenticate
- **Permission Overrides**: Some users (like Reports User) have custom permission overrides that modify their effective access level
