# Email Verification API

This document describes the email verification API endpoints for user self-service email verification.

## Overview

The email verification system allows users to verify their email addresses through a self-service flow. Users can request verification emails and verify their email addresses using secure tokens.

## Endpoints

### Request Email Verification

**POST** `/user/{userId}/email-verification`

Requests a verification email to be sent to the user's primary email address.

#### Request Body

```json
{
  "expiryMinutes": 15  // Optional, defaults to 15 minutes
}
```

#### Response

```json
{
  "id": "uuid",
  "authMethodId": "uuid",
  "status": "pending",
  "retryCount": 0,
  "retried": null,
  "expired": "2024-01-01T12:15:00Z",
  "created": "2024-01-01T12:00:00Z",
  "sent": null,
  "updated": "2024-01-01T12:00:00Z"
}
```

#### Error Responses

- `400 Bad Request`: Email already verified, invalid request body, or no email auth method found
- `425 Too Early`: Resend cooldown period not passed (1 minute)
- `500 Internal Server Error`: Database or email service errors

### Verify Email

**POST** `/user/{userId}/email-verification/verify?token={token}`

Verifies an email address using a verification token.

#### Query Parameters

- `token` (required): The verification token from the email

#### Response

```json
{
  "id": "uuid",
  "authMethodId": "uuid",
  "status": "verified",
  "retryCount": 0,
  "retried": null,
  "expired": "2024-01-01T12:15:00Z",
  "created": "2024-01-01T12:00:00Z",
  "sent": null,
  "updated": "2024-01-01T12:00:00Z"
}
```

#### Error Responses

- `400 Bad Request`: Missing token, already verified, or invalid token
- `401 Unauthorized`: Token not found, expired, or doesn't belong to user
- `500 Internal Server Error`: Database errors

## Configuration

### Default Values

- **Expiry Time**: 15 minutes
- **Cooldown Period**: 1 minute
- **Token Length**: 64 characters (hex)

### Rate Limiting

- Users can only request a new verification email after the cooldown period (1 minute) has passed
- If a verification request exists and hasn't expired, a new request will be rejected
- If a verification request has expired, a new request will create a new verification record

## Security Features

- Tokens are hashed before storage in the database
- Tokens expire after 15 minutes by default
- Users can only verify their own email addresses
- Cooldown period prevents spam/abuse

## Database Schema

### EmailVerification Table

```sql
CREATE TABLE EmailVerification (
  Id UUID PRIMARY KEY,
  AuthMethodId UUID NOT NULL UNIQUE,
  TokenHash TEXT NOT NULL UNIQUE,
  Status TEXT NOT NULL CHECK (Status IN ('pending','verified','expired')),
  RetryCount INTEGER NOT NULL DEFAULT 0,
  Retried TIMESTAMPTZ,
  Expired TIMESTAMPTZ,
  Created TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  Sent TIMESTAMPTZ,
  Updated TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

### AuthMethod Table Updates

```sql
ALTER TABLE AuthMethod ADD COLUMN EmailVerified BOOLEAN NOT NULL DEFAULT FALSE;
```

## BigQuery Audit

All email verification events are logged to BigQuery for audit purposes:

- **Event Types**: `create`, `verify`, `expire`
- **Table**: `EmailVerificationEvents`
- **Partitioned by**: `eventtime`
- **Clustered by**: `emailverificationid`

## Email Template

The verification email includes:
- Professional HTML template
- Verification link with token
- 15-minute expiry notice
- Security information

## Error Handling

The API provides detailed error responses for common scenarios:
- Email already verified
- Invalid or expired tokens
- Rate limiting violations
- Missing authentication methods
- Database connectivity issues
