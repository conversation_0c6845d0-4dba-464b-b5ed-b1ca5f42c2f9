# This image is for developement purposes only!!!
# If you use it in production, you will be fired.
# Don't do it.
# Don't even think about it.

# The Keycloak image is based on ubi-micro, which is a very small image
# that doesn't include any package manager. This means we can't install
# jq or any other tools we might need to run our entrypoint script.
# To work around this, we build a second image that installs needed
# packages into a chroot directory, and then copy that chroot into
# the Keycloak image. This way, we can install whatever is needed.

# 1) Build stage: install jq + util-linux into a fake root
FROM registry.access.redhat.com/ubi9 AS ubi-micro-build

# make a chroot dir
RUN mkdir -p /mnt/rootfs

# install just jq and util-linux (for setsid) and their deps into /mnt/rootfs
RUN dnf install \
      --installroot /mnt/rootfs \
      --releasever 9 \
      --setopt install_weak_deps=false \
      --nodocs -y \
      jq \
      util-linux \
      diffutils \
      postgresql \
  && dnf --installroot /mnt/rootfs clean all \
  && rpm --root /mnt/rootfs -e --nodeps setup

# 2) Final stage: copy that chroot into the Keycloak image
FROM quay.io/keycloak/keycloak:latest

# merge in everything we installed under /mnt/rootfs
COPY --from=ubi-micro-build /mnt/rootfs/ /

# use your custom entrypoint as before
ENTRYPOINT ["/app/docker-entrypoint.sh"]
