# BigQuery Emulator Overview

## Introduction and Problem Statement

It is the development philosophy of this team that **every** tool/microservice of the `data-core` stack be able to be run locally.  This is straightforward for our own microservices, but integrating 3rd-party tools is more difficult.  For these, we rely on **emulators** to provide the needed functionality.  The goal is that the **entire development stack** should be able to be run locally, which means that all tools should be stable, deterministic, and idempotent.

Google provides emulators for almost all of their services (PubSub, etc.), but **not** BigQuery.  As such, we rely on a 3rd-party emulator (the only one that we could find!) to fill this gap.

[BigQuery Emulator](https://github.com/goccy/bigquery-emulator) is the tool that we use.  The problem is that, while it does *many* things that we need, there are a few missing features, which we must fix ourselves.  Unfortunately, it seems that the maintainer's interest in the project has waned, so we are largely on our own here, but we don't have a better option.

The goal of this document is to explain **how** to develop and work with our modified emulator.

Even though progress on the GitHub project has stagnated, we still would like to contribute our modifications back to the community, and so have sought to keep our modifications compartmentalized in such a way that patches can be shared, minimizing interdependence.
