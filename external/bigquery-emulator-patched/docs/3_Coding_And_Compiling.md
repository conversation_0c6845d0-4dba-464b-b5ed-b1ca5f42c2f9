# Coding And Compiling

## What's Different

Working on the emulator is *not* done as part of the `data-core` repo.  I have found it easiest to use the **WSL Ubuntu 24.04 (LTS)**.  You may need to install additional packages, but I believe that I installed this:

```bash
sudo apt install git vim ca-certificates libssl-dev clang ccache gcc
```

You may need to add additional build tools such as `build-essential` or `cmake`, but I don't remember whether or not they are explicitly required.

Please note that you can access WSL files from Windows, and you can access Windows files through the WSL, you just need to know the paths involved.

The standard development for the `data-core` team puts the code in a Windows directory, and edits it using Windows programs (VS Code/Cursor, Git, etc.).  Modifying the BigQuery Emulator, however, is done in opposite: For faster compilation, all editing is done in the WSL (VS Code/Cursor can still access it), but once compiled to a single binary, it is copied out to `data-core` on the Windows file system so that it can be tested.

  > **NOTE:** The first time that the emulator is compiled, it takes approximately **1 hour** to compile fully (although subsequent compiles are significantly faster).  Plan accordingly.

## The Repos

There are 3 repos that may need to be modified:
  - [BigQuery Emulator](https://github.com/goccy/bigquery-emulator.git) - The actual emulator that provides APIs and stores data in ZetaSQLite
  - [Go-ZetaSQL](https://github.com/goccy/go-zetasql.git) - The library for parsing SQL statements
  - [Go-ZetaSQLite](https://github.com/goccy/go-zetasqlite.git) - The library that uses ZetaSQL structures with SQLite

I placed the 3 repos in my `~/bqe` folder like this:

```bash
mkdir -p ~/bqe/bigquery-emulator && git clone --origin goccy https://github.com/goccy/bigquery-emulator.git ~/bqe/bigquery-emulator
mkdir -p ~/bqe/go-zetasql && git clone --origin goccy https://github.com/goccy/go-zetasql.git ~/bqe/go-zetasql
mkdir -p ~/bqe/go-zetasqlite && git clone --origin goccy https://github.com/goccy/go-zetasqlite.git ~/bqe/go-zetasqlite
```

  > NOTE: The remote for each of the cloned repos is named `goccy`.  This is intentional, and will make it easier to notice in the documentation if the remote changes.

## Patch files

We have already been customizing the libraries.  Please see the `external/bigquery-emulator-patched` directory in the `data-core` repo to see the existing diffs.  As of this writing, there are only two: `bigquery-emulator.diff` and `go-zetasqlite.diff`.  `go-zetasql` has not been modified as of this writing and could technically be omitted from the build steps, but it is assumed that it will also need to be modified at some point and is therefore being included.


### Applying a patch file

To apply the patch files, copy the patch file into the WSL directory `~/bqe`, and do something similar to this:

```bash
(cd ~/bqe/bigquery-emulator && patch -p1 < ../bigquery-emulator.diff)
(cd ~/bqe/go-zetasqlite && patch -p1 < ../go-zetasqlite.diff)
```

### Creating a patch file

The diff (patch) files should be a unified diff.  There are multiple ways of creating them, but one way is:

```bash
git diff goccy/main > ../go-zetasqlite.diff
```

Diff files should be committed to the `data-core` repo.  The diff should be all-inclusive.  Smaller diffs may be generated if they represent a GitHub pull request.

  > NOTE: The diff is always against the 3rd-party remote main (in this case, named `goccy`).  If this needs to change (and there may be a good reason to diff against something else, especially if another fork has needed features) then documentation should be updated accordingly.

## Compilation script

I use a helper script to do the compilation (usage shown in the next section, after this code block):

```bash
#!/usr/bin/env bash
set -euo pipefail

# ------------------------------------------------------------
# BigQuery Emulator builder with conditional local zetasql
# ------------------------------------------------------------
# Behavior:
# - If USE_LOCAL_ZETASQL=true (via config/env/flag), we:
#    * build ~/bqe/go-zetasql
#    * add replace in go-zetasqlite -> ../go-zetasql
#    * add replace in bigquery-emulator -> ../go-zetasql and ../go-zetasqlite
# - If USE_LOCAL_ZETASQL=false, we:
#    * skip building ~/bqe/go-zetasql
#    * DO NOT add replace for go-zetasql anywhere
#    * add replace in bigquery-emulator only for go-zetasqlite
#
# Config precedence (highest first):
#   CLI flag: --with-zetasql / --without-zetasql (optional --set-default)
#   ENV var: USE_LOCAL_ZETASQL=true|false
#   Config file: ~/.bqe-build.conf  (contains: USE_LOCAL_ZETASQL=true|false)
#
# Output:
#   ~/go/bin/bigquery-emulator -> gzip -> /mnt/c/.../bqbin.gz
# ------------------------------------------------------------

# --- Paths (adjust if needed) ---
BQE_ROOT="${BQE_ROOT:-$HOME/bqe}"
EMU_DIR="$BQE_ROOT/bigquery-emulator"
ZETA_DIR="$BQE_ROOT/go-zetasql"
ZETALITE_DIR="$BQE_ROOT/go-zetasqlite"

OUT_GZ="/mnt/c/Users/<USER>/source/data-core/external/bigquery-emulator-patched/bqbin.gz"
OUT_DIR="$(dirname "$OUT_GZ")"

# --- Config ---
CONF_FILE="${CONF_FILE:-$HOME/.bqe-build.conf}"
USE_LOCAL_ZETASQL="${USE_LOCAL_ZETASQL:-}"

print_usage() {
  cat <<EOF
Usage: $(basename "$0") [--with-zetasql|--without-zetasql] [--set-default]

Options:
  --with-zetasql       Use local ~/bqe/go-zetasql (compile it; add replace lines).
  --without-zetasql    Do NOT use local go-zetasql (skip compile; no replace).
  --set-default        Persist the selected zetasql mode to $CONF_FILE.

Precedence: CLI flag > USE_LOCAL_ZETASQL env var > $CONF_FILE
EOF
}

# Parse flags
SET_DEFAULT="false"
CLI_CHOICE=""
while [[ $# -gt 0 ]]; do
  case "$1" in
    --with-zetasql) CLI_CHOICE="true"; shift ;;
    --without-zetasql) CLI_CHOICE="false"; shift ;;
    --set-default) SET_DEFAULT="true"; shift ;;
    -h|--help) print_usage; exit 0 ;;
    *) echo "Unknown option: $1" >&2; print_usage; exit 1 ;;
  esac
done

# Load from file if not set via CLI/ENV
if [[ -z "${USE_LOCAL_ZETASQL:-}" ]]; then
  if [[ -n "$CLI_CHOICE" ]]; then
    USE_LOCAL_ZETASQL="$CLI_CHOICE"
  elif [[ -f "$CONF_FILE" ]]; then
    # shellcheck disable=SC1090
    source "$CONF_FILE"
    USE_LOCAL_ZETASQL="${USE_LOCAL_ZETASQL:-false}"
  else
    USE_LOCAL_ZETASQL="false"
  fi
else
  # ENV provided; optionally overridden by CLI
  if [[ -n "$CLI_CHOICE" ]]; then
    USE_LOCAL_ZETASQL="$CLI_CHOICE"
  fi
fi

if [[ "$SET_DEFAULT" == "true" ]]; then
  echo "USE_LOCAL_ZETASQL=$USE_LOCAL_ZETASQL" > "$CONF_FILE"
  echo "Saved default to $CONF_FILE"
fi

echo "==> USE_LOCAL_ZETASQL = $USE_LOCAL_ZETASQL"

# --- Helpers: do-edits-with-backup in a subshell that always restores ---
with_go_mod_backup() {
  # Args: <repo_dir> <commands...>
  local repo_dir="$1"; shift
  (
    set -euo pipefail
    cd "$repo_dir"

    # Back up mod files if present (some repos may not have go.sum yet)
    [[ -f go.mod ]] && cp -f go.mod go.mod.bak || true
    [[ -f go.sum ]] && cp -f go.sum go.sum.bak || true

    restore() {
      # Restore only if backups exist
      [[ -f go.mod.bak ]] && mv -f go.mod.bak go.mod || true
      [[ -f go.sum.bak ]] && mv -f go.sum.bak go.sum || true
    }
    trap restore EXIT

    # Run the provided commands that may edit go.mod/go.sum
    "$@"
  )
}

# --- Step 0: optionally build local go-zetasql ---
if [[ "$USE_LOCAL_ZETASQL" == "true" ]]; then
  echo "==> Building local go-zetasql (archive)"
  ( cd "$ZETA_DIR" && go install -buildmode=archive . )
else
  echo "==> Skipping local go-zetasql build"
fi

# --- Step 1: build go-zetasqlite (may need replace -> go-zetasql) ---
echo "==> Building go-zetasqlite (archive)"
if [[ "$USE_LOCAL_ZETASQL" == "true" ]]; then
  # Add replace for go-zetasql during build, then restore
  with_go_mod_backup "$ZETALITE_DIR" bash -ceu '
    go mod edit -replace=github.com/goccy/go-zetasql=../go-zetasql
    go install -buildmode=archive .
  '
else
  # Build without any replace (use upstream zetasql)
  ( cd "$ZETALITE_DIR" && go install -buildmode=archive . )
fi

# --- Step 2: build bigquery-emulator with the minimal necessary replaces ---
echo "==> Building bigquery-emulator (exe)"
with_go_mod_backup "$EMU_DIR" bash -ceu '
  # Always point emulator to local zetasqlite
  go mod edit -replace=github.com/goccy/go-zetasqlite=../go-zetasqlite

  # Conditionally point emulator to local zetasql
  if [[ "'"$USE_LOCAL_ZETASQL"'" == "true" ]]; then
    go mod edit -replace=github.com/goccy/go-zetasql=../go-zetasql
  fi

  go install -buildmode=exe ./cmd/bigquery-emulator
'

# --- Step 3: package output ---
echo "==> Packaging emulator to $OUT_GZ"
mkdir -p "$OUT_DIR"
gzip -c "$HOME/go/bin/bigquery-emulator" > "$OUT_GZ"
echo "==> Done."
```

## Using the script

This repository includes a helper script for rebuilding the [BigQuery Emulator](https://github.com/goccy/bigquery-emulator) against local development versions of its dependencies.

The script automatically:

- Builds [`go-zetasqlite`](https://github.com/goccy/go-zetasqlite) (and optionally [`go-zetasql`](https://github.com/goccy/go-zetasql))
- Temporarily adds the required `replace` directives to `go.mod` files
- Restores all `go.mod`/`go.sum` files to their original state after the build
- Produces a compressed emulator binary for distribution

---

### Usage

```bash
./build-bqe.sh [--with-zetasql|--without-zetasql] [--set-default]
```

#### Options

- `--with-zetasql`  
  Build and use the local `~/bqe/go-zetasql` project.  
  Adds `replace` directives for `go-zetasql` in both **go-zetasqlite** and **bigquery-emulator**.

- `--without-zetasql`  
  Do **not** build `go-zetasql`.  
  `go-zetasqlite` will depend on the upstream `go-zetasql`.  
  The **bigquery-emulator** will only use the local `go-zetasqlite`.

- `--set-default`  
  Persist the chosen mode (`with` or `without`) to `~/.bqe-build.conf`.  
  Future invocations without flags will use this default.

#### Precedence

1. Command-line flags (`--with-zetasql` / `--without-zetasql`)
2. Environment variable:
   ```bash
   export USE_LOCAL_ZETASQL=true   # or false
   ```
3. Config file: `~/.bqe-build.conf`  
   (Automatically created if you run with `--set-default`.)

---

### Examples

#### Build using upstream `go-zetasql` (default)
```bash
./build-bqe.sh
```

#### Build against local `go-zetasql` for development
```bash
./build-bqe.sh --with-zetasql
```

#### Make local `go-zetasql` the default for future builds
```bash
./build-bqe.sh --with-zetasql --set-default
```

#### Override default using environment variable
```bash
USE_LOCAL_ZETASQL=false ./build-bqe.sh
```

---

### Output

On success, the script produces:

- The built emulator binary at:
  ```
  ~/go/bin/bigquery-emulator
  ```
- A compressed copy written to:
  ```
  /mnt/c/Users/<USER>/source/data-core/external/bigquery-emulator-patched/bqbin.gz
  ```
