# The End Goal

## How we implement the emulator into our development workflow

All code is found in the `data-core` repo, under `external/bigquery-emulator-patched/`.

It is assumed that most of our developers are unaware that we use a hacked emulator, because we have worked hard to make sure that the development project "just works".  This illusion is maintained as follows:

1. There is a zipped binary named `bqbin.gz`.  This is the final executable (zipped, of course).
    - Gitlab places a limit on the total size of our repo, and this file is **VERY LARGE**.  As such, we don't want to modify it often.
    - The emulator container (see the `infra/docker-compose.yml` file) uses a **named** volume and a **mounted** volume.
      - The **named** volume is used to store a copy of the zipped binary as well as an unzipped copy.  The unzipped copy is the executable used by the container.  We use a named volume because it will persist across container restarts and rebuilds, to save time.
      - The **mounted** volume points to the directory containing the zipped binary from the repo.  This volume allows us to always reference the most recent version of the binary, in the event it gets updated when the developer merges the latest changes from *main/master*, for example.
    - When the container starts, it checks the **mounted** volume's zipped binary to see if it is newer than the **named** volume.  If so, it re-copies zipped binary to the named volume and extracts the newer binary from the zip file.  If the mounted volume's zipped binary was *not* newer, then it simply starts the binary that it already has.
      > Note: Why are we going to this trouble?  Because we want the newest version of the emulator to be executed whenever the container starts without having to always rebuild the container.  Furthermore, we don't want for `docker compose` to pull in the binary itself when building the image, because it causes the `docker compose build` **context** to be **very** large, which makes the startup of the dev script **very slow** (see the `.dockerignore` file).  In short, the complexity is to improve the development experience and making it seem that things "just work".

2. We do not store the code of the BigQuery emulator in our repo.  The reason is that it is too big, and there's actually **three** different repos that need to be accessed.  This will be discussed later.

3. We **do** store patch files that can be applied to the official repos, which will apply all of our modifications.

4. We also store patch files that represent the pull requests that have been submitted to GitHub, but we don't have a good way to organize this at the moment.  The "omnibus" patch files already include these smaller, individual patch files.

