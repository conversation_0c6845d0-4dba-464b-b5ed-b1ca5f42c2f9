package bqbatch

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"reflect"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/pubsub"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/mocks/bqexecutor"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// This type is used as a key in which to store the batch in the context.  It
// exists simply to make the linters happy.
type contextBatchKey string

// This constant is the identifier used in the context to store the connection information.
const BatchKey contextBatchKey = "bqbatch"

// PubSub topic name for DLQ backup incase bigquery is down.
const PubsubDLQTopic string = pubsubdata.TopicDLQBQBatch

var (
	// The maximum number of rows that a queue should hold before it flushes.
	MaxRows = 5000
	// The maximum time that a queue should wait before it flushes.
	MaxWait = 10 * time.Second
	// The number of retries allowed if load fails.
	MaxAttempts = 1

	// BigQuery load job size limit (16MB)
	MaxBigQueryBatchSize = 16 * 1024 * 1024 // 16MB in bytes
	// PubSub message size limit (10MB)
	MaxPubSubMessageSize = 9 * 1024 * 1024 // 9MB in bytes

	// Default metrics flush interval
	defaultMetricsFlushInterval = 30 * time.Second
	// Default concurrency limit
	defaultMaxConcurrency = 3

	// Maximum queue depth before blocking (0 = unlimited)
	MaxQueueDepth = 0 // Default: unlimited (backward compatible)

	// Allow overriding time.Sleep for testing
	timeSleep = time.Sleep
)

// Batcher is the public interface.  You Register types, Add rows, then Shutdown.
type Batcher interface {
	// Register ties a Go struct type (T or *T) to a BigQuery table name and config.
	Register(rowExample any, table string, cfg QueueConfig) error
	// Add enqueues a row; it'll dispatch to whichever queue was registered for that type.
	Add(row any) error
	// LoadBatch loads raw JSONL data directly into BigQuery for the specified table
	LoadBatch(table string, rawData []byte) error
	// Shutdown flushes all queues and stops accepting new rows.
	Shutdown() error
}

// batcher is the concrete implementation of Batcher.
type batcher struct {
	exec        connect.BigQueryExecutorInterface
	psClient    connect.PsClient
	cfgs        map[string]QueueConfig
	queues      map[string]*queue
	typeToTable map[reflect.Type]string
	mu          sync.Mutex
	shutdown    bool
}

// queue represents a per-table buffer and its worker.
type queue struct {
	table    string
	cfg      QueueConfig
	schema   bigquery.Schema
	exec     connect.BigQueryExecutorInterface
	psClient connect.PsClient
	batcher  *batcher // Reference to parent batcher for metrics

	// Used to stop the worker.
	ctrlCtx context.Context
	cancel  context.CancelFunc

	// Used for BQ calls.
	loadCtx context.Context

	rows []bigquery.ValueSaver
	mu   sync.Mutex
	wg   sync.WaitGroup

	// Performance metrics accumulator and mutex
	metricsMu sync.Mutex
	metrics   schemas.BatchPerformanceStats

	// Queue depth management
	currentDepth int32      // atomic counter for current queue depth
	maxDepth     int        // maximum allowed depth (0 = unlimited)
	depthMu      sync.Mutex // separate mutex for depth operations
	depthCond    *sync.Cond // condition variable for blocking/unblocking
}

func init() {
	// This is a in-production safety check.  If it fails, the process will be
	// terminated.  It is not a test, but a sanity check.

	// Validate that all the default table/struct pairings are valid.
	for _, q := range queues {
		if q.table == "" {
			logger.Fatalf("Default Batcher failed validation: table name is empty")
		}
		if _, _, err := getValidType(q.rowExample); err != nil {
			logger.Fatalf("Default Batcher failed validation: %v", err)
		}
	}

	// Validate that the batcher initializes correctly.
	fakeExecutor := bqexecutor.NewFakeBigQueryExecutor()
	fakeExecutor.Config = connect.DatabaseConfig{
		Environment: "test",
		DBName:      "test-dataset",
	}
	fakeExecutor.Ctx = context.Background()
	validationBatcher, err := newDefault(fakeExecutor, nil)
	if err != nil {
		logger.Fatalf("Default Batcher failed validation: %v", ErrDefaultBatcher)
	}
	// Immediately shut down the validation batcher to prevent background workers from running
	if err := validationBatcher.Shutdown(); err != nil {
		logger.Fatalf("Failed to shutdown validation batcher: %v", err)
	}

	// Override the MaxRows and MaxWait values if they are overridden in the
	// environment variables.
	if maxRows := os.Getenv("BQBATCH_MAX_ROWS"); maxRows != "" {
		maxRowsInt, err := strconv.Atoi(maxRows)
		if err == nil && maxRowsInt > 0 {
			MaxRows = maxRowsInt
		}
	}
	if maxWait := os.Getenv("BQBATCH_MAX_WAIT"); maxWait != "" {
		maxWaitInt, err := strconv.Atoi(maxWait)
		if err == nil && maxWaitInt > 0 {
			MaxWait = time.Duration(maxWaitInt) * time.Second
		}
	}
	if maxAttempts := os.Getenv("BQBATCH_MAX_ATTEMPTS"); maxAttempts != "" {
		maxAttemptsInt, err := strconv.Atoi(maxAttempts)
		if err == nil && maxAttemptsInt > 0 {
			MaxAttempts = maxAttemptsInt
		}
	}
	if maxBQSize := os.Getenv("BQBATCH_MAX_BQ_SIZE"); maxBQSize != "" {
		maxBQSizeInt, err := strconv.Atoi(maxBQSize)
		if err == nil && maxBQSizeInt > 0 {
			MaxBigQueryBatchSize = maxBQSizeInt
		}
	}
	if maxPSSize := os.Getenv("BQBATCH_MAX_PS_SIZE"); maxPSSize != "" {
		maxPSSizeInt, err := strconv.Atoi(maxPSSize)
		if err == nil && maxPSSizeInt > 0 {
			MaxPubSubMessageSize = maxPSSizeInt
		}
	}
	if metricsFlushInterval := os.Getenv("BQBATCH_METRICS_FLUSH_INTERVAL"); metricsFlushInterval != "" {
		metricsFlushIntervalInt, err := strconv.Atoi(metricsFlushInterval)
		if err == nil && metricsFlushIntervalInt > 0 {
			defaultMetricsFlushInterval = time.Duration(metricsFlushIntervalInt) * time.Second
		}
	}
	if maxConcurrency := os.Getenv("BQBATCH_MAX_CONCURRENCY"); maxConcurrency != "" {
		maxConcurrencyInt, err := strconv.Atoi(maxConcurrency)
		if err == nil && maxConcurrencyInt > 0 {
			defaultMaxConcurrency = maxConcurrencyInt
		}
	}
	if maxQueueDepth := os.Getenv("BQBATCH_MAX_QUEUE_DEPTH"); maxQueueDepth != "" {
		maxQueueDepthInt, err := strconv.Atoi(maxQueueDepth)
		if err == nil && maxQueueDepthInt >= 0 {
			MaxQueueDepth = maxQueueDepthInt
		}
	}
}

// WithBatch adds the Connections to the context.
func WithBatch(ctx context.Context, batch Batcher) context.Context {
	return context.WithValue(ctx, BatchKey, batch)
}

// GetBatch gets the Connections from the context.
// checkConnections is optional
var GetBatch = func(ctx context.Context) (Batcher, error) {
	batch, ok := ctx.Value(BatchKey).(Batcher)
	if !ok {
		return nil, ErrBatchContext
	}

	return batch, nil
}

// Helper function that assembles a batch handler for use in the data-core
// microservices.  This function registers the default table/struct pairings.
func NewDefault(exec connect.BigQueryExecutorInterface, psclient connect.PsClient) Batcher {
	// To be clear, ignoring the error is fine here because the unit test has
	// already verified that all the default table/struct pairings are valid.
	// This is just a convenience function for the data-core microservices.
	// It is also verified in the init() function.
	b, _ := newDefault(exec, psclient)
	return b
}

// This is a list of the default table/struct pairings.  It is validated in a
// unit test that all pairings are valid.
var queues = []struct {
	rowExample any
	table      string
}{
	{schemas.EdiRawMessages{}, schemas.T_EdiRawMessages},
	{schemas.DlqMessages{}, schemas.T_DlqMessages},
	{schemas.RmsEngine{}, schemas.T_RmsEngine},
	{schemas.MonitorName{}, schemas.T_MonitorName},
	{schemas.MacAddress{}, schemas.T_MacAddress},
	{schemas.RmsData{}, schemas.T_RmsData},
	{schemas.FaultNotification{}, schemas.T_FaultNotification},
	{schemas.GatewayPerformanceStatistics{}, schemas.T_GatewayPerformanceStatistics},
	{schemas.GatewayLogMessage{}, schemas.T_GatewayLogMessage},
	{schemas.FaultLogs{}, schemas.T_FaultLogs},
	{schemas.LogMonitorReset{}, schemas.T_LogMonitorReset},
	{schemas.LogPreviousFail{}, schemas.T_logPreviousFail},
	{schemas.LogACLineEvent{}, schemas.T_logACLineEvent},
	{schemas.LogFaultSignalSequence{}, schemas.T_logFaultSignalSequence},
	{schemas.LogConfiguration{}, schemas.T_logConfiguration},
	{schemas.NotificationMessages{}, schemas.T_NotificationMessages},
	{schemas.BatchPerformanceStats{}, schemas.T_BatchPerformanceStats},
	{schemas.InviteEvent{}, schemas.T_InviteEvents},
}

// Helper function (used by the unit tests) that ensures that all registered
// table/struct pairings are valid.  This exists so that the public
// NewDefault() function can be used cleanly.
func newDefault(exec connect.BigQueryExecutorInterface, psClient connect.PsClient) (Batcher, error) {
	b := New(exec, psClient)

	defaultConfig := QueueConfig{
		MaxSize:              1000,
		FlushInterval:        1 * time.Second,
		MetricsFlushInterval: defaultMetricsFlushInterval,
		MaxConcurrency:       defaultMaxConcurrency,
		MaxQueueDepth:        5000, // 5x flush size for reasonable buffering
	}

	for _, q := range queues {
		// Errors are ignored because a unit test has already verified that all
		// the default table/struct pairings are valid and also because the module
		// will fail on init() if any of the default table/struct pairings are
		// invalid.
		_ = b.Register(q.rowExample, q.table, defaultConfig)
	}

	return b, nil
}

// New returns an empty batcher; you must Register() at least once.
func New(exec connect.BigQueryExecutorInterface, psClient connect.PsClient) Batcher {
	return &batcher{
		exec:        exec,
		psClient:    psClient,
		cfgs:        make(map[string]QueueConfig),
		queues:      make(map[string]*queue),
		typeToTable: make(map[reflect.Type]string),
	}
}

// Helper function that validates that the provided type is valid for use in
// the batcher.
func getValidType(t any) (reflect.Type, bigquery.Schema, error) {
	// Figure out the underlying struct type.
	tType := reflect.TypeOf(t)
	if tType == nil {
		return nil, nil, ErrNotStruct
	}
	if tType.Kind() == reflect.Ptr {
		tType = tType.Elem()
	}
	if tType.Kind() != reflect.Struct {
		return nil, nil, ErrNotStruct
	}

	// Verify that the schema can be inferred.
	schema, err := bigquery.InferSchema(t)
	if err != nil {
		return nil, nil, err
	}
	return tType, schema, nil
}

// Register ties a Go struct type (T or *T) to a BigQuery table name and config.
// This is a required step before you can Add() rows.
// The rowExample parameter is used to infer the schema of the table.
func (b *batcher) Register(rowExample any, table string, cfg QueueConfig) error {
	t, schema, err := getValidType(rowExample)
	if err != nil {
		return err
	}

	b.mu.Lock()
	defer b.mu.Unlock()

	if b.shutdown {
		return ErrBatcherIsShutDown
	}
	if _, exists := b.typeToTable[t]; exists {
		return fmt.Errorf("type %s already registered", t)
	}

	// Validate MaxQueueDepth
	if cfg.MaxQueueDepth < 0 {
		return fmt.Errorf("MaxQueueDepth cannot be negative: %d", cfg.MaxQueueDepth)
	}

	// Warn if MaxQueueDepth is less than MaxSize
	if cfg.MaxQueueDepth > 0 && cfg.MaxQueueDepth < cfg.MaxSize {
		logger.Warnf("MaxQueueDepth (%d) is less than MaxSize (%d) for table %s. This may cause frequent blocking.",
			cfg.MaxQueueDepth, cfg.MaxSize, table)
	}

	// Remember the mapping.
	b.typeToTable[t] = table
	b.cfgs[table] = cfg

	// Create the queue.
	b.queues[table] = newQueue(b.exec, b.psClient, table, cfg, schema, b)
	return nil
}

func (b *batcher) Add(row any) error {
	// Figure out the underlying struct type.
	t := reflect.TypeOf(row)
	if t == nil {
		return ErrNotStruct
	}
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	if t.Kind() != reflect.Struct {
		return ErrNotStruct
	}

	// Lock the *batcher*.
	b.mu.Lock()

	// Validate the state of the batcher and whether the type is registered.
	if b.shutdown {
		b.mu.Unlock()
		return ErrBatcherIsShutDown
	}
	table, ok := b.typeToTable[t]
	if !ok {
		b.mu.Unlock()
		return ErrUnknownType
	}
	q, ok := b.queues[table]
	b.mu.Unlock()
	if !ok {
		return fmt.Errorf("%w (%s)", ErrUnknownType, table)
	}
	// Batch lock is no longer held.

	// Check if queue depth limiting is enabled
	if q.maxDepth > 0 {
		// Block if queue is at capacity
		q.depthMu.Lock()
		for atomic.LoadInt32(&q.currentDepth) >= int32(q.maxDepth) {
			// Check if batcher is shutting down
			if b.shutdown {
				q.depthMu.Unlock()
				return ErrBatcherIsShutDown
			}
			q.depthCond.Wait()
		}
		q.depthMu.Unlock()
	}

	// Increment depth counter
	atomic.AddInt32(&q.currentDepth, 1)

	// Lock the *queue*.
	q.mu.Lock()

	// Append the row to the queue.
	q.rows = append(q.rows, &bigquery.StructSaver{
		Struct: row,
		Schema: q.schema,
	})

	// Check if we need to flush.
	sz := len(q.rows)
	q.mu.Unlock()
	if sz >= q.cfg.MaxSize {
		q.flushAsync()
	}

	return nil
}

// Shutdown signals all queue workers to flush and stop.
func (b *batcher) Shutdown() error {
	b.mu.Lock()
	if b.shutdown {
		b.mu.Unlock()
		return ErrBatcherIsShutDown
	}
	b.shutdown = true
	// Kick off all the workers to flush.
	for _, q := range b.queues {
		q.cancel()
		// Wake up any blocked Add operations
		if q.maxDepth > 0 {
			q.depthCond.Broadcast()
		}
	}
	b.mu.Unlock()

	// Wait for them all.
	for _, q := range b.queues {
		q.wg.Wait()
	}

	return nil
}

// newQueue initializes a queue and starts its worker.
func newQueue(exec connect.BigQueryExecutorInterface, psClient connect.PsClient, table string, cfg QueueConfig, schema bigquery.Schema, batcher *batcher) *queue {
	// ctrlCtx controls the worker's lifecycle
	ctrlCtx, cancel := context.WithCancel(exec.GetContext())
	// loadCtx is the "root" context for BQ operations (loader.Run / job.Wait)
	loadCtx := exec.GetContext()

	q := &queue{
		table:    table,
		cfg:      cfg,
		schema:   schema,
		exec:     exec,
		psClient: psClient,
		batcher:  batcher,
		ctrlCtx:  ctrlCtx,
		cancel:   cancel,
		loadCtx:  loadCtx,
		rows:     make([]bigquery.ValueSaver, 0, cfg.MaxSize),
		metrics: schemas.BatchPerformanceStats{
			Table: table,
		},
		maxDepth: cfg.MaxQueueDepth,
	}
	q.depthCond = sync.NewCond(&q.depthMu)
	q.wg.Add(1)
	go q.worker()
	return q
}

// worker handles timed flushes and shutdown.
func (q *queue) worker() {
	defer q.wg.Done()
	ticker := time.NewTicker(q.cfg.FlushInterval)

	// Ensure metrics flush interval is positive
	metricsInterval := q.cfg.MetricsFlushInterval
	if metricsInterval <= 0 {
		metricsInterval = defaultMetricsFlushInterval
	}
	statTicker := time.NewTicker(metricsInterval)
	defer ticker.Stop()
	defer statTicker.Stop()

	for {
		select {
		case <-ticker.C:
			q.flushAsync()
		case <-statTicker.C:
			q.flushMetrics()
		case <-q.ctrlCtx.Done():
			q.flushSync()
			q.flushMetrics()
			return
		}
	}
}

// flushAsync triggers a non-blocking flush.
func (q *queue) flushAsync() {
	// copy rows under lock
	q.mu.Lock()
	if len(q.rows) == 0 {
		q.mu.Unlock()
		return
	}
	batch := make([]bigquery.ValueSaver, len(q.rows))
	copy(batch, q.rows)
	q.rows = q.rows[:0]
	q.mu.Unlock()

	// run load in goroutine
	go q.load(batch)
}

// flushSync does a blocking flush (for shutdown).
func (q *queue) flushSync() {
	q.mu.Lock()
	if len(q.rows) == 0 {
		q.mu.Unlock()
		return
	}
	batch := make([]bigquery.ValueSaver, len(q.rows))
	copy(batch, q.rows)
	q.rows = q.rows[:0]
	q.mu.Unlock()

	q.load(batch)
}

// load performs the BigQuery streaming insert for the given rows.
func (q *queue) load(rows []bigquery.ValueSaver) error {
	// Track total processing time (everything before BigQuery operations)
	processingStart := time.Now()

	// Get the table name in the correct namespace
	tablename := connect.CombineTableNamespace(q.exec.GetConfig().Namespace, q.table)

	// BigQuery streaming insert limits:
	// - Max 50,000 rows per request (we use 500 for optimal performance per Google's recommendation)
	// - Max 10 MB per HTTP request (we use 8 MB to be safe)
	const MaxStreamingRows = 500              // Google's recommended batch size
	const MaxStreamingBytes = 7 * 1024 * 1024 // 7 MB (under 10 MB limit)

	// Split by BOTH row count AND size to respect BigQuery's limits
	batches := splitRowsBatchBySizeAndCount(rows, MaxStreamingBytes, MaxStreamingRows)
	totalSplits := len(batches) - 1 // Number of splits = number of batches - 1

	if len(batches) > 1 {
		logger.Debugf("Processing %d streaming sub-batches in parallel for table %s", len(batches), q.table)
	}

	// End processing time measurement (includes splitting)
	processingTime := time.Since(processingStart)

	// Track BigQuery loading time (wall-clock time for parallel operations)
	loadingStart := time.Now()

	// Process each sub-batch and collect metrics
	successCount := 0
	totalDLQMessages := 0
	totalDLQBytes := 0

	// Process sub-batches in parallel with a concurrency limit
	concurrencyLimit := q.cfg.MaxConcurrency
	if concurrencyLimit <= 0 {
		concurrencyLimit = defaultMaxConcurrency // Use global default
	}
	semaphore := make(chan struct{}, concurrencyLimit)

	// Use a WaitGroup to wait for all sub-batches to complete
	var wg sync.WaitGroup
	results := make([]struct {
		dlqMessages int
		dlqBytes    int
		err         error
		index       int
	}, len(batches))

	for i, batch := range batches {
		wg.Add(1)
		go func(batchIndex int, batch []bigquery.ValueSaver) {
			defer wg.Done()

			// Acquire semaphore to limit concurrency
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// Normal loads always send to DLQ on failure
			err := q.streamingInsertSubBatch(batch, tablename, true)
			// Log retry attempts with context
			if err != nil {
				logger.Warnf("BqBatch streaming insert failed sub-batch %d/%d for table %s, error: %v",
					batchIndex+1, len(batches), tablename, err)
			}

			// Calculate DLQ metrics based on error (normal loads always send to DLQ)
			dlqMessages := 0
			dlqBytes := 0
			if err != nil {
				dlqMessages = 1
				// Estimate bytes for DLQ metrics (convert rows to JSON for size estimation)
				estimatedBytes := estimateRowsSize(batch)
				dlqBytes = estimatedBytes
			}

			// Store results in thread-safe manner
			results[batchIndex] = struct {
				dlqMessages int
				dlqBytes    int
				err         error
				index       int
			}{dlqMessages, dlqBytes, err, batchIndex}
		}(i, batch)
	}

	// Wait for all sub-batches to complete
	wg.Wait()
	var lastError error

	// Process results in order
	totalRetries := 0
	for _, result := range results {
		if result.err != nil {
			logger.Errorf("Failed to stream sub-batch %d/%d: %v", result.index+1, len(batches), result.err)
			lastError = result.err

			// Log failure with DLQ context
			if result.dlqMessages > 0 {
				logger.Errorf("BqBatch streaming failed after %d retries for sub-batch %d/%d. Sent to DLQ (%d messages, %d bytes)",
					MaxAttempts, result.index+1, len(batches), result.dlqMessages, result.dlqBytes)
			} else {
				logger.Errorf("BqBatch streaming failed after %d retries for sub-batch %d/%d. Not sending to DLQ.",
					MaxAttempts, result.index+1, len(batches))
			}

			totalDLQMessages += result.dlqMessages
			totalDLQBytes += result.dlqBytes
			totalRetries += MaxAttempts // Each failed sub-batch attempted all retries
			// Continue with other batches even if one fails
		} else {
			successCount++
		}
	}

	loadingTime := time.Since(loadingStart)
	totalTime := loadingTime + processingTime

	// Handle nil error case
	lastErrorString := ""
	if lastError != nil {
		lastErrorString = lastError.Error()
	}

	// Record batch performance stats
	totalBytes := estimateRowsSize(rows)

	// Create and add performance stat record
	// Note: We only call updateMetrics once per batch operation, not per sub-batch
	// This ensures we get the correct wall-clock timing for parallel processing
	stat := schemas.BatchPerformanceStats{
		Table:           q.table,
		Timestamp:       time.Now().UTC(),
		BatchSize:       len(rows),
		BatchBytes:      totalBytes,
		ProcessingMs:    processingTime.Milliseconds(), // Splitting time
		LoadingMs:       loadingTime.Milliseconds(),    // Wall-clock time for BigQuery operations
		TotalDurationMs: totalTime.Milliseconds(),      // Total wall-clock time
		Retries:         totalRetries,                  // Sum of retries from failed sub-batches
		Error:           lastErrorString,
		Splits:          totalSplits,
		DLQMessages:     totalDLQMessages,
		DLQBytes:        totalDLQBytes,
		CurrentDepth:    atomic.LoadInt32(&q.currentDepth), // Current queue depth when metrics were recorded
	}
	q.updateMetrics(stat)

	// Decrement depth counter and signal waiters if depth limiting is enabled
	// This is done at the very end of the load operation, after all processing is complete
	if q.maxDepth > 0 {
		atomic.AddInt32(&q.currentDepth, -int32(len(rows)))
		q.depthCond.Broadcast()
	}

	// Return the last error if there is one, used to nack DLQ batches
	return lastError
}

// streamingInsertSubBatch performs the BigQuery streaming insert for the given rows
func (q *queue) streamingInsertSubBatch(rows []bigquery.ValueSaver, tablename string, sendToDLQ bool) error {
	// Get BigQuery dataset and table
	client := q.exec.GetClient()
	dataset := client.Dataset(q.exec.GetConfig().DBName)

	// Handle test environment where dataset may be nil
	if dataset == nil {
		logger.Debugf("Simulating streaming insert for %d rows in test environment", len(rows))
		return nil
	}

	table := dataset.Table(tablename)
	inserter := table.Inserter()

	// Convert ValueSaver slice to interface slice for streaming insert
	var streamingRows []interface{}
	for _, row := range rows {
		// Extract the underlying struct from StructSaver
		if structSaver, ok := row.(*bigquery.StructSaver); ok {
			streamingRows = append(streamingRows, structSaver.Struct)
		} else {
			streamingRows = append(streamingRows, row)
		}
	}

	// Perform streaming insert with retry
	var lastErr error
	for i := range MaxAttempts {
		err := inserter.Put(q.loadCtx, streamingRows)
		if err != nil {
			lastErr = fmt.Errorf("streaming insert failed: %w", err)
		} else {
			return nil // success
		}

		// exponential backoff
		logger.Warnf("BqBatch streaming insert failed for table %s, retrying in %d seconds, error: %v",
			tablename, 1<<i, lastErr)
		timeSleep(1 << i * time.Second)
	}

	// Only send to DLQ if requested
	if sendToDLQ {
		// Convert rows back to JSONL for DLQ compatibility
		rawData, err := convertRowsToJSONL(rows)
		if err != nil {
			logger.Errorf("Failed to convert rows to JSONL for DLQ: %v", err)
		} else {
			// Send raw data to DLQ
			if err := q.sendRawDLQMessage(rawData, q.table, lastErr.Error()); err != nil {
				logger.Errorf("Failed to send raw DLQ message: %v", err)
			}
		}
	}

	return lastErr
}

// loadSubBatch performs the BigQuery batch load for raw JSONL data
// Returns error only
func (q *queue) loadSubBatch(rawData []byte, tablename string, sendToDLQ bool) error {
	// set up reader source
	rs := bigquery.NewReaderSource(bytes.NewReader(rawData))
	rs.SourceFormat = bigquery.JSON
	rs.Schema = q.schema // Use the schema to properly map JSON fields to BigQuery columns

	// configure loader
	dataset := q.exec.GetClient().Dataset(q.exec.GetConfig().DBName)

	// Handle test environment where dataset may be nil
	if dataset == nil {
		logger.Debugf("Simulating load batch for %d bytes in test environment", len(rawData))
		return nil
	}

	t := dataset.Table(tablename)
	loader := t.LoaderFrom(rs)
	loader.WriteDisposition = bigquery.WriteAppend

	// run job with retry
	var lastErr error
	for i := range MaxAttempts {
		job, err := loader.Run(q.loadCtx)
		if err != nil {
			lastErr = fmt.Errorf("loader.Run failed: %w", err)
		} else {
			status, err := job.Wait(q.loadCtx)
			if err != nil {
				lastErr = fmt.Errorf("job.Wait failed: %w", err)
			} else if statusErr := status.Err(); statusErr != nil {
				msg := fmt.Sprintf("BQ job status error: %v", statusErr)
				for _, err := range status.Errors {
					msg += fmt.Sprintf("BQ row error: %s (%s)", err.Message, err.Reason)
				}
				lastErr = fmt.Errorf("%s", msg)
			} else {
				return nil // success
			}
		}

		// exponential backoff
		logger.Warnf("BqBatch load failed for table %s, retrying in %d seconds, error: %v",
			tablename, 1<<i, lastErr)
		timeSleep(1 << i * time.Second)
	}

	// Only send to DLQ if requested
	if sendToDLQ {
		// Send raw data to DLQ
		if err := q.sendRawDLQMessage(rawData, q.table, lastErr.Error()); err != nil {
			logger.Errorf("Failed to send raw DLQ message: %v", err)
		}
	}

	return lastErr
}

// sendRawDLQMessage sends raw JSON bytes to the PubSub DLQ topic with metadata in attributes
// If the data exceeds MaxPubSubMessageSize, it splits the batch into smaller chunks
func (q *queue) sendRawDLQMessage(rawData []byte, table string, errorMsg string) error {
	// Check if we need to split the batch
	if len(rawData) > MaxPubSubMessageSize {
		logger.Warnf("DLQ batch size (%d bytes) exceeds PubSub limit (%d bytes) for table %s, splitting into chunks",
			len(rawData), MaxPubSubMessageSize, table)
		return q.sendRawDLQMessageSplit(rawData, table, errorMsg)
	}

	topic := q.psClient.Topic(PubsubDLQTopic)

	// Build proper attributes using the standard format
	commonAttrs := pubsubdata.CommonAttributes{
		Topic:     pubsubdata.TopicDLQBQBatch,
		DLQReason: errorMsg,
	}
	headerDetails := pubsubdata.HeaderDetails{}
	attrs := pubsubdata.BuildAttributes(commonAttrs, headerDetails)

	// Add DLQ-specific attributes
	attrs["table"] = table
	attrs["format"] = "raw_jsonl" // Indicates this is raw JSONL data, not a FailedBatch wrapper

	for i := range MaxAttempts {
		res := topic.Publish(q.loadCtx, &pubsub.Message{
			Data:       rawData,
			Attributes: attrs,
		})
		_, err := res.Get(q.loadCtx)
		if err == nil {
			return nil
		}

		// exponential backoff
		logger.Warnf("BqBatch backup pubsub DLQ failed for table %s, retrying in %d seconds, error: %v",
			table, 1<<i, err)
		timeSleep(1 << i * time.Second)
	}

	logger.Errorf("BqBatch failed to send to DLQ backup for table %s after %d retries. Data size: %d bytes. Data lost.",
		table, MaxAttempts, len(rawData))
	return fmt.Errorf("failed to send raw DLQ message after %d retries", MaxAttempts)
}

// sendRawDLQMessageSplit splits large JSONL data into smaller chunks and sends each to DLQ
func (q *queue) sendRawDLQMessageSplit(rawData []byte, table string, errorMsg string) error {
	// Split JSONL by lines
	lines := bytes.Split(rawData, []byte("\n"))

	// Filter out empty lines
	var validLines [][]byte
	for _, line := range lines {
		if len(line) > 0 {
			validLines = append(validLines, line)
		}
	}

	if len(validLines) == 0 {
		return fmt.Errorf("no valid lines to send to DLQ")
	}

	logger.Infof("Splitting %d rows into smaller DLQ chunks for table %s", len(validLines), table)

	// Build chunks that fit within PubSub size limit
	var chunks [][]byte
	var currentChunk []byte

	for _, line := range validLines {
		// Check if adding this line would exceed the limit
		testSize := len(currentChunk) + len(line) + 1 // +1 for newline
		if testSize > MaxPubSubMessageSize && len(currentChunk) > 0 {
			// Save current chunk and start a new one
			chunks = append(chunks, currentChunk)
			currentChunk = append([]byte{}, line...)
			currentChunk = append(currentChunk, '\n')
		} else {
			currentChunk = append(currentChunk, line...)
			currentChunk = append(currentChunk, '\n')
		}
	}

	// Don't forget the last chunk
	if len(currentChunk) > 0 {
		chunks = append(chunks, currentChunk)
	}

	logger.Infof("Split DLQ data into %d chunks for table %s", len(chunks), table)

	// Send each chunk
	topic := q.psClient.Topic(PubsubDLQTopic)
	var lastError error
	successCount := 0

	for chunkIdx, chunk := range chunks {
		// Build proper attributes using the standard format
		commonAttrs := pubsubdata.CommonAttributes{
			Topic:     pubsubdata.TopicDLQBQBatch,
			DLQReason: fmt.Sprintf("%s (chunk %d/%d)", errorMsg, chunkIdx+1, len(chunks)),
		}
		headerDetails := pubsubdata.HeaderDetails{}
		attrs := pubsubdata.BuildAttributes(commonAttrs, headerDetails)

		// Add DLQ-specific attributes
		attrs["table"] = table
		attrs["format"] = "raw_jsonl"
		attrs["chunk_index"] = fmt.Sprintf("%d", chunkIdx)
		attrs["total_chunks"] = fmt.Sprintf("%d", len(chunks))

		// Retry this chunk
		chunkSent := false
		for i := range MaxAttempts {
			res := topic.Publish(q.loadCtx, &pubsub.Message{
				Data:       chunk,
				Attributes: attrs,
			})
			_, err := res.Get(q.loadCtx)
			if err == nil {
				chunkSent = true
				break
			}

			lastError = err
			// exponential backoff
			logger.Warnf("BqBatch backup pubsub DLQ failed for table %s chunk %d/%d, retrying in %d seconds, error: %v",
				table, chunkIdx+1, len(chunks), 1<<i, err)
			timeSleep(1 << i * time.Second)
		}

		if chunkSent {
			successCount++
		} else {
			logger.Errorf("Failed to send DLQ chunk %d/%d for table %s after %d retries",
				chunkIdx+1, len(chunks), table, MaxAttempts)
		}
	}

	if successCount == 0 {
		logger.Errorf("BqBatch failed to send any DLQ chunks to backup for table %s after %d retries. Total data size: %d bytes. Data lost.",
			table, MaxAttempts, len(rawData))
		return fmt.Errorf("failed to send any DLQ chunks after %d retries: %w", MaxAttempts, lastError)
	}

	if successCount < len(chunks) {
		logger.Warnf("Sent %d/%d DLQ chunks successfully for table %s", successCount, len(chunks), table)
		return fmt.Errorf("sent %d/%d chunks, last error: %w", successCount, len(chunks), lastError)
	}

	logger.Infof("Successfully sent all %d DLQ chunks for table %s", len(chunks), table)
	return nil
}

// LoadBatch loads raw JSONL data directly into BigQuery for the specified table
func (b *batcher) LoadBatch(table string, rawData []byte) error {
	q, ok := b.queues[table]
	if !ok {
		return fmt.Errorf("unknown table: %s", table)
	}

	// Get the table name in the correct namespace
	tablename := connect.CombineTableNamespace(q.exec.GetConfig().Namespace, table)

	logger.Debugf("Loading table data for %s with %d bytes", table, len(rawData))

	// Track total duration for metrics
	startTime := time.Now()

	// Use the unified loadSubBatch method
	err := q.loadSubBatch(rawData, tablename, false)

	// Calculate metrics
	totalTime := time.Since(startTime)

	// Handle nil error case
	errorString := ""
	retries := 0
	if err != nil {
		errorString = err.Error()
		retries = MaxAttempts // If there's an error, it means all retries were attempted
	}

	// Create and add performance stat record for LoadTableData
	stat := schemas.BatchPerformanceStats{
		Table:           table,
		Timestamp:       time.Now().UTC(),
		BatchSize:       0, // Unknown for raw data
		BatchBytes:      len(rawData),
		ProcessingMs:    0,                        // No pre-serialization or splitting
		LoadingMs:       totalTime.Milliseconds(), // Total time (includes retries)
		TotalDurationMs: totalTime.Milliseconds(),
		Retries:         retries, // MaxAttempts if failed, 0 if succeeded
		Error:           errorString,
		Splits:          0, // No batch splitting
		DLQMessages:     0, // Should be 0 for DLQ reloads
		DLQBytes:        0,
		CurrentDepth:    atomic.LoadInt32(&q.currentDepth), // Current queue depth when metrics were recorded
	}
	q.updateMetrics(stat)

	return err
}

// Update performance metrics after each batch load
func (q *queue) updateMetrics(stat schemas.BatchPerformanceStats) {
	q.metricsMu.Lock()
	defer q.metricsMu.Unlock()
	m := &q.metrics
	m.BatchSize += stat.BatchSize
	m.BatchBytes += stat.BatchBytes
	m.ProcessingMs += stat.ProcessingMs

	m.LoadingMs += stat.LoadingMs
	m.TotalDurationMs += stat.TotalDurationMs

	m.Retries += stat.Retries
	m.Splits += stat.Splits
	m.DLQMessages += stat.DLQMessages
	m.DLQBytes += stat.DLQBytes

	// Update current depth with the latest value (not cumulative)
	m.CurrentDepth = stat.CurrentDepth

	if stat.Error != "" {
		m.Error = stat.Error
	}
	if m.Timestamp.IsZero() || stat.Timestamp.After(m.Timestamp) {
		m.Timestamp = stat.Timestamp
	}
}

// Flush the performance metrics to BigQuery if changed
func (q *queue) flushMetrics() {
	q.metricsMu.Lock()
	stat := q.metrics
	q.metricsMu.Unlock()

	// Only flush if metrics have changed (nonzero)
	if stat.BatchSize == 0 && stat.BatchBytes == 0 && stat.ProcessingMs == 0 && stat.LoadingMs == 0 && stat.TotalDurationMs == 0 && stat.Retries == 0 && stat.Splits == 0 && stat.DLQMessages == 0 && stat.DLQBytes == 0 && stat.CurrentDepth == 0 {
		return
	}

	// Add metrics to BigQuery via batcher.Add()
	_ = q.batcher.Add(stat)

	// Reset metrics
	q.metricsMu.Lock()
	q.metrics = schemas.BatchPerformanceStats{Table: q.table}
	q.metricsMu.Unlock()
}
