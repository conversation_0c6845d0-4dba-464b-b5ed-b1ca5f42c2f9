package bqbatch

import (
	"bytes"
	"encoding/json"

	"cloud.google.com/go/bigquery"
)

// preSerializeRows serializes each row once and returns PreSerializedRow slices
func preSerializeRows(rows []bigquery.ValueSaver) ([]PreSerializedRow, error) {
	var result []PreSerializedRow
	for _, r := range rows {
		rowMap, _, err := r.Save()
		if err != nil {
			return nil, err
		}
		jsonBytes, err := json.Marshal(rowMap)
		if err != nil {
			return nil, err
		}

		result = append(result, PreSerializedRow{
			JSONBytes: jsonBytes,
			Size:      len(jsonBytes) + 1, // +1 for newline in JSONL
		})
	}
	return result, nil
}

// splitBatch splits a batch of pre-serialized rows into sub-batches that fit within size limits
func splitBatch(rows []PreSerializedRow, maxSize int) [][]PreSerializedRow {
	var batches [][]PreSerializedRow
	var currentBatch []PreSerializedRow
	var currentSize int
	for _, row := range rows {
		if currentSize+row.Size > maxSize && len(currentBatch) > 0 {
			batches = append(batches, currentBatch)
			currentBatch = []PreSerializedRow{row}
			currentSize = row.Size
		} else {
			currentBatch = append(currentBatch, row)
			currentSize += row.Size
		}
	}
	if len(currentBatch) > 0 {
		batches = append(batches, currentBatch)
	}
	return batches
}

// splitRowsBatchBySizeAndCount splits rows by BOTH size and count limits to respect BigQuery streaming insert constraints
func splitRowsBatchBySizeAndCount(rows []bigquery.ValueSaver, maxBytes int, maxRows int) [][]bigquery.ValueSaver {
	var batches [][]bigquery.ValueSaver
	var currentBatch []bigquery.ValueSaver
	var currentSize int

	for _, row := range rows {
		// Estimate this row's size
		rowMap, _, err := row.Save()
		if err != nil {
			// If we can't serialize the row, skip it (will fail later during actual insert)
			continue
		}
		jsonBytes, err := json.Marshal(rowMap)
		if err != nil {
			continue
		}
		rowSize := len(jsonBytes)

		// Check if adding this row would exceed either limit
		wouldExceedSize := currentSize+rowSize > maxBytes
		wouldExceedCount := len(currentBatch) >= maxRows

		if (wouldExceedSize || wouldExceedCount) && len(currentBatch) > 0 {
			// Start a new batch
			batches = append(batches, currentBatch)
			currentBatch = []bigquery.ValueSaver{row}
			currentSize = rowSize
		} else {
			// Add to current batch
			currentBatch = append(currentBatch, row)
			currentSize += rowSize
		}
	}

	// Don't forget the last batch
	if len(currentBatch) > 0 {
		batches = append(batches, currentBatch)
	}

	return batches
}

// estimateRowsSize estimates the total byte size of rows by converting them to JSON
func estimateRowsSize(rows []bigquery.ValueSaver) int {
	totalSize := 0
	for _, row := range rows {
		rowMap, _, err := row.Save()
		if err != nil {
			continue // Skip rows that can't be serialized for size estimation
		}
		jsonBytes, err := json.Marshal(rowMap)
		if err != nil {
			continue
		}
		totalSize += len(jsonBytes) + 1 // +1 for newline
	}
	return totalSize
}

// convertRowsToJSONL converts ValueSaver rows to JSONL format for DLQ compatibility
func convertRowsToJSONL(rows []bigquery.ValueSaver) ([]byte, error) {
	var buf bytes.Buffer
	for _, row := range rows {
		rowMap, _, err := row.Save()
		if err != nil {
			return nil, err
		}
		jsonBytes, err := json.Marshal(rowMap)
		if err != nil {
			return nil, err
		}
		buf.Write(jsonBytes)
		buf.WriteByte('\n')
	}
	return buf.Bytes(), nil
}
