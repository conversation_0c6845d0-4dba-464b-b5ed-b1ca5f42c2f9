package dashboard

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestGetEDIInitialState tests the getEDIInitialState function with comprehensive table-driven tests
func TestGetEDIInitialState(t *testing.T) {
	t.<PERSON>() // Enable parallel execution of this test function
	tests := []struct {
		name     string
		device   pgDeviceResult
		expected DeviceState
	}{
		{
			name: "disabled_device",
			device: pgDeviceResult{
				IsEnabled: false,
			},
			expected: DeviceStateNeverComm,
		},
		{
			name: "enabled_device",
			device: pgDeviceResult{
				IsEnabled: true,
			},
			expected: DeviceStateError,
		},
	}

	for _, tt := range tests {
		tt := tt // capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.<PERSON>() // Enable subtests to run in parallel
			result := getEDIInitialState(tt.device)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestConvertPgLocationResults tests the convertPgLocationResults function with comprehensive table-driven tests
func TestConvertPgLocationResults(t *testing.T) {
	t.<PERSON>() // Enable parallel execution of this test function
	tests := []struct {
		name           string
		pgLocations    *[]pgLocationResult
		expectedResult []LocationInfo
	}{
		{
			name:           "nil_input",
			pgLocations:    nil,
			expectedResult: []LocationInfo{},
		},
		{
			name:           "empty_input",
			pgLocations:    &[]pgLocationResult{},
			expectedResult: []LocationInfo{},
		},
		{
			name: "single_location",
			pgLocations: &[]pgLocationResult{
				{
					ID:        "550e8400-e29b-41d4-a716-446655440000",
					Name:      "Test Location",
					Latitude:  "40.7128",
					Longitude: "-74.0060",
				},
			},
			expectedResult: []LocationInfo{
				{
					ID:        "550e8400-e29b-41d4-a716-446655440000",
					Name:      "Test Location",
					Latitude:  "40.7128",
					Longitude: "-74.0060",
				},
			},
		},
		{
			name: "multiple_locations",
			pgLocations: &[]pgLocationResult{
				{
					ID:        "550e8400-e29b-41d4-a716-446655440000",
					Name:      "Test Location 1",
					Latitude:  "40.7128",
					Longitude: "-74.0060",
				},
				{
					ID:        "550e8400-e29b-41d4-a716-446655440001",
					Name:      "Test Location 2",
					Latitude:  "40.7129",
					Longitude: "-74.0061",
				},
			},
			expectedResult: []LocationInfo{
				{
					ID:        "550e8400-e29b-41d4-a716-446655440000",
					Name:      "Test Location 1",
					Latitude:  "40.7128",
					Longitude: "-74.0060",
				},
				{
					ID:        "550e8400-e29b-41d4-a716-446655440001",
					Name:      "Test Location 2",
					Latitude:  "40.7129",
					Longitude: "-74.0061",
				},
			},
		},
	}

	for _, tt := range tests {
		tt := tt // capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel() // Enable subtests to run in parallel
			result := convertPgLocationResults(tt.pgLocations)
			assert.Equal(t, tt.expectedResult, result)
		})
	}
}

// TestConvertPgDeviceInfos tests the convertPgDeviceInfos function with comprehensive table-driven tests
func TestConvertPgDeviceInfos(t *testing.T) {
	t.Parallel() // Enable parallel execution of this test function
	tests := []struct {
		name           string
		dbInfo         *[]pgDeviceResult
		expectedResult *[]dataPayload
	}{
		{
			name:           "nil_input",
			dbInfo:         nil,
			expectedResult: nil,
		},
		{
			name:           "empty_input",
			dbInfo:         &[]pgDeviceResult{},
			expectedResult: &[]dataPayload{},
		},
		{
			name: "disabled_device",
			dbInfo: &[]pgDeviceResult{
				{
					DeviceID:   "device-123",
					LocationID: "location-123",
					IsEnabled:  false,
				},
			},
			expectedResult: &[]dataPayload{
				{
					DeviceIdentifier: "device-123",
					LocationID:       "location-123",
					Status: status{
						State: DeviceStateNeverComm,
					},
				},
			},
		},
		{
			name: "enabled_device",
			dbInfo: &[]pgDeviceResult{
				{
					DeviceID:   "device-456",
					LocationID: "location-456",
					IsEnabled:  true,
				},
			},
			expectedResult: &[]dataPayload{
				{
					DeviceIdentifier: "device-456",
					LocationID:       "location-456",
					Status: status{
						State: DeviceStateError,
					},
				},
			},
		},
		{
			name: "multiple_devices",
			dbInfo: &[]pgDeviceResult{
				{
					DeviceID:   "device-1",
					LocationID: "location-1",
					IsEnabled:  false,
				},
				{
					DeviceID:   "device-2",
					LocationID: "location-2",
					IsEnabled:  true,
				},
			},
			expectedResult: &[]dataPayload{
				{
					DeviceIdentifier: "device-1",
					LocationID:       "location-1",
					Status: status{
						State: DeviceStateNeverComm,
					},
				},
				{
					DeviceIdentifier: "device-2",
					LocationID:       "location-2",
					Status: status{
						State: DeviceStateError,
					},
				},
			},
		},
	}

	for _, tt := range tests {
		tt := tt // capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel() // Enable subtests to run in parallel
			result := convertPgDeviceInfos(tt.dbInfo)
			assert.Equal(t, tt.expectedResult, result)
		})
	}
}
