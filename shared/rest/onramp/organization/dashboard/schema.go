package dashboard

// DeviceState represents the state of a device using enum-style pattern
type DeviceState string

// Device state enum values
const (
	// DeviceStateNoFault indicates the device is operating normally without any faults
	DeviceStateNoFault DeviceState = "nofault"

	// DeviceStateFaulted indicates the device has detected a fault condition
	DeviceStateFaulted DeviceState = "faulted"

	// DeviceStateError indicates there's an error communicating with the device or processing its data
	DeviceStateError DeviceState = "error"

	// DeviceStateNeverComm indicates the device has never communicated or is disabled
	DeviceStateNeverComm DeviceState = "nevercomm"
)

// String implements the Stringer interface for DeviceState
func (ds DeviceState) String() string {
	return string(ds)
}

// DashboardResponse represents the top-level response for dashboard devices endpoint
type DashboardResponse struct {
	OrganizationID string         `json:"organizationId"` // Organization ID
	Locations      []LocationInfo `json:"locations"`      // List of locations in the organization
}

// LocationInfo contains location details
type LocationInfo struct {
	ID        string        `json:"id" example:"550e8400-e29b-41d4-a716-************"` // Location UUID
	Name      string        `json:"name" example:"Main Street Intersection"`           // Location name
	Latitude  string        `json:"lat" example:"40.7128"`                             // Location latitude
	Longitude string        `json:"long" example:"-74.0060"`                           // Location longitude
	Devices   []dataPayload `json:"devices"`                                           // List of devices with their status
}

// pgLocationResult represents a location record from PostgreSQL
type pgLocationResult struct {
	ID        string        `db:"id"`
	Name      string        `db:"name"`
	Latitude  string        `db:"latitude"`
	Longitude string        `db:"longitude"`
	Devices   []dataPayload `json:"devices"` // List of devices with their status
}

// pgDeviceResult represents a device record from PostgreSQL with all needed fields
type pgDeviceResult struct {
	ID         string `db:"id"`
	DeviceID   string `db:"deviceid"`
	DeviceType string `db:"devicetype"`
	LocationID string `db:"locationid"` // Internal use only - not exposed in API response
	IsEnabled  bool   `db:"isenabled"`
	GatewayID  string `db:"gatewayid"`
}

// dataPayload represents the complete device information response
type dataPayload struct {
	DeviceIdentifier string `json:"device_identifier" example:"device-uuid-123"` // Device UUID or identifier string
	LocationID       string `json:"-"`                                           // Location UUID this device belongs to (internal use only)
	Status           status `json:"status"`                                      // Current device status and fault information
}

// status represents the device status information
type status struct {
	State DeviceState `json:"state" example:"nofault"` // Current device state
}
