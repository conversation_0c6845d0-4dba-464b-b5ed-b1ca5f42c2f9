package dashboard

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/gateway/v1"
	"github.com/go-redis/redismock/v9"
	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	apiShared "synapse-its.com/shared/api"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/mocks/dbexecutor"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/rest/domain/auth"
)

// TestGetDashboardDevicesWithDeps tests the main handler function with comprehensive table-driven tests
func TestGetDashboardDevicesWithDeps(t *testing.T) {
	t.<PERSON>()
	orgID := uuid.MustParse("c469a554-f7a8-5de5-a57e-e1ba16f970d3")
	locationID := uuid.MustParse("39a67f33-5d98-5dcf-8951-94adcd3e2857")
	deviceID := uuid.MustParse("18b12d00-4f4b-5340-ae86-d415f3d4cf77")

	tests := []struct {
		name           string
		setupDeps      func() HandlerDeps
		setupRequest   func() *http.Request
		expectedStatus int
		expectedError  bool
	}{
		{
			name: "success_case",
			setupDeps: func() HandlerDeps {
				pgLocations := []pgLocationResult{
					{
						ID:        locationID.String(),
						Name:      "4th and Main",
						Latitude:  "33.75175770",
						Longitude: "-84.41253000",
					},
				}

				pgDevices := []pgDeviceResult{
					{
						ID:         deviceID.String(),
						DeviceID:   deviceID.String(),
						DeviceType: "traffic_light",
						LocationID: locationID.String(),
						IsEnabled:  false,
						GatewayID:  "gateway-123",
					},
				}

				rdb, mock := redismock.NewClientMock()
				mock.ExpectMGet().SetVal([]interface{}{nil})

				return HandlerDeps{
					GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
							Redis:    rdb,
						}, nil
					},
					GetLocations: func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]pgLocationResult, error) {
						return &pgLocations, nil
					},
					GetDevicesInfo: func(pg connect.DatabaseExecutor, userPermissions *authorizer.UserPermissions, orgId uuid.UUID) (*[]pgDeviceResult, []string, error) {
						return &pgDevices, []string{"GatewayRMSData:gateway-123"}, nil
					},
					GetRedisDeviceStatus: func(ctx context.Context, rd *redis.Client, deviceInfo *[]dataPayload, gatewayIds []string) (*[]dataPayload, error) {
						return deviceInfo, nil
					},
					ConvertLocations: func(pgLocations *[]pgLocationResult) []LocationInfo {
						return convertPgLocationResults(pgLocations)
					},
					ConvertDevices: func(pgDevices *[]pgDeviceResult) *[]dataPayload {
						return convertPgDeviceInfos(pgDevices)
					},
				}
			},
			setupRequest: func() *http.Request {
				req := httptest.NewRequest("GET", "/api/organizations/"+orgID.String()+"/dashboard/devices", nil)
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": orgID.String(),
				})
				userPermissions := &authorizer.UserPermissions{UserID: "test-user"}
				sessionData := &auth.Session{
					UserID:          "test-user",
					UserPermissions: userPermissions,
				}
				req = req.WithContext(context.WithValue(req.Context(), auth.SessionContextKey, sessionData))
				return req
			},
			expectedStatus: http.StatusOK,
			expectedError:  false,
		},
		{
			name: "unauthorized_user_permissions_error",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{}
			},
			setupRequest: func() *http.Request {
				req := httptest.NewRequest("GET", "/api/organizations/"+orgID.String()+"/dashboard/devices", nil)
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": orgID.String(),
				})
				return req
			},
			expectedStatus: http.StatusUnauthorized,
			expectedError:  true,
		},
		{
			name: "bad_request_invalid_organization_id",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{}
			},
			setupRequest: func() *http.Request {
				req := httptest.NewRequest("GET", "/api/organizations/invalid-uuid/dashboard/devices", nil)
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "invalid-uuid",
				})
				userPermissions := &authorizer.UserPermissions{UserID: "test-user"}
				sessionData := &auth.Session{
					UserID:          "test-user",
					UserPermissions: userPermissions,
				}
				req = req.WithContext(context.WithValue(req.Context(), auth.SessionContextKey, sessionData))
				return req
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  true,
		},
		{
			name: "internal_error_database_connection_failed",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
						return nil, assert.AnError
					},
				}
			},
			setupRequest: func() *http.Request {
				req := httptest.NewRequest("GET", "/api/organizations/"+orgID.String()+"/dashboard/devices", nil)
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": orgID.String(),
				})
				userPermissions := &authorizer.UserPermissions{UserID: "test-user"}
				sessionData := &auth.Session{
					UserID:          "test-user",
					UserPermissions: userPermissions,
				}
				req = req.WithContext(context.WithValue(req.Context(), auth.SessionContextKey, sessionData))
				return req
			},
			expectedStatus: http.StatusInternalServerError,
			expectedError:  true,
		},
		{
			name: "internal_error_locations_retrieval_failed",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetLocations: func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]pgLocationResult, error) {
						return nil, assert.AnError
					},
				}
			},
			setupRequest: func() *http.Request {
				req := httptest.NewRequest("GET", "/api/organizations/"+orgID.String()+"/dashboard/devices", nil)
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": orgID.String(),
				})
				userPermissions := &authorizer.UserPermissions{UserID: "test-user"}
				sessionData := &auth.Session{
					UserID:          "test-user",
					UserPermissions: userPermissions,
				}
				req = req.WithContext(context.WithValue(req.Context(), auth.SessionContextKey, sessionData))
				return req
			},
			expectedStatus: http.StatusInternalServerError,
			expectedError:  true,
		},
		{
			name: "internal_error_devices_retrieval_failed",
			setupDeps: func() HandlerDeps {
				pgLocations := []pgLocationResult{
					{
						ID:        locationID.String(),
						Name:      "Test Location",
						Latitude:  "0.0",
						Longitude: "0.0",
					},
				}
				return HandlerDeps{
					GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetLocations: func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]pgLocationResult, error) {
						return &pgLocations, nil
					},
					GetDevicesInfo: func(pg connect.DatabaseExecutor, userPermissions *authorizer.UserPermissions, orgId uuid.UUID) (*[]pgDeviceResult, []string, error) {
						return nil, nil, assert.AnError
					},
				}
			},
			setupRequest: func() *http.Request {
				req := httptest.NewRequest("GET", "/api/organizations/"+orgID.String()+"/dashboard/devices", nil)
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": orgID.String(),
				})
				userPermissions := &authorizer.UserPermissions{UserID: "test-user"}
				sessionData := &auth.Session{
					UserID:          "test-user",
					UserPermissions: userPermissions,
				}
				req = req.WithContext(context.WithValue(req.Context(), auth.SessionContextKey, sessionData))
				return req
			},
			expectedStatus: http.StatusInternalServerError,
			expectedError:  true,
		},
		{
			name: "success_with_redis_error_graceful_degradation",
			setupDeps: func() HandlerDeps {
				pgLocations := []pgLocationResult{
					{
						ID:        locationID.String(),
						Name:      "Test Location",
						Latitude:  "0.0",
						Longitude: "0.0",
					},
				}
				pgDevices := []pgDeviceResult{
					{
						ID:         deviceID.String(),
						DeviceID:   deviceID.String(),
						DeviceType: "traffic_light",
						LocationID: locationID.String(),
						IsEnabled:  true,
						GatewayID:  "gateway-1",
					},
				}
				return HandlerDeps{
					GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
						rdb, mock := redismock.NewClientMock()
						mock.ExpectMGet().SetErr(assert.AnError)
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
							Redis:    rdb,
						}, nil
					},
					GetLocations: func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]pgLocationResult, error) {
						return &pgLocations, nil
					},
					GetDevicesInfo: func(pg connect.DatabaseExecutor, userPermissions *authorizer.UserPermissions, orgId uuid.UUID) (*[]pgDeviceResult, []string, error) {
						return &pgDevices, []string{"GatewayRMSData:gateway-1"}, nil
					},
					GetRedisDeviceStatus: func(ctx context.Context, rd *redis.Client, deviceInfo *[]dataPayload, gatewayIds []string) (*[]dataPayload, error) {
						return nil, assert.AnError
					},
					ConvertLocations: func(pgLocations *[]pgLocationResult) []LocationInfo {
						return convertPgLocationResults(pgLocations)
					},
					ConvertDevices: func(pgDevices *[]pgDeviceResult) *[]dataPayload {
						return convertPgDeviceInfos(pgDevices)
					},
				}
			},
			setupRequest: func() *http.Request {
				req := httptest.NewRequest("GET", "/api/organizations/"+orgID.String()+"/dashboard/devices", nil)
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": orgID.String(),
				})
				userPermissions := &authorizer.UserPermissions{UserID: "test-user"}
				sessionData := &auth.Session{
					UserID:          "test-user",
					UserPermissions: userPermissions,
				}
				req = req.WithContext(context.WithValue(req.Context(), auth.SessionContextKey, sessionData))
				return req
			},
			expectedStatus: http.StatusOK,
			expectedError:  false,
		},
		{
			name: "success_with_empty_devices",
			setupDeps: func() HandlerDeps {
				pgLocations := []pgLocationResult{
					{
						ID:        locationID.String(),
						Name:      "4th and Main",
						Latitude:  "33.75175770",
						Longitude: "-84.41253000",
					},
				}
				pgDevices := []pgDeviceResult{}
				return HandlerDeps{
					GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
						rdb, mock := redismock.NewClientMock()
						mock.ExpectMGet().SetVal([]interface{}{})
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
							Redis:    rdb,
						}, nil
					},
					GetLocations: func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]pgLocationResult, error) {
						return &pgLocations, nil
					},
					GetDevicesInfo: func(pg connect.DatabaseExecutor, userPermissions *authorizer.UserPermissions, orgId uuid.UUID) (*[]pgDeviceResult, []string, error) {
						return &pgDevices, []string{}, nil
					},
					GetRedisDeviceStatus: func(ctx context.Context, rd *redis.Client, deviceInfo *[]dataPayload, gatewayIds []string) (*[]dataPayload, error) {
						return deviceInfo, nil
					},
					ConvertLocations: func(pgLocations *[]pgLocationResult) []LocationInfo {
						return convertPgLocationResults(pgLocations)
					},
					ConvertDevices: func(pgDevices *[]pgDeviceResult) *[]dataPayload {
						return convertPgDeviceInfos(pgDevices)
					},
				}
			},
			setupRequest: func() *http.Request {
				req := httptest.NewRequest("GET", "/api/organizations/"+orgID.String()+"/dashboard/devices", nil)
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": orgID.String(),
				})
				userPermissions := &authorizer.UserPermissions{UserID: "test-user"}
				sessionData := &auth.Session{
					UserID:          "test-user",
					UserPermissions: userPermissions,
				}
				req = req.WithContext(context.WithValue(req.Context(), auth.SessionContextKey, sessionData))
				return req
			},
			expectedStatus: http.StatusOK,
			expectedError:  false,
		},
	}

	for _, tt := range tests {
		tt := tt // capture for parallel
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			// Setup
			deps := tt.setupDeps()
			req := tt.setupRequest()
			w := httptest.NewRecorder()

			// Execute
			handler := GetDashboardDevicesWithDeps(deps)
			handler(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			if !tt.expectedError {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, "success", response["status"])
			}
		})
	}
}

// TestGetLocations tests the getLocations function
func TestGetLocations(t *testing.T) {
	t.Parallel()
	orgID := uuid.MustParse("c469a554-f7a8-5de5-a57e-e1ba16f970d3")

	tests := []struct {
		name           string
		setupMock      func() connect.DatabaseExecutor
		orgId          uuid.UUID
		expectedError  bool
		expectedResult bool
	}{
		{
			name: "success_case",
			setupMock: func() connect.DatabaseExecutor {
				return &dbexecutor.FakeDBExecutor{}
			},
			orgId:          orgID,
			expectedError:  false,
			expectedResult: true,
		},
		{
			name: "database_error",
			setupMock: func() connect.DatabaseExecutor {
				fakeDB := &dbexecutor.FakeDBExecutor{}
				fakeDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return assert.AnError
				}
				return fakeDB
			},
			orgId:          orgID,
			expectedError:  true,
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		tt := tt // capture for parallel
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			// Setup
			mockDB := tt.setupMock()

			// Execute
			result, err := getLocations(mockDB, tt.orgId)

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				if tt.expectedResult {
					assert.NotNil(t, result)
				}
			}
		})
	}
}

// TestGetPgDeviceInfo tests the getPgDeviceInfo function
func TestGetPgDeviceInfo(t *testing.T) {
	t.Parallel()
	orgID := uuid.MustParse("c469a554-f7a8-5de5-a57e-e1ba16f970d3")
	userPermissions := &authorizer.UserPermissions{UserID: "test-user"}

	tests := []struct {
		name            string
		setupMock       func() connect.DatabaseExecutor
		userPermissions *authorizer.UserPermissions
		orgId           uuid.UUID
		expectedError   bool
		expectedResult  bool
	}{
		{
			name: "success_case",
			setupMock: func() connect.DatabaseExecutor {
				return &dbexecutor.FakeDBExecutor{}
			},
			userPermissions: userPermissions,
			orgId:           orgID,
			expectedError:   false,
			expectedResult:  true,
		},
		{
			name: "nil_organization_id",
			setupMock: func() connect.DatabaseExecutor {
				return &dbexecutor.FakeDBExecutor{}
			},
			userPermissions: userPermissions,
			orgId:           uuid.Nil,
			expectedError:   true,
			expectedResult:  false,
		},
		{
			name: "database_error",
			setupMock: func() connect.DatabaseExecutor {
				fakeDB := &dbexecutor.FakeDBExecutor{}
				fakeDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return assert.AnError
				}
				return fakeDB
			},
			userPermissions: userPermissions,
			orgId:           orgID,
			expectedError:   true,
			expectedResult:  false,
		},
		{
			name: "success_with_gateway_ids",
			setupMock: func() connect.DatabaseExecutor {
				fakeDB := &dbexecutor.FakeDBExecutor{}
				fakeDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Mock database to return devices with gateway IDs
					if slice, ok := dest.(*[]pgDeviceResult); ok {
						*slice = []pgDeviceResult{
							{
								ID:         "device-1",
								DeviceID:   "device-1",
								DeviceType: "traffic_light",
								LocationID: "location-1",
								IsEnabled:  true,
								GatewayID:  "gateway-1",
							},
							{
								ID:         "device-2",
								DeviceID:   "device-2",
								DeviceType: "sensor",
								LocationID: "location-1",
								IsEnabled:  true,
								GatewayID:  "gateway-2",
							},
							{
								ID:         "device-3",
								DeviceID:   "device-3",
								DeviceType: "camera",
								LocationID: "location-1",
								IsEnabled:  true,
								GatewayID:  "gateway-1", // Same gateway as device-1
							},
						}
					}
					return nil
				}
				return fakeDB
			},
			userPermissions: userPermissions,
			orgId:           orgID,
			expectedError:   false,
			expectedResult:  true,
		},
	}

	for _, tt := range tests {
		tt := tt // capture for parallel
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			// Setup
			mockDB := tt.setupMock()

			// Execute
			devices, gatewayIds, err := getPgDeviceInfo(mockDB, tt.userPermissions, tt.orgId)

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, devices)
				assert.Nil(t, gatewayIds)
			} else {
				assert.NoError(t, err)
				if tt.expectedResult {
					assert.NotNil(t, devices)
					assert.NotNil(t, gatewayIds)
				}
			}
		})
	}
}

// TestMergeRedisStatusIntoDevices tests the mergeRedisStatusIntoDevices function
func TestMergeRedisStatusIntoDevices(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name           string
		devices        *[]dataPayload
		redisStatusMap *[]dataPayload
		expectedResult *[]dataPayload
	}{
		{
			name:           "nil_devices",
			devices:        nil,
			redisStatusMap: &[]dataPayload{},
			expectedResult: nil,
		},
		{
			name:           "nil_redis_status_map",
			devices:        &[]dataPayload{},
			redisStatusMap: nil,
			expectedResult: &[]dataPayload{},
		},
		{
			name: "successful_merge",
			devices: &[]dataPayload{
				{DeviceIdentifier: "device-1", LocationID: "location-1", Status: status{State: DeviceStateNeverComm}},
				{DeviceIdentifier: "device-2", LocationID: "location-1", Status: status{State: DeviceStateNeverComm}},
			},
			redisStatusMap: &[]dataPayload{
				{DeviceIdentifier: "device-1", LocationID: "location-1", Status: status{State: DeviceStateNoFault}},
			},
			expectedResult: &[]dataPayload{
				{DeviceIdentifier: "device-1", LocationID: "location-1", Status: status{State: DeviceStateNoFault}},
				{DeviceIdentifier: "device-2", LocationID: "location-1", Status: status{State: DeviceStateNeverComm}},
			},
		},
		{
			name: "no_matching_devices",
			devices: &[]dataPayload{
				{DeviceIdentifier: "device-1", LocationID: "location-1", Status: status{State: DeviceStateNeverComm}},
			},
			redisStatusMap: &[]dataPayload{
				{DeviceIdentifier: "device-3", LocationID: "location-1", Status: status{State: DeviceStateNoFault}},
			},
			expectedResult: &[]dataPayload{
				{DeviceIdentifier: "device-1", LocationID: "location-1", Status: status{State: DeviceStateNeverComm}},
			},
		},
	}

	for _, tt := range tests {
		tt := tt // capture for parallel
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			// Execute
			mergeRedisStatusIntoDevices(tt.devices, tt.redisStatusMap)

			// Assert
			if tt.expectedResult != nil {
				assert.Equal(t, len(*tt.expectedResult), len(*tt.devices))
				for i, expected := range *tt.expectedResult {
					assert.Equal(t, expected.DeviceIdentifier, (*tt.devices)[i].DeviceIdentifier)
					assert.Equal(t, expected.Status.State, (*tt.devices)[i].Status.State)
				}
			}
		})
	}
}

// TestDeviceState_String tests the String method for DeviceState
func TestDeviceState_String(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name     string
		state    DeviceState
		expected string
	}{
		{
			name:     "nofault_state",
			state:    DeviceStateNoFault,
			expected: "nofault",
		},
		{
			name:     "faulted_state",
			state:    DeviceStateFaulted,
			expected: "faulted",
		},
		{
			name:     "error_state",
			state:    DeviceStateError,
			expected: "error",
		},
		{
			name:     "nevercomm_state",
			state:    DeviceStateNeverComm,
			expected: "nevercomm",
		},
	}

	for _, tt := range tests {
		tt := tt // capture for parallel
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			// Execute
			result := tt.state.String()

			// Assert
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetRedisDeviceStatus(t *testing.T) {
	// Backup original ProcessRmsData
	origProc := deviceProcessRmsData
	defer func() { deviceProcessRmsData = origProc }()

	tests := []struct {
		name       string
		info       *[]dataPayload
		gateways   []string
		mockSetup  func(redismock.ClientMock)
		processErr bool
		wantErr    bool
		wantNil    bool
		wantLen    int
	}{
		{
			name:      "nil info",
			info:      nil,
			gateways:  []string{"k"},
			mockSetup: func(_ redismock.ClientMock) {},
			wantErr:   false, wantNil: true,
		},
		{
			name:      "empty info",
			info:      &[]dataPayload{},
			gateways:  []string{"k"},
			mockSetup: func(_ redismock.ClientMock) {},
			wantErr:   false, wantNil: true,
		},
		{
			name:      "empty gateways",
			info:      &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways:  []string{},
			mockSetup: func(_ redismock.ClientMock) {},
			wantErr:   false, wantNil: true,
		},
		{
			name:     "mget error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				m.ExpectMGet("k").SetErr(errors.New("mget fail"))
			},
			wantErr: true,
		},
		{
			name:     "raw nil and wrong type",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k1", "k2"},
			mockSetup: func(m redismock.ClientMock) {
				m.ExpectMGet("k1", "k2").SetVal([]interface{}{nil, 123})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
		{
			name:     "json unmarshal error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				m.ExpectMGet("k").SetVal([]interface{}{`not-json`})
			},
			wantErr: true,
		},
		{
			name:     "base64 decode error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: "bad!"})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
		{
			name:     "proto unmarshal error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				raw := base64.StdEncoding.EncodeToString([]byte("no-proto"))
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: raw})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
		{
			name:     "missing device id",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				msg := &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{{DeviceId: "other", Message: []byte("hi")}}}
				rawProto, _ := proto.Marshal(msg)
				rawB64 := base64.StdEncoding.EncodeToString(rawProto)
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: rawB64})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
		{
			name:     "process error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				// same as valid path
				msg := &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{{DeviceId: "d1", Message: []byte("hi")}}}
				rawProto, _ := proto.Marshal(msg)
				rawB64 := base64.StdEncoding.EncodeToString(rawProto)
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: rawB64})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			processErr: true,
			wantErr:    false,
			wantNil:    false,
			wantLen:    1,
		},
		{
			name:     "valid path",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				msg := &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{{DeviceId: "d1", Message: []byte("hi")}}}
				rawProto, _ := proto.Marshal(msg)
				rawB64 := base64.StdEncoding.EncodeToString(rawProto)
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: rawB64})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
	}

	for _, tc := range tests {
		tc := tc // capture for parallel, but as discussed, run serially due to global state override
		t.Run(tc.name, func(t *testing.T) {
			// override ProcessRmsData
			deviceProcessRmsData = func(header *pubsubdata.HeaderDetails, data []byte) (*helper.RmsStatusRecord, *helper.HeaderRecord, error) {
				if tc.processErr {
					return nil, nil, errors.New("process error")
				}
				return &helper.RmsStatusRecord{MonitorTime: time.Now().UTC(), Fault: "ok"}, &helper.HeaderRecord{Model: 1, FirmwareRevision: "1", FirmwareVersion: "1", CommVersion: "1"}, nil
			}

			// setup redis mock
			rClient, rm := redismock.NewClientMock()
			tc.mockSetup(rm)

			// invoke
			out, err := getRedisDeviceStatus(context.Background(), rClient, tc.info, tc.gateways)

			// error assertion
			if tc.wantErr {
				if err == nil {
					t.Fatalf("%s: expected error, got none", tc.name)
				}
				return
			}
			if err != nil {
				t.Fatalf("%s: unexpected error: %v", tc.name, err)
			}

			// nil assertion
			if tc.wantNil {
				if out != nil {
					t.Errorf("%s: expected nil output, got %v", tc.name, out)
				}
				return
			}

			// content assertion
			if out == nil {
				t.Fatalf("%s: expected output, got nil", tc.name)
			}
			if len(*out) != tc.wantLen {
				t.Errorf("%s: len = %d; want %d", tc.name, len(*out), tc.wantLen)
			}
		})
	}
}
