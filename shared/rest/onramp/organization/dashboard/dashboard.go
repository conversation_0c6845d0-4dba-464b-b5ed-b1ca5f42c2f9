package dashboard

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"slices"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/gateway/v1"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/proto"
	apiShared "synapse-its.com/shared/api"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/devices"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/rest/external/rediscache"
	"synapse-its.com/shared/rest/onramp/helper"
)

// HandlerDeps is a struct that contains the dependencies for the dashboard handler
type HandlerDeps struct {
	GetConnections       func(context.Context, ...bool) (*connect.Connections, error)
	GetLocations         func(connect.DatabaseExecutor, uuid.UUID) (*[]pgLocationResult, error)
	GetDevicesInfo       func(connect.DatabaseExecutor, *authorizer.UserPermissions, uuid.UUID) (*[]pgDeviceResult, []string, error)
	GetRedisDeviceStatus func(context.Context, *redis.Client, *[]dataPayload, []string) (*[]dataPayload, error)
	ConvertLocations     func(*[]pgLocationResult) []LocationInfo
	ConvertDevices       func(*[]pgDeviceResult) *[]dataPayload
}

var deviceProcessRmsData = devices.ProcessRmsData

// GetDashboardDevicesWithDeps handles the GET /dashboard/devices request
// @Summary Get dashboard devices and locations
// @Description Retrieves all locations and devices with their status for dashboard visualization. The endpoint returns locations and devices separately. Users can only view devices they have permission to access. Status defaults: "nevercomm" for disabled devices, "error" for enabled devices with no status data.
// @Tags env:dev, env:qa, env:sandbox, dashboard
// @Produce json
// @Param organizationId path string true "Organization ID to scope the request"
// @Success 200 {object} response.SuccessResponse{data=DashboardResponse} "Dashboard data retrieved successfully"
// @Failure 400 {object} response.BadRequestResponse "Bad Request - Invalid parameters"
// @Failure 401 {object} response.UnauthorizedResponse "Unauthorized"
// @Failure 500 {object} response.InternalErrorResponse "Internal Server Error"
// @Router /api/organizations/{organizationId}/dashboard/devices [get]
func GetDashboardDevicesWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		session, err := rediscache.GetSessionFromContext(ctx)
		if err != nil {
			logger.Errorf("Unable to retrieve user permissions from session: %v", err)
			response.CreateUnauthorizedResponse(w)
			return
		}

		userPermissions := session.UserPermissions

		orgId, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("failed to get database connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		pg := connections.Postgres
		rd := connections.Redis

		pgLocations, err := deps.GetLocations(pg, orgId)
		if err != nil {
			logger.Errorf("failed to retrieve locations: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		pgDevices, gatewayIds, err := deps.GetDevicesInfo(pg, userPermissions, orgId)
		if err != nil {
			logger.Errorf("failed to retrieve devices: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		locations := convertPgLocationResults(pgLocations)

		devicesPayload := convertPgDeviceInfos(pgDevices)

		redisStatusMap, err := deps.GetRedisDeviceStatus(ctx, rd, devicesPayload, gatewayIds)
		if err != nil {
			logger.Warnf("failed to retrieve Redis status, using defaults: %v", err)
			redisStatusMap = nil
		}

		mergeRedisStatusIntoDevices(devicesPayload, redisStatusMap)

		devicesByLocation := make(map[string][]dataPayload)
		for _, device := range *devicesPayload {
			devicesByLocation[device.LocationID] = append(devicesByLocation[device.LocationID], device)
		}

		for i := range locations {
			locationID := locations[i].ID
			if locationDevices, exists := devicesByLocation[locationID]; exists {
				locations[i].Devices = locationDevices
			} else {
				locations[i].Devices = []dataPayload{}
			}
		}

		response.CreateSuccessResponse(DashboardResponse{
			OrganizationID: orgId.String(),
			Locations:      locations,
		}, w)
	}
}

var getLocations = func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]pgLocationResult, error) {
	query := `
		SELECT 
			l.Id AS id,
			l.Name AS name,
			COALESCE(CAST(l.Latitude AS TEXT), '0.00000000') AS latitude,
			COALESCE(CAST(l.Longitude AS TEXT), '0.00000000') AS longitude
		FROM {{Location}} l
		WHERE l.OrganizationId = $1
		AND l.IsDeleted = FALSE
		ORDER BY l.Name
	`

	pgLocations := &[]pgLocationResult{}
	err := pg.QueryGenericSlice(pgLocations, query, orgId)
	if err != nil {
		return nil, err
	}

	return pgLocations, nil
}

var getPgDeviceInfo = func(pg connect.DatabaseExecutor, userPermissions *authorizer.UserPermissions, orgId uuid.UUID) (*[]pgDeviceResult, []string, error) {
	if orgId == uuid.Nil {
		return nil, nil, fmt.Errorf("organization ID is required")
	}

	query := `
		SELECT DISTINCT
			d.OrigId as ID,
			d.Id as DeviceID,
			d.Type as DeviceType,
			COALESCE(d.LocationId::text, '') AS LocationID,
			d.IsEnabled AS IsEnabled,
			COALESCE(d.SoftwareGatewayId::text, '') AS GatewayID
		FROM {{Device}} as d
		WHERE d.OrganizationId = $1 AND d.IsDeleted = FALSE
		ORDER BY d.Id
	`

	pgDevices := &[]pgDeviceResult{}
	err := pg.QueryGenericSlice(pgDevices, query, orgId)
	if err != nil {
		return nil, nil, err
	}

	devs := *pgDevices
	gatewayIds := make([]string, 0, len(devs))
	for _, d := range devs {
		if d.GatewayID != "" {
			key := fmt.Sprintf("GatewayRMSData:%s", d.GatewayID)
			if !slices.Contains(gatewayIds, key) {
				gatewayIds = append(gatewayIds, key)
			}
		}
	}

	return pgDevices, gatewayIds, nil
}

var getRedisDeviceStatus = func(ctx context.Context, rd *redis.Client, deviceInfo *[]dataPayload, gatewayIds []string) (*[]dataPayload, error) {
	if deviceInfo == nil || len(*deviceInfo) == 0 || len(gatewayIds) == 0 {
		return nil, nil
	}

	idxMap := make(map[string]int, len(*deviceInfo))
	for i, dp := range *deviceInfo {
		idxMap[dp.DeviceIdentifier] = i
	}

	vals, err := rd.MGet(ctx, gatewayIds...).Result()
	if err != nil {
		return nil, err
	}

	for i, raw := range vals {
		if raw == nil {
			logger.Warnf("no info for redis key: %s", gatewayIds[i])
			continue
		}
		str, ok := raw.(string)
		if !ok {
			logger.Errorf("unable to convert redis value to string: %s got type %v", gatewayIds[i], fmt.Sprintf("%T", raw))
			continue
		}
		var blob apiShared.RedisData
		err = json.Unmarshal([]byte(str), &blob)
		if err != nil {
			return nil, err
		}

		decode, err := base64.StdEncoding.DecodeString(blob.MsgData)
		if err != nil {
			logger.Errorf("%v: could not base64 decode redis message, %v", err, blob.MsgData)
			continue
		}

		msg := &gatewayv1.DeviceData{}
		if err = proto.Unmarshal(decode, msg); err != nil {
			logger.Errorf("%v: could not json unmarshal redis message, %v", err, decode)
			continue
		}

		for i, d := range msg.GetMessages() {
			idx, found := idxMap[d.DeviceId]
			if !found {
				continue
			}

			dp := &(*deviceInfo)[idx]

			logger.Debugf("/data/device endpoint, rms data from redis, processing record : %v, Deviceid : %v", i, d.DeviceId)
			pubsubheader := &pubsubdata.HeaderDetails{GatewayTimezone: blob.GatewayTimezone}
			rmsData, _, err := deviceProcessRmsData(pubsubheader, d.GetMessage())
			if err != nil {
				dp.Status.State = DeviceStateError
				logger.Infof("Error parsing record: %v", err)
			} else {
				dp.Status.State = DeviceState(rmsData.FaultStatus)
			}
		}

	}
	return deviceInfo, nil
}

func mergeRedisStatusIntoDevices(devices *[]dataPayload, redisStatusMap *[]dataPayload) {
	if devices == nil || redisStatusMap == nil {
		return
	}

	redisStatusLookup := make(map[string]*dataPayload)
	for i := range *redisStatusMap {
		dp := &(*redisStatusMap)[i]
		redisStatusLookup[dp.DeviceIdentifier] = dp
	}

	for i := range *devices {
		dp := &(*devices)[i]
		if redisDp, exists := redisStatusLookup[dp.DeviceIdentifier]; exists {
			dp.Status.State = redisDp.Status.State
		}
	}
}

// GetDashboardDevicesHandler is the production-ready HTTP handler using default dependencies
var GetDashboardDevicesHandler = GetDashboardDevicesWithDeps(HandlerDeps{
	GetConnections:       connect.GetConnections,
	GetLocations:         getLocations,
	GetDevicesInfo:       getPgDeviceInfo,
	GetRedisDeviceStatus: getRedisDeviceStatus,
	ConvertLocations:     convertPgLocationResults,
	ConvertDevices:       convertPgDeviceInfos,
})
