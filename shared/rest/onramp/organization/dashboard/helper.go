package dashboard

func getEDIInitialState(d pgDeviceResult) DeviceState {
	if !d.IsEnabled {
		return DeviceStateNeverComm
	}

	return DeviceStateError
}

func convertPgLocationResults(pgLocations *[]pgLocationResult) []LocationInfo {
	if pgLocations == nil {
		return []LocationInfo{}
	}

	locations := make([]LocationInfo, 0, len(*pgLocations))
	for _, loc := range *pgLocations {
		locations = append(locations, LocationInfo{
			ID:        loc.ID,
			Name:      loc.Name,
			Latitude:  loc.Latitude,
			Longitude: loc.Longitude,
		})
	}

	return locations
}

func convertPgDeviceInfos(dbInfo *[]pgDeviceResult) *[]dataPayload {
	if dbInfo == nil {
		return nil
	}
	infos := *dbInfo
	out := make([]dataPayload, 0, len(infos))
	for _, d := range infos {
		dp := dataPayload{
			DeviceIdentifier: d.DeviceID,
			LocationID:       d.LocationID,
			Status: status{
				State: getEDIInitialState(d),
			},
		}
		out = append(out, dp)
	}
	return &out
}
