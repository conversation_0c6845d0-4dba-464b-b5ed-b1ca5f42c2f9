package invites

import (
	"context"
	"time"

	"github.com/google/uuid"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/bqbatcher"
	"synapse-its.com/shared/mocks/dbexecutor"
	"synapse-its.com/shared/mocks/pubsub"
	"synapse-its.com/shared/pubsubdata"
)

// setupTestDeps provides a complete set of test dependencies for handler testing
func setupTestDeps() HandlerDeps {
	return HandlerDeps{
		GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
			return &connect.Connections{
				Postgres: &dbexecutor.FakeDBExecutor{},
				Pubsub:   pubsub.NewFakePubsubClient(),
			}, nil
		},
		GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
			return bqbatcher.FakeBatch(ctx)
		},
		CreateInvite: func(pg connect.DatabaseExecutor, orgID uuid.UUID, tokenHash string, invitationStatus string, req CreateInviteRequest) (*UserInvite, error) {
			return &UserInvite{
				ID:                     uuid.New(),
				OrganizationIdentifier: orgID,
				TokenHash:              tokenHash,
				Email:                  req.Email,
				InviterID:              req.InviterID,
				CustomRoleID:           req.OrganizationRole,
				Status:                 StatusPending,
				Message:                req.Message,
				RequireSSO:             false,
				RetryCount:             0,
				Created:                time.Now().UTC(),
				Updated:                time.Now().UTC(),
			}, nil
		},
		GenerateToken: func(length uint) (string, error) {
			return "test-token-64-chars-long-abcdefghijklmnopqrstuvwxyz1234567890", nil
		},
		HashString: func(input string) string {
			return "hashed-" + input
		},
		GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
			return "Test Organization", nil
		},
		RenderEmailTemplate: func(data EmailTemplateData) (string, error) {
			return "<html>Test Email</html>", nil
		},
		RenderEmailSubject: func(data EmailTemplateData) (string, error) {
			return "You're Invited to " + data.AppName + "!", nil
		},
		PublishEmailNotification: func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message pubsubdata.NotificationRequest) error {
			return nil
		},
		LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
			return nil
		},
		GetInvitesForUser: func(pg connect.DatabaseExecutor, userID uuid.UUID) (*[]UserInvite, error) {
			return &[]UserInvite{}, nil
		},
		GetInvitesForOrganization: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (*[]UserInvite, error) {
			return &[]UserInvite{}, nil
		},
		UpdateInviteStatus: func(pg connect.DatabaseExecutor, inviteID uuid.UUID, status string, now *time.Time) error {
			return nil
		},
		GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
			// Use a fixed organization ID for testing
			orgID, _ := uuid.Parse("123e4567-e89b-12d3-a456-************")
			return &UserInvite{
				ID:                     inviteID,
				OrganizationIdentifier: orgID,
				TokenHash:              "test-hash",
				Email:                  "<EMAIL>",
				InviterID:              uuid.New(),
				CustomRoleID:           uuid.New(),
				Status:                 StatusPending,
				RetryCount:             0,
				Created:                time.Now().UTC(),
				Updated:                time.Now().UTC(),
			}, nil
		},
		UpdateInviteToken: func(pg connect.DatabaseExecutor, inviteID uuid.UUID, tokenHash string, retryCount int, req *ResendInviteRequest) error {
			return nil
		},
		ValidateInviteToken: func(pg connect.DatabaseExecutor, token string) (*UserInvite, error) {
			return &UserInvite{
				ID:                     uuid.New(),
				OrganizationIdentifier: uuid.New(),
				TokenHash:              "test-hash",
				Email:                  "<EMAIL>",
				InviterID:              uuid.New(),
				CustomRoleID:           uuid.New(),
				Status:                 StatusPending,
				Created:                time.Now().UTC(),
				Updated:                time.Now().UTC(),
			}, nil
		},
		CheckCooldownPeriod: func(invite *UserInvite) error {
			return nil
		},
		VerifyEmailInAuthMethod: func(pg connect.DatabaseExecutor, userID uuid.UUID, email string, requireSSO bool) error {
			return nil
		},
		VerifyCustomRoleForOrganization: func(pg connect.DatabaseExecutor, orgID uuid.UUID, roleID uuid.UUID) error {
			return nil
		},
		VerifyUserNotMemberOfOrganization: func(pg connect.DatabaseExecutor, userID uuid.UUID, orgID uuid.UUID) error {
			return nil
		},
		CreateMembershipForUser: func(pg connect.DatabaseExecutor, userID uuid.UUID, orgID uuid.UUID, roleID uuid.UUID, email string) error {
			return nil
		},
		CheckEmailExistsInAuthMethod: func(pg connect.DatabaseExecutor, email string) (bool, error) {
			return false, nil
		},
	}
}
