package helper

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"cloud.google.com/go/bigquery"
	"synapse-its.com/shared/api/softwaregateway"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/schemas"
)

// jsonMarshal provides a test seam for JSON marshaling in this package.
var jsonMarshal = json.Marshal

var VerifyByteLen = func(byteMsg []byte, expectedLength int) bool {
	return len(byteMsg) == expectedLength
}

func GetFirmwareAttributes(monitorModel MonitorModel, firmwareVersion byte) (volt220 bool, voltDC bool, mainsDC bool) {
	switch firmwareVersion {
	case 0x09, 0x54: // standard 220V / 50Hz, LSU
		volt220 = true
		voltDC = false
		mainsDC = false
	case 0x30: // China 220V / 50Hz
		volt220 = true
		voltDC = false
		mainsDC = false
	case 0x31: // 48VDC load, ac main, LA Hybrid
		volt220 = false
		voltDC = true
		mainsDC = false
	case 0x32: // 48VDC loads, ac main, standard hybrid
		volt220 = false
		voltDC = true
		mainsDC = false
	case 0x36: // 48VDC loads, 48 VDC Main
		volt220 = false
		voltDC = true
		mainsDC = true
	case 0x38: // 12VDC loads, 12 VDC Main
		volt220 = false
		voltDC = true
		mainsDC = true
	case 0x39, 0x48: // 24VDC loads, 24 VDC Main
		volt220 = false
		voltDC = true
		mainsDC = true
	case 0x40: // Moscow 220V / 50Hz
		volt220 = true
		voltDC = false
		mainsDC = false
	case 0x45: // Econolite China 220V / 50Hz
		volt220 = true
		voltDC = false
		mainsDC = false
	default: // normal ac unit
		volt220 = false
		voltDC = false
		mainsDC = false
	}
	if monitorModel == CMU2212_lv || monitorModel == CMUip2212_lv || monitorModel == CMU212_48 {
		volt220 = false
		voltDC = true
		mainsDC = true
	}
	return volt220, voltDC, mainsDC
}

var GetACLineEventType = func(monitorModel MonitorModel, mainsDC bool, mainVoltage int64, powerDownLevel int64, eventType byte) string {
	// this information was derived from routines.DisplayACLog
	switch eventType {
	case 0x05:
		switch monitorModel {
		case CMU212, CMU212_220, CMU212_48, CMU212_12, CMU212_24, CMU212_48H, CMU2212_hv, CMUip2212_hv, CMU2212_lv, CMUip2212_lv, CMU2212_vhv, CMUip2212_vhv:
			return "CMU Power Up"
		default:
			return "AC Power Up"
		}
	case 0x41:
		if mainsDC {
			return "Restore DC Main"
		} else {
			return "Restore AC"
		}
	case 0x42:
		return "Restore WDT"
	case 0x43:
		return "Restore AC and WDT"
	case 0x44:
		return "AC Power Up"
	case 0x49:
		if mainsDC {
			return "Restore Interrupt DC Main"
		} else {
			return "Restore Interrupt AC"
		}
	case 0x50:
		return "Red Enable: Override Off"
	case 0x52:
		return "Red Enable and WDT: Override Off"
	case 0x60:
		switch monitorModel {
		case CMU212, CMU212_220, CMU212_48, CMU212_12, CMU212_24, CMU212_48H, CMU2212_hv, CMUip2212_hv, CMU2212_lv, CMUip2212_lv, CMU2212_vhv, CMUip2212_vhv:
			return "nReset Not Active"
		default:
			return "Restore AC Main Frequency"
		}
	case 0x81: // TODO - this will not work depending on the device model
		return "Power Down"
	case 0x82:
		return "Brownout WDT"
	case 0x83:
		if mainVoltage > powerDownLevel {
			return "Brownout AC and WDT"
		} else {
			return "Power Down"
		}
	case 0x88:
		return "AC Interrupt"
	case 0x89:
		if mainsDC {
			return "DC Main Interrupt"
		} else {
			return "AC Interrupt"
		}
	case 0x8b:
		return "AC Interrupt and WDT"
	case 0x90:
		return "Red Enable: Override On"
	case 0x92:
		return "Red Enable and WDT: Override On"
	case 0xa0:
		switch monitorModel {
		case CMU212, CMU212_220, CMU212_48, CMU212_12, CMU212_24, CMU212_48H, CMU2212_hv, CMUip2212_hv, CMU2212_lv, CMUip2212_lv, CMU2212_vhv, CMUip2212_vhv:
			return "nReset Active (ATC Power Fail)"
		case Ecl2018:
			return "Brownout AC Main Frequency"
		}
	default:
		return "AC event type error"
	}
	return ""
}

func MonitorModelToString(model byte, firmwareRevision byte) string {
	switch model {
	case 3:
		return "2010ECL"
	case 51:
		return "CMUip2212-HV"
	case 11:
		return "MMU2-16LEip"
	default:
		return "unknown"
	}
}

func GetMonitorName(bytes []byte) string {
	theMonitorName := ""

	for _, b := range bytes {
		if int(b) >= 32 && int(b) <= 127 {
			theMonitorName += string(b)
		}
	}

	theMonitorName = strings.TrimSpace(theMonitorName)

	return theMonitorName
}

var ValidateChecksum = func(responseMsg []byte) error {
	// check the checksum of the message received -- if not a valid checksum - return error
	// the checsum is in the last byte of the response msg
	if len(responseMsg) == 0 {
		return fmt.Errorf("%w", ErrValidateChecksumLen)
	}

	var sum uint8
	for _, b := range responseMsg[:len(responseMsg)-1] {
		sum += b
	}

	// take the 1s complement of the least significant byte
	expected := ^sum
	actual := responseMsg[len(responseMsg)-1]
	if expected != actual {
		return fmt.Errorf("%w", ErrValidateChecksum)
	}
	return nil
}

func ConvertLSandMStoUnint16(ls byte, ms byte) uint16 {
	return (256*uint16(ms) + uint16(ls))
}

// The purpose of this function is to decode a packed BCD(Binary coded decimal)
// This is needed because of the data sent by the firmware for the timestamp bytes
func ConvertToIntFromBcd(value byte) (int, error) {
	hi := value >> 4
	lo := value & 0x0F
	if hi > 9 || lo > 9 {
		return 0, fmt.Errorf("%w : 0x%02X (nibbles %d,%d)", ErrConvertToIntFromBcd, value, hi, lo)
	}
	return int(hi)*10 + int(lo), nil
}

func ConvertBCDBytesToDateTimeII(month byte, day byte, yr byte, hr byte, min byte, sec byte, tzName string) (theTime time.Time, err error) {
	year, err := ConvertToIntFromBcd(yr)
	if err != nil {
		return theTime, (fmt.Errorf("%w = %d %v", ErrValidateDateTimePartsMon, yr, err))
	}
	if year < 70 {
		year += 2000
	} else {
		year += 1900
	}

	imonth, err := ConvertToIntFromBcd(month)
	if (imonth == 0) || (imonth > 12) || (err != nil) {
		return theTime, (fmt.Errorf("%w = %d %v", ErrValidateDateTimePartsMon, imonth, err))
	}
	iday, err := ConvertToIntFromBcd(day)
	if (iday == 0) || (iday > 31) || (err != nil) {
		return theTime, (fmt.Errorf("%w = %d %v", ErrValidateDateTimePartsDay, iday, err))
	}
	ihr, err := ConvertToIntFromBcd(hr)
	if (ihr > 24) || (err != nil) {
		return theTime, (fmt.Errorf("%w = %d %v", ErrValidateDateTimePartsHour, ihr, err))
	}
	imin, err := ConvertToIntFromBcd(min)
	if (imin > 60) || (err != nil) {
		return theTime, (fmt.Errorf("%w = %d %v", ErrValidateDateTimePartsMin, imin, err))
	}
	isec, err := ConvertToIntFromBcd(sec)
	if (isec > 60) || (err != nil) {
		return theTime, (fmt.Errorf("%w = %d %v", ErrValidateDateTimePartsSec, isec, err))
	}

	loc, err := time.LoadLocation(tzName)
	if err != nil {
		logger.Warnf("%v:  %v", ErrInvalidTimezone, tzName)
		loc = time.Local
	}

	theTime = time.Date(year, time.Month(imonth), int(iday), int(ihr), int(imin), int(isec), 0, loc)

	return theTime.UTC(), nil
}

func IsBitSet(n uint32, pos int) bool {
	val := n & (1 << pos)
	return (val > 0)
}

// func GetReportDetail(headerDetail *HeaderRecord, rmsEngineDetail *RmsEngineRecord) (reportHeaderDetail *ReportHeaderRecord) {
// TODO: Need to determin if needed. Only used in edicmu2212 and ediecl2010
// reportHeaderDetail = &ReportHeaderRecord{}

// reportHeaderDetail.ModelText = MonitorModelToString(byte(headerDetail.Model), byte(headerDetail.FirmwareRevision))

// switch headerDetail.Model {
// case Ecl2010:
// 	if headerDetail.FirmwareRevision > int(0x27) {
// 		reportHeaderDetail.FirmwareTypeText = strconv.Itoa(int((headerDetail.FirmwareVersion / 16))) + strconv.Itoa(int((headerDetail.FirmwareVersion % 16)))
// 		reportHeaderDetail.FirmwareVersionText = strconv.Itoa(int(((headerDetail.FirmwareRevision & 0xf0) / 16))) + "." + strconv.Itoa(int((headerDetail.FirmwareRevision & 0xf)))
// 		reportHeaderDetail.MonitorCommVersionText = strconv.Itoa(int(((headerDetail.CommVersion & 0xf0) / 16))) + "." + strconv.Itoa(int((headerDetail.CommVersion & 0xf)))
// 		if rmsEngineDetail != nil { // rmsengine info is optional and not available in every model
// 			reportHeaderDetail.RMSEngineFirmwareTypeText = strconv.Itoa(int((rmsEngineDetail.EngineVersion / 16))) + strconv.Itoa(int((rmsEngineDetail.EngineVersion % 16)))
// 			reportHeaderDetail.RMSEngineFirmwareVersionText = strconv.Itoa(int(((rmsEngineDetail.EngineRevision & 0xf0) / 16))) + "." + strconv.Itoa(int((rmsEngineDetail.EngineRevision & 0xf)))
// 		}
// 		return reportHeaderDetail
// 	} else {
// 		return nil
// 	}
// case CMUip2212_hv, Mmu16le:
// 	reportHeaderDetail.FirmwareTypeText = strconv.Itoa(int((headerDetail.FirmwareVersion / 16))) + strconv.Itoa(int((headerDetail.FirmwareVersion % 16)))
// 	reportHeaderDetail.FirmwareVersionText = strconv.Itoa(int(((headerDetail.FirmwareRevision & 0xf0) / 16))) + "." + strconv.Itoa(int((headerDetail.FirmwareRevision & 0xf)))
// 	reportHeaderDetail.MonitorCommVersionText = strconv.Itoa(int(((headerDetail.CommVersion & 0xf0) / 16))) + "." + strconv.Itoa(int((headerDetail.CommVersion & 0xf)))
// 	if rmsEngineDetail != nil { // rmsengine info is optional and not available in every model
// 		reportHeaderDetail.RMSEngineFirmwareTypeText = strconv.Itoa(int((rmsEngineDetail.EngineVersion / 16))) + strconv.Itoa(int((rmsEngineDetail.EngineVersion % 16)))
// 		reportHeaderDetail.RMSEngineFirmwareVersionText = strconv.Itoa(int(((rmsEngineDetail.EngineRevision & 0xf0) / 16))) + "." + strconv.Itoa(int((rmsEngineDetail.EngineRevision & 0xf)))
// 	}
// 	return reportHeaderDetail
// default:
// 	return nil
// }
// }

// GetFormattedTime gets the current time and formats it to a string tin the format 15:04:05.000
func GetFormattedTime() string {
	ct := time.Now()
	return (ct.Format("1/2/2006") + " " + ct.Format("15:04:05.000"))
}

// BeautifyBytesToHexString returns the hex representation of a byte slice as a string
func BeautifyBytesToHexString(byteArray []byte) string {
	var retVal string

	for i, b := range byteArray {
		if i > 0 {
			retVal += " "
		}
		retVal += fmt.Sprintf("%02X", b)
	}
	return retVal
}

// RemoveCharacter removes all insances of the charToRemove from inputString, returning a string without the character
func RemoveCharacter(inputString string, charToRemove byte) string {
	var resultString string

	for i := 0; i < len(inputString); i++ {
		if inputString[i] != charToRemove {
			resultString += string(inputString[i])
		}
	}
	return resultString
}

// GetValueByKey returns a string value from a map.  If the key does not exist, an empty string is returned.
func GetValueByKey(data interface{}, key string) string {
	if dataMap, ok := data.(map[string]interface{}); ok {
		if value, exists := dataMap[key]; exists {
			strValue := fmt.Sprintf("%v", value)
			return strValue
		}
		return ""
	}
	return ""
}

// ConvertToString converts a map string to a string
func ConvertToString(value interface{}) (string, bool) {
	str, ok := value.(string)
	return str, ok
}

// StringToBool given a string, return a bool.
func StringToBool(s string) bool {
	lowercase := strings.ToLower(s)
	switch lowercase {
	case "true", "True", "1":
		return true
	case "false", "False", "0":
		return false
	default:
		return false
	}
}

// ConvertToBytes takes an interface and converts it to bytes
func ConvertToBytes(value interface{}) ([]byte, bool) {
	b, ok := value.([]byte)
	return b, ok
}

// AreBoolSlicesEqual compare two boolean byte slices
func AreBoolSlicesEqual(slice1, slice2 []bool) bool {
	if len(slice1) != len(slice2) {
		return false
	}
	for i, v := range slice1 {
		if v != slice2[i] {
			return false
		}
	}
	return true
}

// CalculateSHA256 compute the hash of the byte slice passed in
func CalculateSHA256(input []byte) string {
	hasher := sha256.New()
	hasher.Write(input)
	hashBytes := hasher.Sum(nil)
	return hex.EncodeToString(hashBytes)
}

func ToString(v interface{}) string {
	if v == nil {
		return ""
	}
	if s, ok := v.(string); ok {
		return s
	}
	return fmt.Sprintf("%v", v)
}

func ToBool(v interface{}) bool {
	if b, ok := v.(bool); ok {
		return b
	}
	return false
}

func ToInt(v interface{}) int {
	switch val := v.(type) {
	case int:
		return val
	case int64:
		return int(val)
	case float64:
		return int(val)
	case float32:
		return int(val)
	case string:
		i, err := strconv.Atoi(val)
		if err != nil {
			return 0
		}
		return i
	default:
		return 0
	}
}

func ToFloat(v interface{}) float64 {
	if f, ok := v.(float64); ok {
		return f
	}
	return 0
}

func GetHeaderBytesFromByteMsg(byteMsg []byte) ([]byte, error) {
	if len(byteMsg) < 7 {
		return nil, fmt.Errorf("%w, got %d", ErrGetHeaderBytesFromByteMsg, len(byteMsg))
	}
	headerBytes := make([]byte, 7)
	copy(headerBytes, byteMsg[:7])
	return headerBytes, nil
}

func ParseHeaderBytesIntoStruct(headerBytes []byte) (*HeaderRecord, error) {
	const MODELBYTE = 2            // 3rd byte is the monitor model
	const FIRMWAREVERSONBYTE = 3   // monitor firmware version
	const FIRMWAREREVISIONBYTE = 4 // monitor firmware revision
	const COMMVERSIONBYTE = 1      // monitor comm version

	if len(headerBytes) != 7 {
		return nil, fmt.Errorf("%w, length was %d", ErrParseHeaderBytesIntoStructLen, len(headerBytes))
	}

	// preserve the header bytes
	theHeader := hex.EncodeToString(headerBytes)

	// monitorId := 0
	monitorModel := headerBytes[MODELBYTE]
	monitorFirmwareVersion := headerBytes[FIRMWAREVERSONBYTE]
	monitorFirmwareRevision := headerBytes[FIRMWAREREVISIONBYTE]
	monitorCommVersion := headerBytes[COMMVERSIONBYTE]

	var powerDownLevel int64
	var blackoutLevel int64
	switch monitorModel { // ref VB6 Routines.SetHeader
	case byte(Ecl2010):
		if monitorFirmwareRevision >= 0x50 { // issue f+
			// monitorIDlsOffset := 5
			// monitorIDmsOffset := 6
			// monitorId = int(ConvertLSandMStoUnint16(headerBytes[monitorIDlsOffset], headerBytes[monitorIDmsOffset]))
			volt220, voltdc, mainsdc := GetFirmwareAttributes(MonitorModel(monitorModel), monitorFirmwareVersion)

			if mainsdc {
				powerDownLevel = 10
				blackoutLevel = 5
			} else {
				powerDownLevel = 25
				blackoutLevel = 10
			}

			return &HeaderRecord{
				Model:            MonitorModel(monitorModel),
				FirmwareVersion:  ConvertByteToString(monitorFirmwareVersion),
				FirmwareRevision: ConvertByteToDecimalFormat(monitorFirmwareRevision),
				CommVersion:      ConvertByteToDecimalFormat(monitorCommVersion),
				Volt220:          volt220,
				VoltDC:           voltdc,
				MainsDC:          mainsdc,
				PowerDownLevel:   powerDownLevel,
				BlackoutLevel:    blackoutLevel,
				MaxChannels:      16,
				Header:           &theHeader,
			}, nil
		} else {
			return nil, fmt.Errorf("model (%d) with firmwarerevision (%d) not supported", monitorModel, monitorFirmwareRevision)
		}
	case byte(CMUip2212_hv):
		// note: multi-trace = false
		return &HeaderRecord{
			// Id:               monitorId,
			Model:            MonitorModel(monitorModel),
			FirmwareVersion:  ConvertByteToString(monitorFirmwareVersion),
			FirmwareRevision: ConvertByteToDecimalFormat(monitorFirmwareRevision),
			CommVersion:      ConvertByteToDecimalFormat(monitorCommVersion),
			Volt220:          false,
			VoltDC:           false,
			MainsDC:          false,
			PowerDownLevel:   powerDownLevel,
			BlackoutLevel:    blackoutLevel,
			MaxChannels:      32,
			Header:           &theHeader,
		}, nil
	case byte(Mmu16le):
		// note: multi-trace = false
		volt220, voltdc, mainsdc := GetFirmwareAttributes(MonitorModel(monitorModel), monitorFirmwareVersion)

		if mainsdc {
			powerDownLevel = 10
			blackoutLevel = 5
		} else {
			powerDownLevel = 25
			blackoutLevel = 10
		}

		return &HeaderRecord{
			// Id:               monitorId,
			Model:            MonitorModel(monitorModel),
			FirmwareVersion:  ConvertByteToString(monitorFirmwareVersion),
			FirmwareRevision: ConvertByteToDecimalFormat(monitorFirmwareRevision),
			CommVersion:      ConvertByteToDecimalFormat(monitorCommVersion),
			Volt220:          volt220,
			VoltDC:           voltdc,
			MainsDC:          mainsdc,
			PowerDownLevel:   powerDownLevel,
			BlackoutLevel:    blackoutLevel,
			MaxChannels:      16,
			Header:           &theHeader,
		}, nil
	case byte(Kcl2018):
		volt220, voltdc, mainsdc := GetFirmwareAttributes(MonitorModel(monitorModel), monitorFirmwareVersion)

		if mainsdc {
			powerDownLevel = 10
			blackoutLevel = 5
		} else {
			powerDownLevel = 25
			blackoutLevel = 10
		}

		return &HeaderRecord{
			Model:            MonitorModel(monitorModel),
			FirmwareVersion:  ConvertByteToString(monitorFirmwareVersion),
			FirmwareRevision: ConvertByteToDecimalFormat(monitorFirmwareRevision),
			CommVersion:      ConvertByteToDecimalFormat(monitorCommVersion),
			Volt220:          volt220,
			VoltDC:           voltdc,
			MainsDC:          mainsdc,
			PowerDownLevel:   powerDownLevel,
			BlackoutLevel:    blackoutLevel,
			MaxChannels:      18,
			Header:           &theHeader,
		}, nil
	case byte(Ecl2018):
		volt220, voltdc, mainsdc := GetFirmwareAttributes(MonitorModel(monitorModel), monitorFirmwareVersion)

		if mainsdc {
			powerDownLevel = 10
			blackoutLevel = 5
		} else {
			powerDownLevel = 25
			blackoutLevel = 10
		}

		return &HeaderRecord{
			Model:            MonitorModel(monitorModel),
			FirmwareVersion:  ConvertByteToString(monitorFirmwareVersion),
			FirmwareRevision: ConvertByteToDecimalFormat(monitorFirmwareRevision),
			CommVersion:      ConvertByteToDecimalFormat(monitorCommVersion),
			Volt220:          volt220,
			VoltDC:           voltdc,
			MainsDC:          mainsdc,
			PowerDownLevel:   powerDownLevel,
			BlackoutLevel:    blackoutLevel,
			MaxChannels:      18,
			Header:           &theHeader,
		}, nil
	default:
		return nil, fmt.Errorf("model (%d) with firmwarerevision (%d) not supported", monitorModel, monitorFirmwareRevision)
	}
}

func ParseHeaderIntoStruct(byteMsg []byte) (*HeaderRecord, error) {
	headerBytes, err := GetHeaderBytesFromByteMsg(byteMsg)
	if err != nil {
		return nil, err
	}
	headerDetails, err := ParseHeaderBytesIntoStruct(headerBytes)
	if err != nil {
		return nil, err
	}
	return headerDetails, nil
}

// ParsePermissives interprets each bit of data as a permissive flag
// on the upper‐triangle of channels 1…channelCount.
// Returns a slice of secondaries for each primary (1…channelCount-1), or an error
// if data cannot be used to calculate permissives.
func ParsePermissives(data []byte, channelCount int) ([][]string, error) {
	if channelCount < 2 {
		return nil, fmt.Errorf("%w: got %d", ErrNotEnoughChannels, channelCount)
	}
	// calculate how many bits are needed: sum_{p=1..(N-1)} (N - p) = N*(N-1)/2
	bitsNeeded := channelCount * (channelCount - 1) / 2
	bitsAvailable := len(data) * 8
	if bitsAvailable < bitsNeeded {
		return nil, fmt.Errorf("%w: have %d bits (%d bytes), need %d bits for %d channels", ErrNotEnoughBits, bitsAvailable, len(data), bitsNeeded, channelCount)
	}
	maxPrim := channelCount - 1
	result := make([][]string, maxPrim)
	primIdx := 0         // zero-based → primary = primIdx+1
	secCh := primIdx + 2 // first secondary for primary=1 is channel 2
	for _, b := range data {
		mask := byte(1)
		for i := 0; i < 8; i++ {
			// if all primaries are filled, we're done and return
			if primIdx >= maxPrim {
				return result, nil
			}
			if b&mask != 0 {
				result[primIdx] = append(result[primIdx], strconv.Itoa(secCh))
			}
			secCh++
			if secCh > channelCount {
				primIdx++
				secCh = primIdx + 2
			}
			mask <<= 1
		}
	}
	return result, nil
}

func (h *HeaderRecord) ToBigQuerySchema() *schemas.HeaderRecord {
	return &schemas.HeaderRecord{
		MonitorId:        h.MonitorId,
		Model:            int64(h.Model),
		FirmwareVersion:  h.FirmwareVersion,
		FirmwareRevision: h.FirmwareRevision,
		CommVersion:      h.CommVersion,
		Volt220:          h.Volt220,
		VoltDC:           h.VoltDC,
		MainsDC:          h.MainsDC,
		PowerDownLevel:   h.PowerDownLevel,
		BlackoutLevel:    h.BlackoutLevel,
		MaxChannels:      h.MaxChannels,
	}
}

// MonitorNameAndIdToMonitorName converts an edi.HeaderRecord and MonitorName plus metadata
// into a schemas.MonitorName ready for BigQuery ingestion.
func MonitorNameAndIdToMonitorName(
	orgID, sgwID, tz, topic, pubsubID, deviceID string,
	pubsubTS time.Time,
	header schemas.HeaderRecord,
	rawMsg []byte,
	monitorData *MonitorNameAndId,
) schemas.MonitorName {
	return schemas.MonitorName{
		OrganizationIdentifier: orgID,
		SoftwareGatewayID:      sgwID,
		TZ:                     tz,
		Topic:                  topic,
		PubsubTimestamp:        pubsubTS,
		PubsubID:               pubsubID,
		DeviceID:               deviceID,
		Header:                 header,
		MonitorName:            monitorData.MonitorName,
		RawMessage:             rawMsg,
	}
}

// RmsStatusToRmsData converts an edi.HeaderRecord and RmsStatusRecord plus metadata
// into a schemas.RmsData ready for BigQuery ingestion.
func RmsStatusToRmsData(
	orgID, sgwID, tz, topic, pubsubID, deviceID string,
	pubsubTS time.Time,
	header schemas.HeaderRecord,
	rawMsg []byte,
	status *RmsStatusRecord,
) schemas.RmsData {
	return schemas.RmsData{
		OrganizationIdentifier: orgID,
		SoftwareGatewayID:      sgwID,
		TZ:                     tz,
		Topic:                  topic,
		PubsubTimestamp:        pubsubTS,
		PubsubID:               pubsubID,
		DeviceID:               deviceID,
		Header:                 header,

		IsFaulted:    status.IsFaulted,
		Fault:        status.Fault,
		FaultStatus:  status.FaultStatus,
		MonitorTime:  status.MonitorTime.UTC(),
		TemperatureF: int64(status.Temperature),

		ChannelGreenStatus:  boolSliceToStatusStruct(status.ChannelGreenStatus),
		ChannelYellowStatus: boolSliceToStatusStruct(status.ChannelYellowStatus),
		ChannelRedStatus:    boolSliceToStatusStruct(status.ChannelRedStatus),

		ChannelGreenVoltage:  intSliceToVoltageStruct(status.VoltagesGreen),
		ChannelYellowVoltage: intSliceToVoltageStruct(status.VoltagesYellow),
		ChannelRedVoltage:    intSliceToVoltageStruct(status.VoltagesRed),

		RawMessage: rawMsg,
	}
}

// RmsStatusToFaultNotification converts an edi.HeaderRecord and RmsStatusRecord plus metadata
// into a schemas.FaultNotification ready for BigQuery ingestion.
func RmsStatusToFaultNotification(
	orgID, sgwID, tz, topic, pubsubID, deviceID string,
	pubsubTS time.Time,
	header schemas.HeaderRecord,
	rawMsg []byte,
	status *RmsStatusRecord,
) schemas.FaultNotification {
	return schemas.FaultNotification{
		OrganizationIdentifier: orgID,
		SoftwareGatewayID:      sgwID,
		TZ:                     tz,
		Topic:                  topic,
		PubsubTimestamp:        pubsubTS,
		PubsubID:               pubsubID,
		DeviceID:               deviceID,
		Header:                 header,

		IsFaulted:    status.IsFaulted,
		Fault:        status.Fault,
		FaultStatus:  status.FaultStatus,
		MonitorTime:  status.MonitorTime.UTC(),
		TemperatureF: int64(status.Temperature),

		ChannelGreenStatus:  boolSliceToStatusStruct(status.ChannelGreenStatus),
		ChannelYellowStatus: boolSliceToStatusStruct(status.ChannelYellowStatus),
		ChannelRedStatus:    boolSliceToStatusStruct(status.ChannelRedStatus),

		ChannelGreenVoltage:  intSliceToVoltageStruct(status.VoltagesGreen),
		ChannelYellowVoltage: intSliceToVoltageStruct(status.VoltagesYellow),
		ChannelRedVoltage:    intSliceToVoltageStruct(status.VoltagesRed),

		RawMessage: rawMsg,
	}
}

// helper to read a bool slice safely
func getNullBool(s []bool, idx int) bigquery.NullBool {
	if idx < len(s) {
		return bigquery.NullBool{Bool: s[idx], Valid: true}
	}
	return bigquery.NullBool{Valid: false}
}

// boolSliceToStatusStruct maps up to 36 booleans into ChannelStatusStruct
func boolSliceToStatusStruct(s []bool) schemas.ChannelStatusStruct {
	return schemas.ChannelStatusStruct{
		Channel01: getNullBool(s, 0),
		Channel02: getNullBool(s, 1),
		Channel03: getNullBool(s, 2),
		Channel04: getNullBool(s, 3),
		Channel05: getNullBool(s, 4),
		Channel06: getNullBool(s, 5),
		Channel07: getNullBool(s, 6),
		Channel08: getNullBool(s, 7),
		Channel09: getNullBool(s, 8),
		Channel10: getNullBool(s, 9),
		Channel11: getNullBool(s, 10),
		Channel12: getNullBool(s, 11),
		Channel13: getNullBool(s, 12),
		Channel14: getNullBool(s, 13),
		Channel15: getNullBool(s, 14),
		Channel16: getNullBool(s, 15),
		Channel17: getNullBool(s, 16),
		Channel18: getNullBool(s, 17),
		Channel19: getNullBool(s, 18),
		Channel20: getNullBool(s, 19),
		Channel21: getNullBool(s, 20),
		Channel22: getNullBool(s, 21),
		Channel23: getNullBool(s, 22),
		Channel24: getNullBool(s, 23),
		Channel25: getNullBool(s, 24),
		Channel26: getNullBool(s, 25),
		Channel27: getNullBool(s, 26),
		Channel28: getNullBool(s, 27),
		Channel29: getNullBool(s, 28),
		Channel30: getNullBool(s, 29),
		Channel31: getNullBool(s, 30),
		Channel32: getNullBool(s, 31),
		Channel33: getNullBool(s, 32),
		Channel34: getNullBool(s, 33),
		Channel35: getNullBool(s, 34),
		Channel36: getNullBool(s, 35),
	}
}

// getNullInt returns a bigquery.NullInt64: valid only if idx < len(s).
func getNullInt(s []int64, idx int) bigquery.NullInt64 {
	if idx < len(s) {
		return bigquery.NullInt64{Int64: int64(s[idx]), Valid: true}
	}
	return bigquery.NullInt64{Valid: false}
}

// intSliceToVoltageStruct maps up to 36 ints into ChannelVoltageStruct,
// using NullInt64 so that missing indexes become SQL NULL.
func intSliceToVoltageStruct(s []int64) schemas.ChannelVoltageStruct {
	return schemas.ChannelVoltageStruct{
		Channel01: getNullInt(s, 0),
		Channel02: getNullInt(s, 1),
		Channel03: getNullInt(s, 2),
		Channel04: getNullInt(s, 3),
		Channel05: getNullInt(s, 4),
		Channel06: getNullInt(s, 5),
		Channel07: getNullInt(s, 6),
		Channel08: getNullInt(s, 7),
		Channel09: getNullInt(s, 8),
		Channel10: getNullInt(s, 9),
		Channel11: getNullInt(s, 10),
		Channel12: getNullInt(s, 11),
		Channel13: getNullInt(s, 12),
		Channel14: getNullInt(s, 13),
		Channel15: getNullInt(s, 14),
		Channel16: getNullInt(s, 15),
		Channel17: getNullInt(s, 16),
		Channel18: getNullInt(s, 17),
		Channel19: getNullInt(s, 18),
		Channel20: getNullInt(s, 19),
		Channel21: getNullInt(s, 20),
		Channel22: getNullInt(s, 21),
		Channel23: getNullInt(s, 22),
		Channel24: getNullInt(s, 23),
		Channel25: getNullInt(s, 24),
		Channel26: getNullInt(s, 25),
		Channel27: getNullInt(s, 26),
		Channel28: getNullInt(s, 27),
		Channel29: getNullInt(s, 28),
		Channel30: getNullInt(s, 29),
		Channel31: getNullInt(s, 30),
		Channel32: getNullInt(s, 31),
		Channel33: getNullInt(s, 32),
		Channel34: getNullInt(s, 33),
		Channel35: getNullInt(s, 34),
		Channel36: getNullInt(s, 35),
	}
}

// RmsEngineToBQ converts RmsEngineRecord to BigQuery schema
func RmsEngineToBQ(orgID string, sgwID string, tz string, topic string, pubsubID string, deviceID string, pubsubTS time.Time, header schemas.HeaderRecord, rawMsg []byte, engineData *RmsEngineRecord) schemas.RmsEngine {
	return schemas.RmsEngine{
		OrganizationIdentifier: orgID,
		SoftwareGatewayID:      sgwID,
		TZ:                     tz,
		Topic:                  topic,
		PubsubTimestamp:        pubsubTS,
		PubsubID:               pubsubID,
		DeviceID:               deviceID,
		Header:                 header,
		RmsVersion:             engineData.EngineVersion,
		RmsRevision:            engineData.EngineRevision,
		RawMessage:             rawMsg,
	}
}

// MacAddressToBQ converts metadata into a schemas.MacAddress ready for BigQuery ingestion.
func MacAddressToBQ(
	orgID, sgwID, tz, topic, pubsubID, deviceID string,
	pubsubTS time.Time,
	macAddress string,
) schemas.MacAddress {
	return schemas.MacAddress{
		OrganizationIdentifier: orgID,
		SoftwareGatewayID:      sgwID,
		TZ:                     tz,
		Topic:                  topic,
		PubsubTimestamp:        pubsubTS,
		PubsubID:               pubsubID,
		DeviceID:               deviceID,
		MacAddress:             macAddress,
	}
}

// ParseChannelStatus parses channel status for green, yellow, or red
func ParseChannelStatus(status uint32, maxChannels int) []bool {
	result := make([]bool, maxChannels)
	for j := 0; j < maxChannels; j++ {
		result[j] = IsBitSet(status, j)
	}
	return result
}

// CombineBytes combines a byte array into a uint32 in big endian order
func CombineBytes(bytes []byte) uint32 {
	combined := uint32(0)
	for _, b := range bytes {
		combined = (combined << 8) | uint32(b)
	}
	return combined
}

func NormalizeTimestamps(previousFail *FaultSignalSequenceRecords) {
	// this function normalizes the data -- the vb 6 code does the same
	const trace_ts_max = 65530
	const trace_interval int64 = 10
	var timestamp int64
	var timestampTr int64 // previous time stamp
	var tshift int64
	var prevTimestamp int64

	if len(previousFail.Records) == 0 {
		return
	}

	// Set the first record's timestamp to the trigger point
	timestampTr = previousFail.Records[0].Timestamp
	previousFail.Records[0].Timestamp = trace_ts_max // write 65530 as the trigger point

	// If there's only one record, we're done
	if len(previousFail.Records) == 1 {
		return
	}

	// Process the remaining records
	timestamp = previousFail.Records[1].Timestamp
	rollover := false

	// Correct rounding logic to the next trace_interval
	diff := timestampTr - timestamp
	if diff%trace_interval != 0 {
		timestampTr = timestamp + (diff/trace_interval+1)*trace_interval
	} else {
		timestampTr += trace_interval // This line ensures we always round up
	}

	tshift = trace_ts_max - timestampTr
	prevTimestamp = timestampTr

	for j := 1; j < len(previousFail.Records); j++ {
		timestampIn := previousFail.Records[j].Timestamp
		timestamp = timestampIn
		if prevTimestamp < timestampIn {
			rollover = true
		}
		prevTimestamp = timestampIn
		if rollover {
			timestamp = -(trace_ts_max - timestampIn)
		}
		timestamp += tshift
		if timestamp < 0 {
			timestamp = 0 // greater than 30 seconds, ignore
		}
		previousFail.Records[j].Timestamp = timestamp
	}
}

func Performcalcs(previousFail *FaultSignalSequenceRecords) (graphData *FaultSignalSequenceRecords) {
	graphData = new(FaultSignalSequenceRecords)
	if len(previousFail.Records) > 0 {
		// xfer over the base data
		graphData.FaultType = previousFail.FaultType
		graphData.RawMessage = previousFail.RawMessage
		graphData.DeviceModel = previousFail.DeviceModel

		// hydrate the complete graphData object.  -- NOTE!!!!  the purpose of all of this logic is to show state change at the boundary  !!!
		// this involves creating the trigger event which is 10ms before the regular event stream
		// then sliding the red, yellow, green data from the previousFail window up 1 timestamp in the graphData

		// create new trigger record
		var graphTraceBufferRecord TraceBuffer
		graphTraceBufferRecord.Timestamp = previousFail.Records[0].Timestamp + 10
		graphTraceBufferRecord.AcVoltage = previousFail.Records[0].AcVoltage
		graphTraceBufferRecord.EE_SF_RE = previousFail.Records[0].EE_SF_RE
		graphTraceBufferRecord.Reds = append(graphTraceBufferRecord.Reds, previousFail.Records[0].Reds...)
		graphTraceBufferRecord.Yellows = append(graphTraceBufferRecord.Yellows, previousFail.Records[0].Yellows...)
		graphTraceBufferRecord.Greens = append(graphTraceBufferRecord.Greens, previousFail.Records[0].Greens...)
		graphTraceBufferRecord.Walks = append(graphTraceBufferRecord.Walks, previousFail.Records[0].Walks...)
		graphData.Records = append(graphData.Records, graphTraceBufferRecord)

		// keep the timestamp, but slide the data up in the graph data record
		for i := 0; i < len(previousFail.Records)-1; i++ {
			graphTraceBufferRecord = TraceBuffer{}
			graphTraceBufferRecord.BufferRawBytes = previousFail.Records[i].BufferRawBytes
			graphTraceBufferRecord.Timestamp = previousFail.Records[i].Timestamp
			graphTraceBufferRecord.AcVoltage = previousFail.Records[i+1].AcVoltage
			graphTraceBufferRecord.EE_SF_RE = previousFail.Records[i+1].EE_SF_RE
			graphTraceBufferRecord.Reds = append(graphTraceBufferRecord.Reds, previousFail.Records[i+1].Reds...)
			graphTraceBufferRecord.Yellows = append(graphTraceBufferRecord.Yellows, previousFail.Records[i+1].Yellows...)
			graphTraceBufferRecord.Greens = append(graphTraceBufferRecord.Greens, previousFail.Records[i+1].Greens...)
			graphTraceBufferRecord.Walks = append(graphTraceBufferRecord.Walks, previousFail.Records[i+1].Walks...)
			graphData.Records = append(graphData.Records, graphTraceBufferRecord)
		}

		// append an additional last record in case the last record was a transition in state... if it was, then it would not be visible being the last record
		// the last record will just be a duplicate of the original last record with a timestamp of +10 (0.05s)
		graphTraceBufferRecord = TraceBuffer{}
		i := len(previousFail.Records) - 1
		graphTraceBufferRecord.Timestamp = previousFail.Records[i].Timestamp + 10
		graphTraceBufferRecord.AcVoltage = previousFail.Records[i].AcVoltage
		graphTraceBufferRecord.EE_SF_RE = previousFail.Records[i].EE_SF_RE
		graphTraceBufferRecord.Reds = append(graphTraceBufferRecord.Reds, previousFail.Records[i].Reds...)
		graphTraceBufferRecord.Yellows = append(graphTraceBufferRecord.Yellows, previousFail.Records[i].Yellows...)
		graphTraceBufferRecord.Greens = append(graphTraceBufferRecord.Greens, previousFail.Records[i].Greens...)
		graphTraceBufferRecord.Walks = append(graphTraceBufferRecord.Walks, previousFail.Records[i].Walks...)
		graphData.Records = append(graphData.Records, graphTraceBufferRecord)

		// at this point we have the new trigger record in place and the data window slid up... now it's time to perform the calcs
		// now, technically, the only thing we need to output from the is Elapsed Time - so we will keep an array of it -- ultimately, this will replace the timestamp on the final return
		elapsedTime := make([]float64, len(graphData.Records))

		for i = 0; i < len(graphData.Records); i++ {
			if i == 0 {
				// this is the trigger record
				elapsedTime[i] = 0.0
			} else {
				tickDelta := graphData.Records[i-1].Timestamp - graphData.Records[i].Timestamp
				tickDeltaXFifty := tickDelta * 50
				tickDeltaXFiftyDivivdedByTenThousand := float64(tickDeltaXFifty) / 10000.0
				elapsedComputation := elapsedTime[i-1] + tickDeltaXFiftyDivivdedByTenThousand
				elapsedTime[i] = math.Round(elapsedComputation*100) / 100
			}
		}

		// ok, now let's replace all timestamps with the time in seconds*100
		var index int
		exceeded30seconds := false
		for index = 0; index < len(graphData.Records); index++ {
			graphData.Records[index].Timestamp = int64(math.Round(elapsedTime[index] * 100))
			if graphData.Records[index].Timestamp > 3000 {
				exceeded30seconds = true
				break
			}
		}
		// replace the TraceData upto, but not including the index -- note:  if the above loop broke out early we may not get 30 seconds
		graphData.Records = graphData.Records[:index]

		// if the graph ran past 30 seconds, duplicate the last entry and mark it at 30 seconds so we can see the line run to 30 seconds
		if exceeded30seconds {
			i = index - 1
			if graphData.Records[i].Timestamp < 3000 {
				graphTraceBufferRecord = TraceBuffer{}
				graphTraceBufferRecord.Timestamp = 3000
				graphTraceBufferRecord.AcVoltage = previousFail.Records[i].AcVoltage
				graphTraceBufferRecord.EE_SF_RE = previousFail.Records[i].EE_SF_RE
				graphTraceBufferRecord.Reds = append(graphTraceBufferRecord.Reds, previousFail.Records[i].Reds...)
				graphTraceBufferRecord.Yellows = append(graphTraceBufferRecord.Yellows, previousFail.Records[i].Yellows...)
				graphTraceBufferRecord.Greens = append(graphTraceBufferRecord.Greens, previousFail.Records[i].Greens...)
				graphData.Records = append(graphData.Records, graphTraceBufferRecord)
			}
		}

		// lastly, check the front end to remove duplicate data -- the ends 0 and up to 30 are used to frame out the state so the user can see it (in case of state change)
		// compare 0 and the next record to check for dup -- if dup delete the 0+1 record (keeping the 0 record)
		// compare last and last -1 record to check for dup -- if last -1 dup, delete it (keep the ending record)
		if len(graphData.Records) >= 2 {
			// compare records 0 and 1
			src1 := 0
			src2 := 1
			sameReds := AreBoolSlicesEqual(graphData.Records[src1].Reds, graphData.Records[src2].Reds)
			sameYellows := AreBoolSlicesEqual(graphData.Records[src1].Yellows, graphData.Records[src2].Yellows)
			sameGreens := AreBoolSlicesEqual(graphData.Records[src1].Greens, graphData.Records[src2].Greens)
			sameWalks := AreBoolSlicesEqual(graphData.Records[src1].Walks, graphData.Records[src2].Walks)
			if sameReds && sameYellows && sameGreens && sameWalks {
				// remove the dup
				graphData.Records = append(graphData.Records[:src2], graphData.Records[src2+1:]...)
			}
		}

		// peek at the last 2 records to see if timestamp is duplicated -- if so, remove the duplicated record
		if len(graphData.Records) >= 2 {
			// compare records end and end - 1
			src1 := len(graphData.Records) - 1
			src2 := src1 - 1
			if graphData.Records[src1].Timestamp == graphData.Records[src2].Timestamp {
				// remove the dup entry.
				graphData.Records = append(graphData.Records[:src2], graphData.Records[src2+1:]...)
			}
		}

		// make sure the first timestamp is always base 0
		if len(graphData.Records) == 1 {
			graphData.Records[0].Timestamp = 0
		}
	}

	return graphData
}

// PerfStatsToBQ converts metadata into a schemas.GatewayPerformanceStatistics ready for BigQuery ingestion.
func PerfStatsToBQ(
	orgID, sgwID, tz, topic, pubsubID string,
	pubsubTS time.Time,
	messageTime time.Time,
	perf map[string]map[string]Stats,
	rawMessage []byte,
) schemas.GatewayPerformanceStatistics {
	rec := schemas.GatewayPerformanceStatistics{
		OrganizationIdentifier: orgID,
		SoftwareGatewayID:      sgwID,
		TZ:                     tz,
		Topic:                  topic,
		PubsubTimestamp:        pubsubTS,
		PubsubID:               pubsubID,
		MessageTime:            messageTime,
		Statistics:             toBQDeviceStats(perf),
		RawMessage:             rawMessage,
	}
	return rec
}

// toBQDeviceStats is a helper function for PerfStatsToBQ
func toBQDeviceStats(perf map[string]map[string]Stats) []schemas.DeviceStats {
	devStats := make([]schemas.DeviceStats, 0, len(perf))
	// Turn perf[deviceID][processName] -> DeviceStats/ProcessStats
	for deviceID, procMap := range perf {
		ds := schemas.DeviceStats{DeviceID: deviceID}

		for procName, stats := range procMap {
			ps := schemas.ProcessStats{
				ProcessName: procName,
				Stats:       toBQStats(stats),
			}
			ds.Processes = append(ds.Processes, ps)
		}

		devStats = append(devStats, ds)
	}

	return devStats
}

// toBQStats is a helper function for toBQDeviceStats
func toBQStats(e Stats) schemas.Stats {
	return schemas.Stats{
		Count:                   e.Count,
		LastExecutedTime:        e.LastExceutedTimeUTC,
		LastExecutedElapsedTime: e.LastExecutedElapsedTimeMS,
		TotalTime:               e.TotalTimeMS,
		MinTime:                 e.MinTimeMS,
		MaxTime:                 e.MaxTimeMS,
		ErrorCount:              e.ErrorCount,
	}
}

// GatewayLogToBQ converts gateway log records directly to BigQuery rows
func GatewayLogToBQ(
	orgID, sgwID, tz, topic, pubsubID string,
	pubsubTS time.Time,
	messageTime time.Time,
	records []softwaregateway.GatewayLogRecord,
) []schemas.GatewayLogMessage {
	out := make([]schemas.GatewayLogMessage, 0, len(records))

	for _, record := range records {
		// event_time already time.Time in new struct; fallback to messageTime if zero
		eventTime := record.EventTime
		if eventTime.IsZero() {
			eventTime = messageTime
		}

		// Marshal attributes to JSON
		var attributesJSON bigquery.NullJSON
		if len(record.Attributes) > 0 {
			attrBytes, err := jsonMarshal(record.Attributes)
			if err != nil {
				logger.Warnf("couldn't marshal attributes: %v", err)
			} else {
				attributesJSON = bigquery.NullJSON{JSONVal: string(attrBytes), Valid: true}
			}
		}

		row := schemas.GatewayLogMessage{
			OrganizationIdentifier: orgID,
			SoftwareGatewayID:      sgwID,
			TZ:                     tz,
			Topic:                  topic,
			PubsubTimestamp:        pubsubTS,
			PubsubID:               pubsubID,
			MessageTime:            messageTime,
			BootID:                 record.BootID,
			EventTime:              eventTime,
			Level:                  record.Level,
			Logger:                 record.Logger,
			Caller:                 record.Caller,
			Message:                record.Msg,
			LogDeviceID:            record.DeviceID,
			Version:                record.Version,
			WorkerID:               record.WorkerID,
			StackTrace:             record.Stacktrace,
			Attributes:             attributesJSON,
			RawMessage:             record.RawJSON,
		}
		out = append(out, row)
	}

	return out
}

// ConvertByteToDecimalFormat converts a byte to "high.low" format where
// the high and low nibbles of the hex representation become decimal digits.
// For example: 0x59 (89 decimal) becomes "5.9"
func ConvertByteToDecimalFormat(value byte) string {
	high := (value >> 4) & 0x0F // Extract high nibble
	low := value & 0x0F         // Extract low nibble
	return fmt.Sprintf("%d.%d", high, low)
}

// ConvertByteToString converts a byte to "highlow" format where
// the high and low nibbles of the hex representation become digits.
// For example: 0x01 becomes "01"
func ConvertByteToString(value byte) string {
	high := (value >> 4) & 0x0F // Extract high nibble
	low := value & 0x0F         // Extract low nibble
	return fmt.Sprintf("%d%d", high, low)
}

// ConvertByteStringToInt64 converts a string back to int64 (reverses ConvertByteToString)
// For example: "59" becomes 89 (0x59 in hex), "1010" becomes 170 (0xAA in hex)
func ConvertByteStringToInt64(str string) int64 {
	// The string format from ConvertByteToString can be:
	// - 2 chars: "59" (both nibbles 0-9)
	// - 3 chars: "105" (first nibble 10-15, second nibble 0-9) or "510" (first nibble 0-9, second nibble 10-15)
	// - 4 chars: "1015" (both nibbles 10-15)

	if len(str) < 2 || len(str) > 4 {
		return 0 // Invalid length
	}

	var high, low int
	var err error

	if len(str) == 2 {
		// Format: "59" - each character is a digit 0-9
		high, err = strconv.Atoi(string(str[0]))
		if err != nil || high < 0 || high > 9 {
			return 0
		}
		low, err = strconv.Atoi(string(str[1]))
		if err != nil || low < 0 || low > 9 {
			return 0
		}
	} else if len(str) == 3 {
		// Could be "105" (high=10, low=5) or "110" (high=1, low=10)
		// Try parsing last two chars as low nibble (10-15) first
		low, err = strconv.Atoi(str[1:])
		if err == nil && low >= 10 && low <= 15 {
			// Last two chars are low nibble (10-15), first char is high nibble (0-9)
			high, err = strconv.Atoi(string(str[0]))
			if err != nil || high < 0 || high > 9 {
				return 0
			}
		} else {
			// First two chars are high nibble (10-15), last char is low nibble (0-9)
			high, err = strconv.Atoi(str[:2])
			if err != nil || high < 10 || high > 15 {
				return 0
			}
			low, err = strconv.Atoi(string(str[2]))
			if err != nil || low < 0 || low > 9 {
				return 0
			}
		}
	} else if len(str) == 4 {
		// Format: "1015" - both nibbles are 10-15
		high, err = strconv.Atoi(str[:2])
		if err != nil || high < 10 || high > 15 {
			return 0
		}
		low, err = strconv.Atoi(str[2:])
		if err != nil || low < 10 || low > 15 {
			return 0
		}
	}

	// Combine high and low nibbles back to byte value
	result := (high << 4) | low
	return int64(result)
}

// ConvertDecimalFormatToInt64 converts a decimal format string back to int64
// For example: "5.9" becomes 89 (0x59 in hex)
func ConvertDecimalFormatToInt64(decimalStr string) int64 {
	// Parse the decimal format string
	parts := strings.Split(decimalStr, ".")
	if len(parts) != 2 {
		return 0 // Return 0 for invalid format
	}

	high, err1 := strconv.Atoi(parts[0])
	low, err2 := strconv.Atoi(parts[1])

	if err1 != nil || err2 != nil || high < 0 || low < 0 || high > 15 || low > 15 {
		return 0 // Return 0 for invalid values (including negative numbers)
	}

	// Combine high and low nibbles back to byte value
	result := (high << 4) | low
	return int64(result)
}

// ConvertBCDStringToInt converts a string that is a bcd "5.9" or "59" to an int. For example, "5.9" or "59" should be converted to byte 0x59 which is 89.
func ConvertBCDStringToInt(bcdString string) int64 {
	// Check if the string contains a decimal point (X.X format)
	if strings.Contains(bcdString, ".") {
		// Parse the decimal format string
		parts := strings.Split(bcdString, ".")
		if len(parts) != 2 {
			return 0 // Return 0 for invalid format
		}

		high, err1 := strconv.Atoi(parts[0])
		low, err2 := strconv.Atoi(parts[1])

		if err1 != nil || err2 != nil || high < 0 || low < 0 || high > 15 || low > 15 {
			return 0 // Return 0 for invalid values (including negative numbers)
		}

		// Combine high and low nibbles back to byte value
		result := (high << 4) | low
		return int64(result)
	} else {
		// Handle XX format (concatenated)
		if len(bcdString) != 2 {
			return 0 // Return 0 for invalid length
		}

		high, err1 := strconv.Atoi(string(bcdString[0]))
		low, err2 := strconv.Atoi(string(bcdString[1]))

		if err1 != nil || err2 != nil || high < 0 || low < 0 || high > 15 || low > 15 {
			return 0 // Return 0 for invalid values (including negative numbers)
		}

		// Combine high and low nibbles back to byte value
		result := (high << 4) | low
		return int64(result)
	}
}

func ParseVoltages[T ~int32 | ~int64](rawBytes []byte, header *HeaderRecord, normalize func(int, *HeaderRecord) T) []T {
	voltages := make([]T, len(rawBytes))
	for i, b := range rawBytes {
		voltages[i] = normalize(int(b), header)
	}
	return voltages
}
