package connect

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"os"
	"reflect"
	"regexp"
	"strings"
	"time"

	"github.com/lib/pq"

	"synapse-its.com/shared/logger"
)

var (
	osGetenv          = os.Getenv
	sqlOpen           = sql.Open
	timeSleepPostgres = time.Sleep
)

const (
	maxRetries = 3
	backoff    = 50 * time.Millisecond
)

// PostgresExecutor provides query execution methods for PostgreSQL.
type PostgresExecutor struct {
	DB     *sql.DB
	Config DatabaseConfig
	Ctx    context.Context
}

// This function initializes a PostgreSQL connection using the provided context
// and an optional config. If config is nil, values are read from environment variables.
var Postgres = func(ctx context.Context, config *DatabaseConfig) (*PostgresExecutor, error) {
	// Allow override via provided config; otherwise use environment variables.
	cfg := DatabaseConfig{
		DBName:      osGetenv("POSTGRES_DB"),
		Environment: osGetenv("ENVIRONMENT"),
		Namespace:   osGetenv("POSTGRES_NAMESPACE"),
	}

	// Apply overrides from the provided config (if any).
	if config != nil {
		if config.DBName != "" {
			cfg.DBName = config.DBName
		}
		if config.Environment != "" {
			cfg.Environment = config.Environment
		}
		if config.Namespace != "" {
			cfg.Namespace = config.Namespace
		}
	}

	postgresUser := osGetenv("POSTGRES_USER")
	postgresPassword := osGetenv("POSTGRES_PASSWORD")
	postgresDB := cfg.DBName
	postgresHost := osGetenv("POSTGRES_HOST")
	postgresPort := osGetenv("POSTGRES_PORT")

	// Set defaults if needed.
	if postgresUser == "" {
		return nil, ErrPostgresUserNotSet
	}
	if postgresPassword == "" {
		return nil, ErrPostgresPasswordNotSet
	}
	if postgresDB == "" {
		return nil, ErrPostgresDBNotSet
	}
	if postgresHost == "" {
		return nil, ErrPostgresHostNotSet
	}
	if postgresPort == "" {
		return nil, ErrPostgresPortNotSet
	}

	// Set sslmode based on host.
	sslmode := "require"
	if (postgresHost == "localhost") || (postgresHost == "127.0.0.1") || (postgresHost == "postgres") {
		sslmode = "disable"
	}

	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		postgresHost, postgresPort, postgresUser, postgresPassword, postgresDB, sslmode)

	var db *sql.DB
	var err error
	retryBackoff := time.Second

	// Connection retry logic.
	for i := 0; i < maxRetries; i++ {
		db, err = sqlOpen("postgres", connStr)
		if err == nil {
			err = db.PingContext(ctx)
			if err == nil {
				break
			}
		}
		logger.Infof("Attempt %d/%d: Failed to connect to PostgreSQL, retrying in %s: %v", i+1, maxRetries, retryBackoff, err)
		timeSleepPostgres(retryBackoff)
		retryBackoff *= 2
	}
	if err != nil {
		return nil, fmt.Errorf("%w after %d attempts: %v", ErrPostgresConnection, maxRetries, err)
	}

	return &PostgresExecutor{DB: db, Config: cfg, Ctx: ctx}, nil
}

// Exec executes a query that does not return rows.
func (p *PostgresExecutor) Exec(query string, args ...interface{}) (sql.Result, error) {
	query = p.ReplaceNamespace(query)
	return p.DB.ExecContext(p.Ctx, query, args...)
}

// ExecMultiple will break the `query` string into multiple individual queries.
// Notice that this function does not support parameters in the queries.
// All statements are executed within a single transaction to ensure atomicity.
func (p *PostgresExecutor) ExecMultiple(queries string) error {
	queries = p.ReplaceNamespace(queries)

	// Split the SQL file/string into individual statements.
	statements := SplitSQLStatements(queries)
	if len(statements) == 0 {
		return nil
	}

	// Start a transaction to ensure all statements succeed or all fail
	tx, err := p.DB.BeginTx(p.Ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	// Ensure rollback on any error
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Execute all statements within the transaction
	for _, stmt := range statements {
		if _, execErr := tx.ExecContext(p.Ctx, stmt); execErr != nil {
			err = fmt.Errorf("error executing statement %q: %w", stmt, execErr)
			return err
		}
	}

	// Commit the transaction
	if commitErr := tx.Commit(); commitErr != nil {
		err = fmt.Errorf("failed to commit transaction: %w", commitErr)
		return err
	}

	return nil
}

// QueryGeneric executes a query and returns the results as a slice of maps.
func (p *PostgresExecutor) QueryGeneric(query string, args ...interface{}) ([]map[string]interface{}, error) {
	query = p.ReplaceNamespace(query)
	rows, err := p.DB.QueryContext(p.Ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		return nil, err
	}

	var results []map[string]interface{}
	for rows.Next() {
		// Create a slice for scanning the values.
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}
		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, err
		}
		rowMap := make(map[string]interface{})
		for i, col := range columns {
			var v interface{}
			val := values[i]
			if b, ok := val.([]byte); ok {
				v = string(b)
			} else {
				v = val
			}
			rowMap[col] = v
		}
		results = append(results, rowMap)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return results, nil
}

// QueryRow executes a query and returns a single row as a map.
// If no row is found, it returns sql.ErrNoRows.
func (p *PostgresExecutor) QueryRow(query string, args ...interface{}) (map[string]interface{}, error) {
	// Substitute namespace in the query.
	query = p.ReplaceNamespace(query)

	// Execute the query.
	rows, err := p.DB.QueryContext(p.Ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// Get the column names.
	columns, err := rows.Columns()
	if err != nil {
		return nil, err
	}

	// Check if a row exists.
	if !rows.Next() {
		// Return error if no row is available.
		if err := rows.Err(); err != nil {
			return nil, err
		}
		return nil, sql.ErrNoRows
	}

	// Create slices for scanning the row values.
	values := make([]interface{}, len(columns))
	valuePtrs := make([]interface{}, len(columns))
	for i := range values {
		valuePtrs[i] = &values[i]
	}

	// Scan the row into the value pointers.
	if err := rows.Scan(valuePtrs...); err != nil {
		return nil, err
	}

	// Build the result map.
	rowMap := make(map[string]interface{})
	for i, col := range columns {
		var v interface{}
		val := values[i]
		if b, ok := val.([]byte); ok {
			v = string(b)
		} else {
			v = val
		}
		rowMap[col] = v
	}

	return rowMap, nil
}

// getFieldMap generates a mapping from database column names to struct field indices.
// It only needs to be computed once per query.
// Returns a map[string]int for column names to field indices.
// Returns a map[int]bool indicating whether each field is exported.
func getFieldMap(structType reflect.Type) (map[string]int, map[int]bool) {
	fieldMap := make(map[string]int, structType.NumField())
	exportedFields := make(map[int]bool, structType.NumField())

	for i := 0; i < structType.NumField(); i++ {
		field := structType.Field(i)
		tag := field.Tag.Get("db")
		if tag == "" {
			// Postgres will automatically treat everything as lower case unless
			// surrounded by quotes in the query.
			// Use lower case version of name of field without "db" tag
			tag = strings.ToLower(field.Name)
		}
		fieldMap[tag] = i

		// Record if field is exported (PkgPath == "" for exported fields)
		exportedFields[i] = (field.PkgPath == "")
	}
	return fieldMap, exportedFields
}

// pgScanRowsIntoStruct scans a single sql.Rows result into a struct using reflection and precomputed fieldMap.
// It safely checks for unexported fields and ensures robust error handling.
func pgScanRowsIntoStruct(rows *sql.Rows, structVal reflect.Value, fieldMap map[string]int, exportedFields map[int]bool, columns []string) error {
	scanTargets := make([]interface{}, len(columns))
	for i, colName := range columns {
		fieldIdx, ok := fieldMap[colName]
		if !ok {
			return fmt.Errorf("no matching struct field found for column %q", colName)
		}

		// Explicit check for exported fields to avoid panic
		if !exportedFields[fieldIdx] {
			return fmt.Errorf("cannot scan into unexported field %q", colName)
		}

		field := structVal.Field(fieldIdx)

		// Check if the field is a slice (PostgreSQL array)
		if field.Type().Kind() == reflect.Slice {
			// For slices, we need to use pq.Array to handle PostgreSQL arrays
			// pq.Array expects a pointer to the slice
			scanTargets[i] = pq.Array(field.Addr().Interface())
		} else {
			// For non-slice fields, use direct address
			scanTargets[i] = field.Addr().Interface()
		}
	}
	return rows.Scan(scanTargets...)
}

// QueryRowStruct executes a query and scans the first row into dest.
func (p *PostgresExecutor) QueryRowStruct(dest interface{}, query string, args ...interface{}) error {
	// Ensure dest is a non-nil pointer.
	destVal := reflect.ValueOf(dest)
	if destVal.Kind() != reflect.Ptr || destVal.IsNil() {
		return errors.New("dest must be a non-nil pointer")
	}

	// Ensure dest points to a struct.
	structVal := destVal.Elem()
	if structVal.Kind() != reflect.Struct {
		return errors.New("dest must point to a struct")
	}

	// Substitute namespace in the query.
	query = p.ReplaceNamespace(query)

	// Execute the query.
	rows, err := p.DB.QueryContext(p.Ctx, query, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	// Get the column names.
	columns, err := rows.Columns()
	if err != nil {
		return err
	}

	// Ensure a row exists.
	if !rows.Next() {
		if err := rows.Err(); err != nil {
			return err
		}
		return sql.ErrNoRows
	}

	// Create a field mapping from the reflect.Type to map[string]int for scanning.
	fieldMap, exportedFields := getFieldMap(structVal.Type())

	// Scan row to structVal based on fieldMap (and exportedFields to avoid panic) and columns
	return pgScanRowsIntoStruct(rows, structVal, fieldMap, exportedFields, columns)
}

// QueryGenericSlice executes a query and scans all returned rows into dest.
func (p *PostgresExecutor) QueryGenericSlice(dest interface{}, query string, args ...interface{}) error {
	// Ensure dest is a non-nil pointer.
	destVal := reflect.ValueOf(dest)
	if destVal.Kind() != reflect.Ptr || destVal.IsNil() {
		return errors.New("dest must be a non-nil pointer")
	}

	// Ensure dest points to a slice.
	sliceVal := destVal.Elem()
	if sliceVal.Kind() != reflect.Slice {
		return errors.New("dest must point to a slice")
	}

	// Ensure the slice element type is a struct.
	elemType := sliceVal.Type().Elem()
	if elemType.Kind() != reflect.Struct {
		return errors.New("dest slice element must be a struct")
	}

	// Substitute namespace in the query.
	query = p.ReplaceNamespace(query)

	// Execute the query.
	rows, err := p.DB.QueryContext(p.Ctx, query, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	// Get the column names.
	columns, err := rows.Columns()
	if err != nil {
		return err
	}

	// Create a field mapping from the reflect.Type to map[string]int for scanning.
	fieldMap, exportedFields := getFieldMap(elemType)

	// Preallocate 10 to cut down on early resizing at the cost of memory
	results := reflect.MakeSlice(sliceVal.Type(), 0, 10)

	// For each row, scan into a new struct and append struct to results
	for rows.Next() {
		newElem := reflect.New(elemType).Elem()
		if err := pgScanRowsIntoStruct(rows, newElem, fieldMap, exportedFields, columns); err != nil {
			return err
		}
		results = reflect.Append(results, newElem)
	}

	// Set sliceVal to results
	sliceVal.Set(results)
	return rows.Err()
}

// EscapeIdentifier safely escapes PostgreSQL identifiers.
func (p *PostgresExecutor) EscapeIdentifier(identifier string) string {
	return fmt.Sprintf(`"%s"`, strings.ReplaceAll(identifier, `"`, `""`))
}

// ReplaceNamespace scans the query for double-curly-brace table markers (e.g., {{results_table}})
// and replaces them with the properly escaped table name that incorporates the namespace.
// For example, if Config.Namespace is "DEV", then "{{results_table}}" becomes "DEVresults_table"
// (properly quoted).
func (p *PostgresExecutor) ReplaceNamespace(query string) string {
	re := regexp.MustCompile(`\{\{(.*?)\}\}`)
	return re.ReplaceAllStringFunc(query, func(match string) string {
		// Extract the table name inside the braces.
		tableName := strings.TrimSpace(match[2 : len(match)-2])
		// Prepend the namespace if set.
		fullTableName := tableName
		if p.Config.Namespace != "" {
			fullTableName = CombineTableNamespace(p.Config.Namespace, tableName)
		}
		// Use EscapeIdentifier to properly quote the table name.
		return p.EscapeIdentifier(fullTableName)
	})
}

// Close the DB connection.
func (p *PostgresExecutor) Close() error {
	return p.DB.Close()
}

// WithDeadlockRetry executes the provided function and retries it up to maxRetries times
// if a PostgreSQL deadlock error (SQLSTATE 40P01) is encountered.
//
// It uses exponential backoff (via timeSleepPostgres) between retries and relies on
// errors.As to detect *pq.Error with code 40P01. If the function succeeds or returns a
// non-deadlock error, it returns immediately. If all retries fail due to deadlocks,
// a final error is returned.
//
// This function assumes maxRetries and backoff are defined at the package level.
func WithDeadlockRetry(fn func() error) error {
	const deadlockSQLState = "40P01"

	for i := 0; i < maxRetries; i++ {
		err := fn()
		if err == nil {
			return nil
		}
		var pqErr *pq.Error
		if errors.As(err, &pqErr) && string(pqErr.Code) == deadlockSQLState {
			timeSleepPostgres(time.Duration(i+1) * backoff)
			continue
		}
		return err
	}
	return fmt.Errorf("max retries reached due to deadlocks")
}
