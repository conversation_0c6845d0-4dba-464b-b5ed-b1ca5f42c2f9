package shared

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"synapse-its.com/shared/connect"
)

// setupTables creates the shared tables.
func setupTables(pg connect.DatabaseExecutor) error {
	// First drop tables to ensure clean state
	_, err := pg.Exec("DROP TABLE IF EXISTS users, logs")
	if err != nil {
		return err
	}

	// Then create fresh tables
	setupQuery := `
		CREATE TABLE users (name TEXT, age INTEGER);
		CREATE TABLE logs (action TEXT);
	`
	_, err = pg.Exec(setupQuery)
	return err
}

// cleanupTables drops the specified tables.
func cleanupTables(pg connect.DatabaseExecutor) error {
	_, err := pg.Exec("DROP TABLE IF EXISTS users, logs")
	return err
}

// verifyData checks that the expected data exists in the database after successful execution
func verifyData(t *testing.T, pg connect.DatabaseExecutor, testName string) {
	t.Helper()

	switch testName {
	case "SuccessCase_AllQueriesSucceed":
		// Verify <PERSON> was inserted and updated
		rows, err := pg.QueryGeneric("SELECT name, age FROM users WHERE name = 'Alice'")
		require.NoError(t, err, "should be able to query users table")
		require.Len(t, rows, 1, "should have one Alice record")
		assert.Equal(t, "Alice", rows[0]["name"], "name should be Alice")
		assert.Equal(t, int64(31), rows[0]["age"], "age should be 31")

		// Verify log entry was created
		logRows, err := pg.QueryGeneric("SELECT action FROM logs WHERE action = 'User updated'")
		require.NoError(t, err, "should be able to query logs table")
		require.Len(t, logRows, 1, "should have one log entry")
		assert.Equal(t, "User updated", logRows[0]["action"], "log action should match")

	}
}

// verifyRollback checks that no data was committed after a failed transaction
func verifyRollback(t *testing.T, pg connect.DatabaseExecutor, testName string) {
	t.Helper()

	switch testName {
	case "FailureCase_OneQueryFails":
		// Verify Bob was not inserted (rollback worked)
		rows, err := pg.QueryGeneric("SELECT name FROM users WHERE name = 'Bob'")
		require.NoError(t, err, "should be able to query users table")
		assert.Empty(t, rows, "Bob should not exist due to rollback")

		// Verify no log entry was created
		logRows, err := pg.QueryGeneric("SELECT action FROM logs WHERE action = 'User update failed'")
		require.NoError(t, err, "should be able to query logs table")
		assert.Empty(t, logRows, "no log entry should exist due to rollback")

		// Verify tables are completely empty (complete rollback)
		allUsers, err := pg.QueryGeneric("SELECT COUNT(*) as count FROM users")
		require.NoError(t, err, "should be able to query user count")
		assert.Equal(t, int64(0), allUsers[0]["count"], "users table should be empty due to rollback")

		allLogs, err := pg.QueryGeneric("SELECT COUNT(*) as count FROM logs")
		require.NoError(t, err, "should be able to query log count")
		assert.Equal(t, int64(0), allLogs[0]["count"], "logs table should be empty due to rollback")
	}
}

// TestExecuteQueriesInTransaction uses table-driven testing to cover the two cases.
func TestExecuteQueriesInTransaction(t *testing.T) {
	// Integration test - should run against real database
	t.Log("Starting TestExecuteQueriesInTransaction")
	t.Logf("POSTGRES_DB environment variable: %s", os.Getenv("POSTGRES_DB"))

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Create postgres connection with test namespace
	config := connect.DatabaseConfig{
		Environment: "test",
		Namespace:   "T_EXECMULTIPLE_TRANSACTION",
		DBName:      os.Getenv("POSTGRES_DB"),
	}

	t.Logf("PostgreSQL config: Environment=%s, Namespace=%s, DBName=%s",
		config.Environment, config.Namespace, config.DBName)
	t.Log("Attempting to connect to PostgreSQL...")

	pg, err := connect.Postgres(ctx, &config)
	require.NoError(t, err, "should connect to postgres")
	t.Log("Successfully connected to PostgreSQL")
	defer pg.Close()

	// Create test tables once before all tests
	t.Log("Setting up test tables...")
	err = setupTables(pg)
	require.NoError(t, err, "should create test tables")
	t.Log("Test tables created successfully")

	// Register cleanup for shared tables using t.Cleanup
	t.Cleanup(func() {
		t.Log("Cleaning up test tables...")
		err := cleanupTables(pg)
		if err != nil {
			t.Logf("Cleanup warning: %v", err)
		} else {
			t.Log("Cleanup completed successfully")
		}
	})

	// Define test cases
	tests := []struct {
		name    string
		queries string
		wantErr bool
	}{
		{
			name: "SuccessCase_AllQueriesSucceed",
			queries: `
				INSERT INTO users (name, age) VALUES ('Alice', 30);
				UPDATE users SET age = 31 WHERE name = 'Alice';
				INSERT INTO logs (action) VALUES ('User updated');
			`,
			wantErr: false,
		},
		{
			name: "FailureCase_OneQueryFails",
			queries: `
				INSERT INTO users (name, age) VALUES ('Bob', 25);
				UPDATE nonexistent_table SET age = 26 WHERE name = 'Bob';
				INSERT INTO logs (action) VALUES ('User update failed');
			`,
			wantErr: true,
		},
	}

	// Run test cases sequentially to avoid race conditions
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean up any existing data before each test
			t.Log("Cleaning up data before test...")
			_, err := pg.Exec("DELETE FROM users")
			if err != nil {
				t.Logf("Users cleanup warning: %v", err)
			}
			_, err = pg.Exec("DELETE FROM logs")
			if err != nil {
				t.Logf("Logs cleanup warning: %v", err)
			}

			// Verify tables are empty before test
			userCount, _ := pg.QueryGeneric("SELECT COUNT(*) as count FROM users")
			logCount, _ := pg.QueryGeneric("SELECT COUNT(*) as count FROM logs")
			t.Logf("Pre-test state: users=%v, logs=%v", userCount[0]["count"], logCount[0]["count"])

			t.Logf("Running test case: %s", tt.name)
			t.Logf("Queries to execute:\n%s", tt.queries)

			err = pg.ExecMultiple(tt.queries)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExecMultiple() error = %v, wantErr %v", err, tt.wantErr)
			}

			if err != nil {
				t.Logf("Expected error occurred: %v", err)
			} else {
				t.Log("All queries executed successfully")
			}

			// Additional verification (e.g., check database state)
			if !tt.wantErr {
				// Verify data was committed
				t.Log("Verifying data was committed...")
				verifyData(t, pg, tt.name)
				t.Log("Data verification passed")
			} else {
				// Verify rollback (no changes in DB)
				t.Log("Verifying rollback occurred...")
				verifyRollback(t, pg, tt.name)
				t.Log("Rollback verification passed")
			}
		})
	}
}
