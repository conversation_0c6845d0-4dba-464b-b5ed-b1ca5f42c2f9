package organization

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	invitesRest "synapse-its.com/shared/rest/onramp/invites"
	"synapse-its.com/testing/integration/onramp"
	"synapse-its.com/testing/utils"
)

func TestOrganizationInvitesCreate_Success(t *testing.T) {
	t.Parallel()

	// 1. Wait for onramp service to be ready
	ctx := context.Background()
	require.NoError(t, utils.AwaitOnramp(ctx, 5*time.Second), "onramp service should be ready")

	// 2. Setup database connection
	pg, err := connect.Postgres(ctx, nil)
	require.NoError(t, err, "Should be able to create database connection")
	defer pg.Close()

	// 3. Setup test client and session
	client, sessionCookie := setupIntegrationTest(t)

	// 4. Generate unique test identifier for this specific test run
	testID := uuid.New().String()[:8]

	// 5. Create test organization for this test
	orgName := fmt.Sprintf("TEST_Invite_Org_%s", testID)
	orgID := createTestOrganizationSQL(t, pg, orgName, "Test organization for invite tests", "municipality")

	// 6. Cleanup test data after test
	t.Cleanup(func() {
		// Hard delete the test organization
		_, err := pg.Exec("DELETE FROM {{Organization}} WHERE id = $1", orgID)
		if err != nil {
			t.Logf("Failed to cleanup test organization %s: %v", orgID, err)
		}
	})

	// 7. Generate unique test data with consistent naming
	const testDataPrefix = "TEST_"
	// Use existing email from AuthMethod for "pending" case
	existingEmail := "<EMAIL>"
	// Use new email for "new user" case
	newUserEmail := fmt.Sprintf("<EMAIL>", testID)

	// 8. Track ALL created resources for cleanup
	var createdInvites []uuid.UUID

	// 9. Test invite creation for both cases using table-driven tests
	t.Run("Invite creation should set correct status based on email existence", func(t *testing.T) {
		// Define test cases
		testCases := []struct {
			name           string
			email          string
			message        string
			expectedStatus string
			description    string
		}{
			{
				name:           "User with email in AuthMethod",
				email:          existingEmail,
				message:        "Welcome to our organization!",
				expectedStatus: invitesRest.StatusPending,
				description:    "Status should be 'pending' for user with email in AuthMethod",
			},
			{
				name:           "User without email in AuthMethod",
				email:          newUserEmail,
				message:        "Welcome new user!",
				expectedStatus: invitesRest.StatusNewUser,
				description:    "Status should be 'new user' for user without email in AuthMethod",
			},
		}

		// Run test cases
		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				// Create invite request
				request := invitesRest.CreateInviteRequest{
					Email:            tc.email,
					InviterID:        onramp.DemoUserID,
					Message:          stringPtr(tc.message),
					OrganizationRole: onramp.DemoRoleID,
					ExpiredDays:      intPtr(7),
				}

				// Make request with detailed logging
				url := fmt.Sprintf("%s/organizations/%s/invites", baseURL, orgID)
				t.Logf("Request URL: %s", url)
				requestBody, _ := json.Marshal(request)
				t.Logf("Request Body: %s", string(requestBody))

				resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, request)

				// Response logging
				t.Logf("Response Status: %d", resp.StatusCode)
				body, _ := io.ReadAll(resp.Body)
				resp.Body = io.NopCloser(bytes.NewReader(body))
				t.Logf("Response Body: %s", string(body))

				// Verify response
				assert.Equal(t, http.StatusOK, resp.StatusCode, "Should be able to create invite")

				// Parse and validate response
				var inviteResp invitesRest.InviteResponse
				apiResp := parseAPIResponse(t, resp, &inviteResp)
				assert.Equal(t, "success", apiResp.Status)
				assert.Equal(t, 200, apiResp.Code)
				assert.Equal(t, "Request Succeeded", apiResp.Message)

				// Verify invite properties
				assert.Equal(t, tc.email, inviteResp.Email)
				assert.Equal(t, onramp.DemoUserID, inviteResp.InviterID)
				assert.Equal(t, onramp.DemoRoleID, inviteResp.CustomRoleID)
				assert.Equal(t, tc.expectedStatus, inviteResp.Status, tc.description)
				assert.NotNil(t, inviteResp.Message)
				assert.Equal(t, tc.message, *inviteResp.Message)

				// Track for cleanup
				createdInvites = append(createdInvites, inviteResp.ID)
			})
		}
	})

	// 9. Post-test verification and cleanup
	t.Cleanup(func() {
		// Hard delete invites
		if len(createdInvites) > 0 {
			deleteInvitesQuery := `DELETE FROM {{Invite}} WHERE id = ANY($1)`
			_, err := pg.Exec(deleteInvitesQuery, createdInvites)
			if err != nil {
				t.Logf("Warning: Failed to cleanup test invites: %v", err)
			}
		}
	})
}

func TestOrganizationInvitesList_Success(t *testing.T) {
	t.Parallel()

	// 1. Wait for onramp service to be ready
	ctx := context.Background()
	require.NoError(t, utils.AwaitOnramp(ctx, 5*time.Second), "onramp service should be ready")

	// 2. Setup database connection
	pg, err := connect.Postgres(ctx, nil)
	require.NoError(t, err, "Should be able to create database connection")
	defer pg.Close()

	// 3. Setup test client and session
	client, sessionCookie := setupIntegrationTest(t)

	// 4. Generate unique test identifier for this specific test run
	testID := uuid.New().String()[:8]

	// 5. Create test organization for this test
	orgName := fmt.Sprintf("TEST_Invite_List_Org_%s", testID)
	orgID := createTestOrganizationSQL(t, pg, orgName, "Test organization for invite list tests", "municipality")

	// 6. Cleanup test data after test
	t.Cleanup(func() {
		// Hard delete the test organization
		_, err := pg.Exec("DELETE FROM {{Organization}} WHERE id = $1", orgID)
		if err != nil {
			t.Logf("Failed to cleanup test organization %s: %v", orgID, err)
		}
	})

	// 7. Test listing invites
	t.Run("List invites should succeed", func(t *testing.T) {
		// Make request with detailed logging
		url := fmt.Sprintf("%s/organizations/%s/invites", baseURL, orgID)
		t.Logf("Request URL: %s", url)

		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

		// Response logging
		t.Logf("Response Status: %d", resp.StatusCode)
		body, _ := io.ReadAll(resp.Body)
		resp.Body = io.NopCloser(bytes.NewReader(body))
		t.Logf("Response Body: %s", string(body))

		// Verify response
		assert.Equal(t, http.StatusOK, resp.StatusCode, "Should be able to list invites")

		// Parse and validate response
		var invitesResp []invitesRest.InviteResponse
		apiResp := parseAPIResponse(t, resp, &invitesResp)
		assert.Equal(t, "success", apiResp.Status)
		assert.Equal(t, 200, apiResp.Code)
		assert.Equal(t, "Request Succeeded", apiResp.Message)

		// Verify response structure
		assert.NotNil(t, invitesResp, "Should return invites list")
		t.Logf("Found %d invites", len(invitesResp))
	})
}

func TestOrganizationInvitesRevoke_Success(t *testing.T) {
	t.Parallel()

	// 1. Wait for onramp service to be ready
	ctx := context.Background()
	require.NoError(t, utils.AwaitOnramp(ctx, 5*time.Second), "onramp service should be ready")

	// 2. Setup database connection
	pg, err := connect.Postgres(ctx, nil)
	require.NoError(t, err, "Should be able to create database connection")
	defer pg.Close()

	// 3. Setup test client and session
	client, sessionCookie := setupIntegrationTest(t)

	// 4. Generate unique test identifier for this specific test run
	testID := uuid.New().String()[:8]

	// 5. Create test organization for this test
	orgName := fmt.Sprintf("TEST_Invite_Revoke_Org_%s", testID)
	orgID := createTestOrganizationSQL(t, pg, orgName, "Test organization for invite revoke tests", "municipality")

	// 6. Cleanup test data after test
	t.Cleanup(func() {
		// Hard delete the test organization
		_, err := pg.Exec("DELETE FROM {{Organization}} WHERE id = $1", orgID)
		if err != nil {
			t.Logf("Failed to cleanup test organization %s: %v", orgID, err)
		}
	})

	// 7. Generate unique test data with consistent naming
	const testDataPrefix = "TEST_"
	inviteEmail := fmt.Sprintf("<EMAIL>", testID)

	// 7. Track ALL created resources for cleanup
	var createdInvites []uuid.UUID

	// 8. First create an invite to revoke
	t.Run("Create invite for revocation test", func(t *testing.T) {
		// Create invite request
		request := invitesRest.CreateInviteRequest{
			Email:            inviteEmail,
			InviterID:        onramp.DemoUserID,
			OrganizationRole: onramp.DemoRoleID,
			ExpiredDays:      intPtr(7),
		}

		url := fmt.Sprintf("%s/organizations/%s/invites", baseURL, orgID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, request)

		require.Equal(t, http.StatusOK, resp.StatusCode, "Should be able to create invite")

		var inviteResp invitesRest.InviteResponse
		parseAPIResponse(t, resp, &inviteResp)
		createdInvites = append(createdInvites, inviteResp.ID)
	})

	// 9. Test revoking invite
	t.Run("Revoke invite should succeed", func(t *testing.T) {
		// Check if we have any created invites
		require.NotEmpty(t, createdInvites, "Should have at least one created invite to revoke")

		// Use the first created invite
		inviteID := createdInvites[0]

		// Create revoke request
		request := invitesRest.RevokeInviteRequest{
			Actor: "test-actor",
		}

		// Make request with detailed logging
		url := fmt.Sprintf("%s/organizations/%s/invites/%s", baseURL, orgID, inviteID)
		t.Logf("Request URL: %s", url)
		requestBody, _ := json.Marshal(request)
		t.Logf("Request Body: %s", string(requestBody))

		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodDelete, url, request)

		// Response logging
		t.Logf("Response Status: %d", resp.StatusCode)
		body, _ := io.ReadAll(resp.Body)
		resp.Body = io.NopCloser(bytes.NewReader(body))
		t.Logf("Response Body: %s", string(body))

		// Verify response
		assert.Equal(t, http.StatusOK, resp.StatusCode, "Should be able to revoke invite")

		// Parse and validate response
		var revokeResp response.SuccessResponse
		apiResp := parseAPIResponse(t, resp, &revokeResp)
		assert.Equal(t, "success", apiResp.Status)
		assert.Equal(t, 200, apiResp.Code)
		assert.Equal(t, "Request Succeeded", apiResp.Message)
	})

	// 10. Post-test verification and cleanup
	t.Cleanup(func() {
		// Cleanup using same method as data creation (API or direct DB)
		for _, inviteID := range createdInvites {
			cleanupTestDataViaAPI(t, client, sessionCookie, inviteID)
		}

		// Verify cleanup was successful
		for _, inviteID := range createdInvites {
			verifyTestDataCleanup(t, inviteID)
		}

		// Final cleanup sweep for this specific test
		cleanupTestDataForTest(t, testOrg1ID, testID)
		verifyTestCleanupComplete(t, testOrg1ID, testID)
	})
}

func TestOrganizationInvitesResend_Success(t *testing.T) {
	t.Parallel()

	// 1. Wait for onramp service to be ready
	ctx := context.Background()
	require.NoError(t, utils.AwaitOnramp(ctx, 5*time.Second), "onramp service should be ready")

	// 2. Setup database connection
	pg, err := connect.Postgres(ctx, nil)
	require.NoError(t, err, "Should be able to create database connection")
	defer pg.Close()

	// 3. Setup test client and session
	client, sessionCookie := setupIntegrationTest(t)

	// 4. Generate unique test identifier for this specific test run
	testID := uuid.New().String()[:8]

	// 5. Create test organization for this test
	orgName := fmt.Sprintf("TEST_Invite_Resend_Org_%s", testID)
	orgID := createTestOrganizationSQL(t, pg, orgName, "Test organization for invite resend tests", "municipality")

	// 6. Cleanup test data after test
	t.Cleanup(func() {
		// Hard delete the test organization
		_, err := pg.Exec("DELETE FROM {{Organization}} WHERE id = $1", orgID)
		if err != nil {
			t.Logf("Failed to cleanup test organization %s: %v", orgID, err)
		}
	})

	// 7. Generate unique test data with consistent naming
	const testDataPrefix = "TEST_"
	inviteEmail := fmt.Sprintf("<EMAIL>", testID)

	// 7. Track ALL created resources for cleanup
	var createdInvites []uuid.UUID

	// 8. First create an invite to resend
	t.Run("Create invite for resend test", func(t *testing.T) {
		// Create invite request
		request := invitesRest.CreateInviteRequest{
			Email:            inviteEmail,
			InviterID:        onramp.DemoUserID,
			OrganizationRole: onramp.DemoRoleID,
			ExpiredDays:      intPtr(7),
		}

		url := fmt.Sprintf("%s/organizations/%s/invites", baseURL, orgID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, request)

		require.Equal(t, http.StatusOK, resp.StatusCode, "Should be able to create invite")

		var inviteResp invitesRest.InviteResponse
		parseAPIResponse(t, resp, &inviteResp)
		createdInvites = append(createdInvites, inviteResp.ID)
	})

	// 9. Test resending invite
	t.Run("Resend invite should succeed", func(t *testing.T) {
		// Use the first created invite
		inviteID := createdInvites[0]

		// Create resend request
		request := invitesRest.ResendInviteRequest{
			Actor:       "test-actor",
			Message:     stringPtr("Resending your invitation"),
			ExpiredDays: intPtr(14),
		}

		// Make request with detailed logging
		url := fmt.Sprintf("%s/organizations/%s/invites/%s/resend", baseURL, orgID, inviteID)
		t.Logf("Request URL: %s", url)
		requestBody, _ := json.Marshal(request)
		t.Logf("Request Body: %s", string(requestBody))

		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, request)

		// Response logging
		t.Logf("Response Status: %d", resp.StatusCode)
		body, _ := io.ReadAll(resp.Body)
		resp.Body = io.NopCloser(bytes.NewReader(body))
		t.Logf("Response Body: %s", string(body))

		// Verify response
		assert.Equal(t, http.StatusOK, resp.StatusCode, "Should be able to resend invite")

		// Parse and validate response
		var resendResp response.SuccessResponse
		apiResp := parseAPIResponse(t, resp, &resendResp)
		assert.Equal(t, "success", apiResp.Status)
		assert.Equal(t, 200, apiResp.Code)
		assert.Equal(t, "Request Succeeded", apiResp.Message)
	})

	// 10. Post-test verification and cleanup
	t.Cleanup(func() {
		// Cleanup using same method as data creation (API or direct DB)
		for _, inviteID := range createdInvites {
			cleanupTestDataViaAPI(t, client, sessionCookie, inviteID)
		}

		// Verify cleanup was successful
		for _, inviteID := range createdInvites {
			verifyTestDataCleanup(t, inviteID)
		}

		// Final cleanup sweep for this specific test
		cleanupTestDataForTest(t, testOrg1ID, testID)
		verifyTestCleanupComplete(t, testOrg1ID, testID)
	})
}
