package subscriptions

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/pubsubdata"
)

// TestDefaultSubscriptions_GoroutineCount tests that the total number of goroutines across all subscriptions does not exceed 100
// This is a check to ensure the pubsub client is not overwhelmed by the number of goroutines
// This was seen as pubsub messages not being acked in time and causing unwanted reprocessing and duplicate messages
// Known issue in the go client library:
// https://github.com/googleapis/google-cloud-go/wiki/Fine-Tuning-PubSub-Receive-Performance#subscriptionreceivesettingsnumgoroutines
func TestDefaultSubscriptions_GoroutineCount(t *testing.T) {
	t.Parallel()

	subs := DefaultSubscriptions()

	// Count total goroutines across all subscriptions
	totalGoroutines := 0
	subscriptionGoroutineCounts := make(map[string]int)

	for _, sub := range subs {
		if sub.ReceiveSettings != nil {
			goroutines := sub.ReceiveSettings.NumGoroutines
			totalGoroutines += goroutines
			subscriptionGoroutineCounts[sub.Name] = goroutines
		}
	}

	// Log the breakdown for debugging
	t.Logf("Total goroutines across all subscriptions: %d", totalGoroutines)
	for name, count := range subscriptionGoroutineCounts {
		t.Logf("Subscription %s has %d goroutines", name, count)
	}

	// Assert that total goroutines don't exceed 100
	assert.LessOrEqual(t, totalGoroutines, 100,
		"Total goroutines (%d) across all subscriptions should not exceed 100: https://github.com/googleapis/google-cloud-go/wiki/Fine-Tuning-PubSub-Receive-Performance#subscriptionreceivesettingsnumgoroutines", totalGoroutines)

	// Additional validation: ensure we have at least some goroutines configured
	assert.Greater(t, totalGoroutines, 0,
		"At least one subscription should have goroutines configured")
}

// TestDefaultSubscriptions_MatchSharedSchemas validates that all subscription names
// in DefaultSubscriptions() match the subscription names defined in the shared schemas
func TestDefaultSubscriptions_MatchSharedSchemas(t *testing.T) {
	t.Parallel()

	subs := DefaultSubscriptions()

	// Create a set of valid subscription names from the shared schemas
	validSubscriptionNames := make(map[string]bool)
	for subscriptionName := range pubsubdata.PubsubSubscriptions {
		validSubscriptionNames[subscriptionName] = true
	}

	// Validate each subscription name exists in the shared schemas
	for _, sub := range subs {
		if !validSubscriptionNames[sub.Name] {
			t.Errorf("Subscription name '%s' from DefaultSubscriptions() not found in shared PubsubSubscriptions", sub.Name)
		}
	}

	// Additional validation: ensure we have subscriptions for all shared schema subscriptions
	// This will need to be removed if we ever want to add another microservice
	subscriptionNamesInDefault := make(map[string]bool)
	for _, sub := range subs {
		subscriptionNamesInDefault[sub.Name] = true
	}

	for subscriptionName := range pubsubdata.PubsubSubscriptions {
		if !subscriptionNamesInDefault[subscriptionName] {
			t.Errorf("Shared subscription '%s' not found in DefaultSubscriptions()", subscriptionName)
		}
	}
}
