package dlqPubsub

import (
	"context"
	"encoding/json"
	"fmt"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc       func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	BatchGetter         func(ctx context.Context) (bqbatch.Batcher, error)
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector       ConnectorFunc
	ParseAttributes ParseAttributesFunc
	GetBatch        BatchGetter
}

// Inserts any pubsub messages that failed to process into a database for monitoring
func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, sub connect.PsSubscription) {
	return func(ctx context.Context, sub connect.PsSubscription) {
		batch, err := deps.GetBatch(ctx)
		if err != nil {
			logger.Errorf("Error getting batch: %v", err)
			return
		}
		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s MessageID: %s. Message Data:%s", string(sub.ID()), string(msg.ID), string(msg.Data))

			// Unwrap original pubsub message
			var msgWrapper pubsubdata.PubsubMessageWrapper
			if errUn := json.Unmarshal([]byte(msg.Data), &msgWrapper); errUn != nil {
				// If this path is being hit, DLQ is failing, we should probably just save what we can
				// Don't bother to set all fields, just set what we can and bail
				logger.Errorf("Error parsing DLQ wrapper: %v", errUn)
				if errBa := batch.Add(schemas.DlqMessages{
					Topic:           sub.ID(),                                            // Set topic to this subscription name to indicate DLQ issue
					Data:            msg.Data,                                            // Just a dump of the whole original payload
					DLQReason:       fmt.Sprintf("Error parsing DLQ wrapper: %v", errUn), // Set new error reason
					PubsubTimestamp: msg.PublishTime,
					PubsubID:        msg.ID,
				}); errBa != nil {
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Parse original attributes
			commonAttrs, _, errPa := deps.ParseAttributes(msgWrapper.Attributes)
			if errPa != nil {
				logger.Errorf("Error parsing DLQ attributes: %v", errPa)
				if errBa := batch.Add(schemas.DlqMessages{
					Topic:           sub.ID(), // Set topic to this subscription name to indicate DLQ issue
					ID:              msgWrapper.ID,
					Data:            msgWrapper.Data,
					Attributes:      etlShared.AttributesMapToBq(msgWrapper.Attributes),
					PublishTime:     msgWrapper.PublishTime,
					OrderingKey:     msgWrapper.OrderingKey,
					DeliveryAttempt: msgWrapper.DeliveryAttempt,
					DLQReason:       fmt.Sprintf("Unable to parse attributes: %v", errPa),
					PubsubTimestamp: msg.PublishTime,
					PubsubID:        msg.ID,
				}); errBa != nil {
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Add to BigQuery batch
			if errBa := batch.Add(schemas.DlqMessages{
				Topic:           commonAttrs.Topic,
				ID:              msgWrapper.ID,
				Data:            msgWrapper.Data,
				Attributes:      etlShared.AttributesMapToBq(msgWrapper.Attributes),
				PublishTime:     msgWrapper.PublishTime,
				OrderingKey:     msgWrapper.OrderingKey,
				DeliveryAttempt: msgWrapper.DeliveryAttempt,
				DLQReason:       commonAttrs.DLQReason,
				PubsubTimestamp: msg.PublishTime,
				PubsubID:        msg.ID,
			}); errBa != nil {
				logger.Errorf("Error adding to BigQuery batch: %v", errBa)
				msg.Nack()
				return
			}

			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription %s: %v", sub.ID(), err)
		}
	}
}

// Handler is the production-ready Pub/Sub processor using real dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:       connect.GetConnections,
	ParseAttributes: pubsubdata.ParseAttributes,
	GetBatch:        bqbatch.GetBatch,
})
