package dlqPubsub

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
)

func TestDLQHandlerWithDeps(t *testing.T) {
	tests := []struct {
		name             string
		connectorErr     error
		batchErr         error
		receiveErr       error
		unmarshalBadJSON bool
		attrErr          error
		addErr           error
		wantAdds         int
	}{
		{"Connection error", errors.New("conn fail"), nil, nil, false, nil, nil, 0},
		{"Batch error", nil, errors.New("batch fail"), nil, false, nil, nil, 0},
		{"Receive error", nil, nil, errors.New("receive fail"), false, nil, nil, 0},
		{"Bad JSON", nil, nil, nil, true, nil, nil, 1},
		{"Attribute error", nil, nil, nil, false, errors.New("attr parse fail"), nil, 1},
		{"Add error - Unmarshal path", nil, nil, nil, true, nil, errors.New("add fail"), 1},
		{"Add error - Attr parse path", nil, nil, nil, false, errors.New("attr fail"), errors.New("add fail"), 1},
		{"Add error - Happy path", nil, nil, nil, false, nil, errors.New("add fail"), 1},
		{"Happy path", nil, nil, nil, false, nil, nil, 1},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			ctx := context.Background()
			addCount := 0

			conn := mocks.FakeConns()
			client := conn.Pubsub.(*mocks.FakePubsubClient)
			client.ReceiveError = tc.receiveErr

			batch, _ := mocks.FakeBatch(ctx)
			batch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				addCount++
				return tc.addErr
			}

			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conn, tc.connectorErr
				},
				ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{Topic: "source", DLQReason: "reason"}, pubsubdata.HeaderDetails{}, nil
				},
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					if tc.batchErr != nil {
						return nil, tc.batchErr
					}
					return batch, nil
				},
			}

			if tc.connectorErr == nil && tc.batchErr == nil && tc.receiveErr == nil {
				topic := client.Topic("test-topic")
				client.CreateSubscription(ctx, "test-sub", connect.SubscriptionConfig{Topic: topic})

				var data []byte
				if tc.unmarshalBadJSON {
					data = []byte("{invalid json}")
				} else {
					wrapper := pubsubdata.PubsubMessageWrapper{
						ID:              "msg-id",
						Data:            []byte("payload"),
						Attributes:      map[string]string{"dlq_reason": "test"},
						PublishTime:     time.Now(),
						DeliveryAttempt: 1,
						OrderingKey:     "key",
					}
					data, _ = json.Marshal(wrapper)
				}

				topic.Publish(ctx, &pubsub.Message{
					Data:        data,
					ID:          "msg-id",
					PublishTime: time.Now(),
				})
			}

			handler := HandlerWithDeps(deps)
			// always get a PsSubscription from the fake client
			topic := client.Topic("test-topic")
			fakeSub, _ := client.CreateSubscription(ctx, "test-sub", connect.SubscriptionConfig{Topic: topic})
			handler(ctx, fakeSub)

			if addCount != tc.wantAdds {
				t.Errorf("Add calls = %d; want %d", addCount, tc.wantAdds)
			}
		})
	}
}
