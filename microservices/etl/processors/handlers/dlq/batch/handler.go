package dlqBqBatch

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc       func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	BatchGetter         func(ctx context.Context) (bqbatch.Batcher, error)
	SendToDLQFunc       func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector       ConnectorFunc
	ParseAttributes ParseAttributesFunc
	GetBatch        BatchGetter
	SendToDLQ       SendToDLQFunc
}

// dlqBqBatch specifically handles batch load failures
func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, sub connect.PsSubscription) {
	return func(ctx context.Context, sub connect.PsSubscription) {
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}

		// Get connections to access PubSub client
		connections, connErr := deps.Connector(ctx)
		if connErr != nil {
			logger.Errorf("Error getting connections: %v", connErr)
			return
		}

		err := sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received DLQ retry message on %s ID: %s", sub.ID(), msg.ID)

			// Check if this is a raw JSONL format message
			if format, exists := msg.Attributes["format"]; exists && format == "raw_jsonl" {
				// Handle raw JSONL format
				table, exists := msg.Attributes["table"]
				if !exists {
					logger.Error("Unknown table in raw DLQ message, sending to DLQ")
					if err := deps.SendToDLQ(ctx, connections.Pubsub, msg, "unknown table in raw DLQ message"); err != nil {
						logger.Errorf("Error sending message to DLQ: %v", err)
						msg.Nack()
						return
					}
					msg.Ack()
					return
				}

				if err := validateJSONL(msg.Data); err != nil {
					logger.Errorf("Invalid JSON in DLQ message for table %s: %v", table, err)
					logger.Errorf("Message data: %s", string(msg.Data))
					if err := deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("invalid JSON for table %s: %v", table, err)); err != nil {
						logger.Errorf("Error sending message to DLQ: %v", err)
						msg.Nack()
						return
					}
					msg.Ack()
					return
				}

				logger.Warnf("Retrying raw DLQ batch for table %s with %d bytes", table, len(msg.Data))
				errLb := batch.LoadBatch(table, msg.Data)
				if errLb != nil {
					logger.Errorf("Error loading raw DLQ batch: %v", errLb)
					msg.Ack()
					return
				}
			}
			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription %s: %v", sub.ID(), err)
		}
	}
}

func validateJSONL(message []byte) error {
	r := bytes.NewReader(message)
	scanner := bufio.NewScanner(r)

	// Set buffer size to handle up to 10MB messages (DLQ production limit)
	// Use 12MB buffer to provide some headroom
	const maxBufferSize = 12 * 1024 * 1024 // 12MB
	scanner.Buffer(make([]byte, 0, maxBufferSize), maxBufferSize)

	lineNum := 0

	for scanner.Scan() {
		lineNum++
		line := bytes.TrimSpace(scanner.Bytes())
		if len(line) == 0 {
			continue
		}

		var obj interface{}
		if err := json.Unmarshal(line, &obj); err != nil {
			return fmt.Errorf("invalid JSON on line %d: %w", lineNum, err)
		}
	}
	if err := scanner.Err(); err != nil {
		return fmt.Errorf("reading input: %w", err)
	}
	return nil
}

// Handler is the production-ready DLQ retry processor.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:       connect.GetConnections,
	ParseAttributes: pubsubdata.ParseAttributes,
	GetBatch:        bqbatch.GetBatch,
	SendToDLQ:       etlShared.SendToDLQ,
})
