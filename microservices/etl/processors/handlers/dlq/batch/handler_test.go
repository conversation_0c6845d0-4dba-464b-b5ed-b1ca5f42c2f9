package dlqBqBatch

import (
	"context"
	"errors"
	"strings"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/mocks/bqbatcher"
	"synapse-its.com/shared/pubsubdata"
)

func TestHandlerWithDeps(t *testing.T) {
	type testCase struct {
		name            string
		getBatchErr     error
		connectorErr    error
		receiveErr      error
		messages        []*pubsub.Message
		batcherLoadErr  error
		sendToDLQErr    error
		expectLoadCount int
		expectDLQCount  int
		expectAck       bool
		expectNack      bool
	}

	tests := []testCase{
		{
			name:        "GetBatchError",
			getBatchErr: errors.New("batch error"),
			// Hand<PERSON> should return early without processing messages
			expectLoadCount: 0,
			expectDLQCount:  0,
		},
		{
			name:         "ConnectorError",
			getBatchErr:  nil,
			connectorErr: errors.New("connector error"),
			// <PERSON><PERSON> should return early without processing messages
			expectLoadCount: 0,
			expectDLQCount:  0,
		},
		{
			name:            "ReceiveError",
			getBatchErr:     nil,
			receiveErr:      errors.New("receive error"),
			messages:        nil, // subscription.Receive returns error first
			expectLoadCount: 0,
			expectDLQCount:  0,
		},
		{
			name:        "UnknownTable_SendToDLQ",
			getBatchErr: nil,
			receiveErr:  nil,
			messages: []*pubsub.Message{
				{
					Data: []byte(`{"test": "data"}`),
					Attributes: map[string]string{
						"format": "raw_jsonl",
						// Missing "table" attribute
					},
					ID:          "msg1",
					PublishTime: time.Now(),
				},
			},
			expectLoadCount: 0,
			expectDLQCount:  1,
			expectAck:       true,
			expectNack:      false,
		},
		{
			name:        "UnknownTable_SendToDLQError_FallbackToNack",
			getBatchErr: nil,
			receiveErr:  nil,
			messages: []*pubsub.Message{
				{
					Data: []byte(`{"test": "data"}`),
					Attributes: map[string]string{
						"format": "raw_jsonl",
						// Missing "table" attribute
					},
					ID:          "msg1a",
					PublishTime: time.Now(),
				},
			},
			sendToDLQErr:    errors.New("DLQ error"),
			expectLoadCount: 0,
			expectDLQCount:  1,
			expectAck:       false,
			expectNack:      true,
		},
		{
			name:        "InvalidJSON_SendToDLQ",
			getBatchErr: nil,
			receiveErr:  nil,
			messages: []*pubsub.Message{
				{
					Data: []byte(`invalid json`),
					Attributes: map[string]string{
						"format": "raw_jsonl",
						"table":  "TestTable",
					},
					ID:          "msg2",
					PublishTime: time.Now(),
				},
			},
			expectLoadCount: 0,
			expectDLQCount:  1,
			expectAck:       true,
			expectNack:      false,
		},
		{
			name:        "InvalidJSON_SendToDLQError_FallbackToNack",
			getBatchErr: nil,
			receiveErr:  nil,
			messages: []*pubsub.Message{
				{
					Data: []byte(`invalid json`),
					Attributes: map[string]string{
						"format": "raw_jsonl",
						"table":  "TestTable",
					},
					ID:          "msg2a",
					PublishTime: time.Now(),
				},
			},
			sendToDLQErr:    errors.New("DLQ error"),
			expectLoadCount: 0,
			expectDLQCount:  1,
			expectAck:       false,
			expectNack:      true,
		},
		{
			name:        "ValidJSONL_LoadSuccess",
			getBatchErr: nil,
			receiveErr:  nil,
			messages: []*pubsub.Message{
				{
					Data: []byte(`{"test": "data1"}` + "\n" + `{"test": "data2"}`),
					Attributes: map[string]string{
						"format": "raw_jsonl",
						"table":  "TestTable",
					},
					ID:          "msg3",
					PublishTime: time.Now(),
				},
			},
			batcherLoadErr:  nil,
			expectLoadCount: 1,
			expectDLQCount:  0,
			expectAck:       true,
			expectNack:      false,
		},
		{
			name:        "ValidJSONL_LoadError_Nack",
			getBatchErr: nil,
			receiveErr:  nil,
			messages: []*pubsub.Message{
				{
					Data: []byte(`{"test": "data"}`),
					Attributes: map[string]string{
						"format": "raw_jsonl",
						"table":  "TestTable",
					},
					ID:          "msg4",
					PublishTime: time.Now(),
				},
			},
			batcherLoadErr:  errors.New("load error"),
			expectLoadCount: 1,
			expectDLQCount:  0,
			expectAck:       false,
			expectNack:      true,
		},
		{
			name:        "NonRawJSONL_Ignore",
			getBatchErr: nil,
			receiveErr:  nil,
			messages: []*pubsub.Message{
				{
					Data: []byte(`{"test": "data"}`),
					Attributes: map[string]string{
						// Missing "format": "raw_jsonl"
						"table": "TestTable",
					},
					ID:          "msg5",
					PublishTime: time.Now(),
				},
			},
			expectLoadCount: 0,
			expectDLQCount:  0,
			expectAck:       true,
			expectNack:      false,
		},
		{
			name:        "SendToDLQError_FallbackToNack",
			getBatchErr: nil,
			receiveErr:  nil,
			messages: []*pubsub.Message{
				{
					Data: []byte(`invalid json`),
					Attributes: map[string]string{
						"format": "raw_jsonl",
						"table":  "TestTable",
					},
					ID:          "msg6",
					PublishTime: time.Now(),
				},
			},
			sendToDLQErr:    errors.New("DLQ error"),
			expectLoadCount: 0,
			expectDLQCount:  1,
			expectAck:       false,
			expectNack:      true,
		},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			subName := "test-sub-" + tc.name

			// Prepare FakeBatcher
			var loadCount int
			fb := &bqbatcher.FakeBatcher{
				LoadBatchFn: func(table string, rawData []byte) error {
					loadCount++
					return tc.batcherLoadErr
				},
			}

			// Create a basic fake subscription for all test cases
			conns := mocks.FakeConns()
			client := conns.Pubsub.(*mocks.FakePubsubClient)
			client.ReceiveError = tc.receiveErr

			// Create a topic and subscription to avoid nil pointer dereference
			topic := client.Topic(subName)
			fakeSub, _ := client.CreateSubscription(ctx, subName, connect.SubscriptionConfig{Topic: topic})

			// Track SendToDLQ calls
			var dlqCount int
			sendToDLQ := func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error {
				dlqCount++
				return tc.sendToDLQErr
			}

			// Publish test messages to the topic if any
			if len(tc.messages) > 0 {
				for _, m := range tc.messages {
					topic.Publish(ctx, m)
				}
			}

			// Build deps
			deps := HandlerDeps{
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					if tc.getBatchErr != nil {
						return nil, tc.getBatchErr
					}
					return fb, nil
				},
				Connector: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					if tc.connectorErr != nil {
						return nil, tc.connectorErr
					}
					return conns, nil
				},
				ParseAttributes: pubsubdata.ParseAttributes,
				SendToDLQ:       sendToDLQ,
			}

			// Always call the handler to test the GetBatch error path
			handler := HandlerWithDeps(deps)
			handler(ctx, fakeSub)

			// Check counts
			assert.Equal(t, tc.expectLoadCount, loadCount, "unexpected LoadBatch call count")
			assert.Equal(t, tc.expectDLQCount, dlqCount, "unexpected SendToDLQ call count")

			// Note: We can't easily track ack/nack in this test setup,
			// but the handler logic is tested through the LoadBatch and SendToDLQ calls
		})
	}
}

func TestValidateJSONL(t *testing.T) {
	tests := []struct {
		name    string
		data    []byte
		wantErr bool
	}{
		{
			name:    "ValidJSONL",
			data:    []byte(`{"test": "data1"}` + "\n" + `{"test": "data2"}`),
			wantErr: false,
		},
		{
			name:    "ValidSingleJSON",
			data:    []byte(`{"test": "data"}`),
			wantErr: false,
		},
		{
			name:    "EmptyLines",
			data:    []byte(`{"test": "data1"}` + "\n\n" + `{"test": "data2"}`),
			wantErr: false,
		},
		{
			name:    "InvalidJSON",
			data:    []byte(`invalid json`),
			wantErr: true,
		},
		{
			name:    "InvalidJSONInLine",
			data:    []byte(`{"test": "data1"}` + "\n" + `invalid json`),
			wantErr: true,
		},
		{
			name:    "EmptyData",
			data:    []byte{},
			wantErr: false,
		},
		{
			name:    "OnlyWhitespace",
			data:    []byte("\n\n  \n"),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateJSONL(tt.data)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestValidateJSONLScannerError tests the scanner.Err() path specifically
func TestValidateJSONLScannerError(t *testing.T) {
	// Create a custom reader that will cause scanner.Err() to return an error
	// We'll use a very large buffer that exceeds the scanner's buffer size
	// This will cause the scanner to fail when it tries to read a line that's too long

	// Create a very long line that will exceed the scanner's buffer (12MB)
	longLine := strings.Repeat("a", 15*1024*1024) // 15MB line (exceeds 12MB buffer)
	data := []byte(longLine)

	err := validateJSONL(data)
	assert.Error(t, err, "Should get scanner error for very long line")
	assert.Contains(t, err.Error(), "reading input", "Error should mention reading input")
}

func TestHandlerProduction(t *testing.T) {
	// Test that the production handler is properly configured
	handler := Handler
	require.NotNil(t, handler, "Production handler should not be nil")

	// The handler should be a function that takes context and subscription
	// We can't easily test the actual behavior without real connections,
	// but we can verify it's properly configured
	assert.NotNil(t, handler, "Handler should be a function")
}
