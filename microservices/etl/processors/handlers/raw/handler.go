package raw

import (
	"context"
	"fmt"

	"cloud.google.com/go/pubsub"

	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc       func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	DLQSender           func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
	BatchGetter         func(ctx context.Context) (bqbatch.Batcher, error)
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector       ConnectorFunc
	ParseAttributes ParseAttributesFunc
	SendToDLQ       DLQSender
	GetBatch        BatchGetter
}

// Inserts all of the raw pubsub messages into bigquery
func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, sub connect.PsSubscription) {
	return func(ctx context.Context, sub connect.PsSubscription) {
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}
		connections, err := deps.Connector(ctx)
		if err != nil {
			logger.Errorf("Error getting connections%v", err)
			return
		}

		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s MessageID: %s. Message Data:%s", sub.ID(), string(msg.ID), string(msg.Data))

			// Parse Attributes
			commonAttrs, httpHeader, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			dAttempt := -1
			if msg.DeliveryAttempt != nil {
				dAttempt = *msg.DeliveryAttempt
			}

			// Insert the row into the BigQuery table.
			if err = batch.Add(schemas.EdiRawMessages{
				Topic:                     commonAttrs.Topic,
				OrganizationIdentifier:    commonAttrs.OrganizationIdentifier,
				SoftwareGatewayIdentifier: httpHeader.GatewayDeviceID,
				MessageType:               httpHeader.MessageType,
				MessageVersion:            httpHeader.MessageVersion,
				PubsubID:                  msg.ID,
				Data:                      msg.Data,
				Attributes:                etlShared.AttributesMapToBq(msg.Attributes),
				PubsubTimestamp:           msg.PublishTime,
				OrderingKey:               msg.OrderingKey,
				DeliveryAttempt:           int64(dAttempt),
			}); err != nil {
				logger.Errorf("Error adding message to batch: %v", err)
				msg.Nack()
				return
			}

			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription %s: %v", sub.ID(), err)
		}
	}
}

// Handler is the production-ready Pub/Sub processor using real dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:       connect.GetConnections,
	ParseAttributes: pubsubdata.ParseAttributes,
	SendToDLQ:       etlShared.SendToDLQ,
	GetBatch:        bqbatch.GetBatch,
})
