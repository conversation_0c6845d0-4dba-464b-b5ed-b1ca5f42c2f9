package mailgun

import (
	"context"
	"os"

	"synapse-its.com/shared/logger"
)

// MailgunClient defines the interface for Mailgun API operations
type MailgunClient interface {
	SendEmail(ctx context.Context, to, subject, body string) error
}

// Service implements the EmailService interface using Mailgun
type Service struct {
	client MailgunClient // Injected Mailgun client (optional)
}

// NewService creates a new Mailgun notification service
func NewService() *Service {
	return &Service{}
}

// NewServiceWithClient creates a new Mailgun notification service with injected client
func NewServiceWithClient(client MailgunClient) *Service {
	return &Service{
		client: client,
	}
}

// SendEmail sends an email using Mailgun
func (s *Service) SendEmail(ctx context.Context, to, subject, body string) error {
	// Get Mailgun client (either injected or from context)
	client, err := s.getMailgunClient(ctx)
	if err != nil {
		logger.Errorf("Failed to get Mailgun client: %v", err)
		return err
	}

	// Read MG_SENDER from environment
	sender := os.Getenv("MG_SENDER")
	if sender == "" {
		logger.Error(ErrMissingSender)
		return ErrMissingSender
	}

	// Validate email addresses for debugging
	logger.Debugf("Sending email from %s to %s", sender, to)

	// Send email via Mailgun API
	err = client.SendEmail(ctx, to, subject, body)
	if err != nil {
		logger.Errorf("Mailgun API error: %v", err)
		return err
	}

	logger.Debugf("Email sent successfully to %s", to)
	return nil
}

// getMailgunClient returns the injected client or gets it from context
func (s *Service) getMailgunClient(ctx context.Context) (MailgunClient, error) {
	// Use injected client if available
	if s.client != nil {
		return s.client, nil
	}

	// Fallback to context-based client for backward compatibility
	contextClient := FromContext(ctx)
	if contextClient == nil {
		return nil, ErrClientNotFound
	}

	// Wrap the context client to implement our interface
	return &mailgunClientAdapter{client: contextClient}, nil
}

// mailgunClientAdapter adapts the context-based Mailgun client to our interface
type mailgunClientAdapter struct {
	client interface{}
}

func (a *mailgunClientAdapter) SendEmail(ctx context.Context, to, subject, body string) error {
	// For backward compatibility with context-based client
	// Cast the client to the actual Mailgun client
	if mgClient, ok := a.client.(*Client); ok {
		return mgClient.SendEmail(ctx, to, subject, body)
	}
	return ErrSendEmail
}
