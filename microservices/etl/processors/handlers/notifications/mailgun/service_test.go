package mailgun

import (
	"context"
	"os"
	"testing"
)

// MockMailgunClientForService implements MailgunClient for testing
type MockMailgunClientForService struct {
	sendEmailFunc func(ctx context.Context, to, subject, body string) error
}

func (m *MockMailgunClientForService) SendEmail(ctx context.Context, to, subject, body string) error {
	if m.sendEmailFunc != nil {
		return m.sendEmailFunc(ctx, to, subject, body)
	}
	return nil
}

func TestNewService(t *testing.T) {
	service := NewService()
	if service == nil {
		t.Fatal("NewService() returned nil")
	}
	if service.client != nil {
		t.Error("NewService() should not have a client by default")
	}
}

func TestNewServiceWithClient(t *testing.T) {
	mockClient := &MockMailgunClientForService{}
	service := NewServiceWithClient(mockClient)
	if service == nil {
		t.Fatal("NewServiceWithClient() returned nil")
	}
	if service.client != mockClient {
		t.Error("NewServiceWithClient() should have the injected client")
	}
}

func TestService_SendEmail_Success(t *testing.T) {
	// Set up environment variables
	originalSender := os.Getenv("MG_SENDER")
	defer os.Setenv("MG_SENDER", originalSender)
	os.Setenv("MG_SENDER", "<EMAIL>")

	// Create mock client
	mockClient := &MockMailgunClientForService{
		sendEmailFunc: func(ctx context.Context, to, subject, body string) error {
			return nil
		},
	}

	// Create service with mock client
	service := NewServiceWithClient(mockClient)

	// Test sending email
	ctx := context.Background()
	err := service.SendEmail(ctx, "<EMAIL>", "Test Subject", "Test Body")
	if err != nil {
		t.Errorf("SendEmail() returned error: %v", err)
	}
}

func TestService_SendEmail_MissingSender(t *testing.T) {
	// Clear MG_SENDER environment variable
	originalSender := os.Getenv("MG_SENDER")
	defer os.Setenv("MG_SENDER", originalSender)
	os.Unsetenv("MG_SENDER")

	// Create mock client
	mockClient := &MockMailgunClientForService{}
	service := NewServiceWithClient(mockClient)

	// Test sending email
	ctx := context.Background()
	err := service.SendEmail(ctx, "<EMAIL>", "Test Subject", "Test Body")

	if err != ErrMissingSender {
		t.Errorf("Expected ErrMissingSender, got: %v", err)
	}
}

func TestService_SendEmail_ClientError(t *testing.T) {
	// Set up environment variables
	originalSender := os.Getenv("MG_SENDER")
	defer os.Setenv("MG_SENDER", originalSender)
	os.Setenv("MG_SENDER", "<EMAIL>")

	// Create mock client that returns error
	mockClient := &MockMailgunClientForService{
		sendEmailFunc: func(ctx context.Context, to, subject, body string) error {
			return ErrSendEmail
		},
	}

	// Create service with mock client
	service := NewServiceWithClient(mockClient)

	// Test sending email
	ctx := context.Background()
	err := service.SendEmail(ctx, "<EMAIL>", "Test Subject", "Test Body")

	if err != ErrSendEmail {
		t.Errorf("Expected ErrSendEmail, got: %v", err)
	}
}

func TestService_SendEmail_GetClientError(t *testing.T) {
	// Set up environment variables
	originalSender := os.Getenv("MG_SENDER")
	defer os.Setenv("MG_SENDER", originalSender)
	os.Setenv("MG_SENDER", "<EMAIL>")

	// Create service without injected client and without context client
	service := NewService()

	// Test sending email without any client available
	ctx := context.Background()
	err := service.SendEmail(ctx, "<EMAIL>", "Test Subject", "Test Body")

	if err != ErrClientNotFound {
		t.Errorf("Expected ErrClientNotFound, got: %v", err)
	}
}

func TestService_getMailgunClient_WithInjectedClient(t *testing.T) {
	mockClient := &MockMailgunClientForService{}
	service := NewServiceWithClient(mockClient)

	ctx := context.Background()
	client, err := service.getMailgunClient(ctx)
	if err != nil {
		t.Errorf("getMailgunClient() returned error: %v", err)
	}
	if client != mockClient {
		t.Error("getMailgunClient() should return the injected client")
	}
}

func TestService_getMailgunClient_WithContextClient(t *testing.T) {
	// Create service without injected client
	service := NewService()

	// Create context with client
	mockClient := &Client{}
	ctx := WithClient(context.Background(), mockClient)

	client, err := service.getMailgunClient(ctx)
	if err != nil {
		t.Errorf("getMailgunClient() returned error: %v", err)
	}
	if client == nil {
		t.Error("getMailgunClient() should return the context client")
	}
}

func TestService_getMailgunClient_NoClient(t *testing.T) {
	// Create service without injected client
	service := NewService()

	// Create context without client
	ctx := context.Background()

	client, err := service.getMailgunClient(ctx)

	if err != ErrClientNotFound {
		t.Errorf("Expected ErrClientNotFound, got: %v", err)
	}
	if client != nil {
		t.Error("getMailgunClient() should return nil when no client is available")
	}
}

func TestMailgunClientAdapter_SendEmail(t *testing.T) {
	// Create a mock client with a proper sender
	mockSender := &MockMailgunClient{}
	mockClient := &Client{
		sender:      mockSender,
		domain:      "test.example.com",
		senderEmail: "<EMAIL>",
	}

	// Create adapter
	adapter := &mailgunClientAdapter{client: mockClient}

	// Test SendEmail method
	ctx := context.Background()
	err := adapter.SendEmail(ctx, "<EMAIL>", "Test", "Body")
	// Should succeed since the client is properly initialized
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
}

func TestMailgunClientAdapter_SendEmail_WrongClientType(t *testing.T) {
	// Create adapter with wrong client type
	adapter := &mailgunClientAdapter{client: "not a client"}

	// Test SendEmail method
	ctx := context.Background()
	err := adapter.SendEmail(ctx, "<EMAIL>", "Test", "Body")

	if err != ErrSendEmail {
		t.Errorf("Expected ErrSendEmail, got: %v", err)
	}
}
