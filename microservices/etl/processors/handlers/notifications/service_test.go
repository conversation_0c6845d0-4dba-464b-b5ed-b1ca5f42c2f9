package notifications

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	mocknotif "synapse-its.com/shared/mocks/notifications"
)

func TestNewCompositeNotificationService(t *testing.T) {
	mockSMSService := &mocknotif.MockSMSService{}
	mockEmailService := &mocknotif.MockEmailService{}

	service := NewCompositeNotificationService(mockSMSService, mockEmailService)

	assert.NotNil(t, service)
	assert.Equal(t, mockSMSService, service.smsService)
	assert.Equal(t, mockEmailService, service.emailService)
}

func TestCompositeNotificationService_SendSMS_Success(t *testing.T) {
	mockSMSService := &mocknotif.MockSMSService{}
	mockEmailService := &mocknotif.MockEmailService{}

	service := NewCompositeNotificationService(mockSMSService, mockEmailService)

	// Setup mock expectations
	mockSMSService.On("SendSMS", mock.Anything, "+1234567890", "Test message").Return(nil)

	// Test SendSMS
	err := service.SendSMS(context.Background(), "+1234567890", "Test message")

	assert.NoError(t, err)
	mockSMSService.AssertExpectations(t)
}

func TestCompositeNotificationService_SendSMS_Error(t *testing.T) {
	mockSMSService := &mocknotif.MockSMSService{}
	mockEmailService := &mocknotif.MockEmailService{}

	service := NewCompositeNotificationService(mockSMSService, mockEmailService)

	// Setup mock expectations
	expectedErr := errors.New("SMS service error")
	mockSMSService.On("SendSMS", mock.Anything, "+1234567890", "Test message").Return(expectedErr)

	// Test SendSMS
	err := service.SendSMS(context.Background(), "+1234567890", "Test message")

	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	mockSMSService.AssertExpectations(t)
}

func TestCompositeNotificationService_SendSMS_NoService(t *testing.T) {
	mockEmailService := &mocknotif.MockEmailService{}

	service := NewCompositeNotificationService(nil, mockEmailService)

	// Test SendSMS without SMS service
	err := service.SendSMS(context.Background(), "+1234567890", "Test message")

	assert.Error(t, err)
	assert.Equal(t, ErrSMSNotSupported, err)
}

func TestCompositeNotificationService_SendEmail_Success(t *testing.T) {
	mockSMSService := &mocknotif.MockSMSService{}
	mockEmailService := &mocknotif.MockEmailService{}

	service := NewCompositeNotificationService(mockSMSService, mockEmailService)

	// Setup mock expectations
	mockEmailService.On("SendEmail", mock.Anything, "<EMAIL>", "Test Subject", "Test Body").Return(nil)

	// Test SendEmail
	err := service.SendEmail(context.Background(), "<EMAIL>", "Test Subject", "Test Body")

	assert.NoError(t, err)
	mockEmailService.AssertExpectations(t)
}

func TestCompositeNotificationService_SendEmail_Error(t *testing.T) {
	mockSMSService := &mocknotif.MockSMSService{}
	mockEmailService := &mocknotif.MockEmailService{}

	service := NewCompositeNotificationService(mockSMSService, mockEmailService)

	// Setup mock expectations
	expectedErr := errors.New("Email service error")
	mockEmailService.On("SendEmail", mock.Anything, "<EMAIL>", "Test Subject", "Test Body").Return(expectedErr)

	// Test SendEmail
	err := service.SendEmail(context.Background(), "<EMAIL>", "Test Subject", "Test Body")

	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	mockEmailService.AssertExpectations(t)
}

func TestCompositeNotificationService_SendEmail_NoService(t *testing.T) {
	mockSMSService := &mocknotif.MockSMSService{}

	service := NewCompositeNotificationService(mockSMSService, nil)

	// Test SendEmail without email service
	err := service.SendEmail(context.Background(), "<EMAIL>", "Test Subject", "Test Body")

	assert.Error(t, err)
	assert.Equal(t, ErrEmailNotSupported, err)
}
