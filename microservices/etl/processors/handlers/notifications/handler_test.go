package notifications

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	mocknotif "synapse-its.com/shared/mocks/notifications"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// createTestMessage creates a test notification message
func createTestMessage(t *testing.T, notifType string, payload map[string]interface{}, metadata map[string]interface{}) *pubsub.Message {
	notif := pubsubdata.NotificationRequest{
		Type:     notifType,
		Payload:  payload,
		Metadata: metadata,
	}

	data, err := json.Marshal(notif)
	if err != nil {
		t.Fatal(err)
	}

	return &pubsub.Message{
		ID:   "test-msg-id",
		Data: data,
		Attributes: map[string]string{
			"organizationIdentifier": "test-org",
			"topic":                  "test-topic",
		},
		PublishTime: time.Now(),
	}
}

func TestHandlerWithDeps(t *testing.T) {
	tests := []struct {
		name               string
		connErr            error
		recvErr            error
		attrErr            error
		batchErr           error
		batchAddErr        error
		dlqErr             error
		smsErr             error
		emailErr           error
		jsonErr            error
		payloadMarshalErr  error
		metadataMarshalErr error
		notifType          string
		payload            map[string]interface{}
		metadata           map[string]interface{}
		wantDLQ            int
		wantBatchAdds      int
		wantSMSSent        bool
		wantEmailSent      bool
	}{
		{
			name:          "get batch error",
			batchErr:      errors.New("no batch"),
			notifType:     "sms",
			wantDLQ:       0,
			wantBatchAdds: 0,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "connector error",
			connErr:       errors.New("no conn"),
			notifType:     "sms",
			wantDLQ:       0,
			wantBatchAdds: 0,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "receive error",
			recvErr:       errors.New("recv fail"),
			notifType:     "sms",
			wantDLQ:       0,
			wantBatchAdds: 0,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "parse attributes error",
			attrErr:       errors.New("fail attr parse"),
			notifType:     "sms",
			wantDLQ:       1,
			wantBatchAdds: 0,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "parse attributes error and fail to send to DLQ",
			attrErr:       errors.New("fail attr parse"),
			dlqErr:        errors.New("dlq failed"),
			notifType:     "sms",
			wantDLQ:       0,
			wantBatchAdds: 0,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "invalid JSON message",
			notifType:     "sms",
			payload:       nil,
			wantDLQ:       1,
			wantBatchAdds: 0,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "JSON unmarshal error",
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890", "message": "test"},
			jsonErr:       errors.New("json unmarshal failed"),
			wantDLQ:       1,
			wantBatchAdds: 0,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "JSON unmarshal error and fail to send to DLQ",
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890", "message": "test"},
			jsonErr:       errors.New("json unmarshal failed"),
			dlqErr:        errors.New("dlq failed"),
			wantDLQ:       0,
			wantBatchAdds: 0,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:              "payload marshal error",
			notifType:         "sms",
			payload:           map[string]interface{}{"to": "1234567890", "message": "test"},
			payloadMarshalErr: errors.New("payload marshal failed"),
			wantDLQ:           1,
			wantBatchAdds:     0,
			wantSMSSent:       false,
			wantEmailSent:     false,
		},
		{
			name:              "payload marshal error and fail to send to DLQ",
			notifType:         "sms",
			payload:           map[string]interface{}{"to": "1234567890", "message": "test"},
			payloadMarshalErr: errors.New("payload marshal failed"),
			dlqErr:            errors.New("dlq failed"),
			wantDLQ:           0,
			wantBatchAdds:     0,
			wantSMSSent:       false,
			wantEmailSent:     false,
		},
		{
			name:               "metadata marshal error",
			notifType:          "sms",
			payload:            map[string]interface{}{"to": "1234567890", "message": "test"},
			metadata:           map[string]interface{}{"source": "test"},
			metadataMarshalErr: errors.New("metadata marshal failed"),
			wantDLQ:            1,
			wantBatchAdds:      0,
			wantSMSSent:        false,
			wantEmailSent:      false,
		},
		{
			name:               "metadata marshal error and fail to send to DLQ",
			notifType:          "sms",
			payload:            map[string]interface{}{"to": "1234567890", "message": "test"},
			metadata:           map[string]interface{}{"source": "test"},
			metadataMarshalErr: errors.New("metadata marshal failed"),
			dlqErr:             errors.New("dlq failed"),
			wantDLQ:            0,
			wantBatchAdds:      0,
			wantSMSSent:        false,
			wantEmailSent:      false,
		},
		{
			name:          "batch add error",
			batchAddErr:   errors.New("batch add failed"),
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890", "message": "test"},
			wantDLQ:       0,
			wantBatchAdds: 0,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "unsupported notification type",
			notifType:     "push",
			payload:       map[string]interface{}{"to": "<EMAIL>", "message": "test"},
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "missing to field in SMS",
			notifType:     "sms",
			payload:       map[string]interface{}{"message": "test"},
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "missing message field in SMS",
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890"},
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "SMS send error - transient",
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890", "message": "test"},
			smsErr:        errors.New("connection timeout"),
			wantDLQ:       1,
			wantBatchAdds: 1,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "SMS send error - permanent",
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890", "message": "test"},
			smsErr:        errors.New("invalid phone number"),
			wantDLQ:       1,
			wantBatchAdds: 1,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "SMS send error - permanent and DLQ fails",
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890", "message": "test"},
			smsErr:        errors.New("invalid phone number"),
			dlqErr:        errors.New("dlq failed"),
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "happy path - SMS sent successfully",
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890", "message": "test"},
			metadata:      map[string]interface{}{"source": "test"},
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   true,
			wantEmailSent: false,
		},
		// Email test cases
		{
			name:          "missing to field in email",
			notifType:     "email",
			payload:       map[string]interface{}{"subject": "test subject", "body": "test body"},
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "missing subject field in email",
			notifType:     "email",
			payload:       map[string]interface{}{"to": "<EMAIL>", "body": "test body"},
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "missing body field in email",
			notifType:     "email",
			payload:       map[string]interface{}{"to": "<EMAIL>", "subject": "test subject"},
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "email send error - transient",
			notifType:     "email",
			payload:       map[string]interface{}{"to": "<EMAIL>", "subject": "test subject", "body": "test body"},
			emailErr:      errors.New("connection timeout"),
			wantDLQ:       1,
			wantBatchAdds: 1,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "email send error - permanent",
			notifType:     "email",
			payload:       map[string]interface{}{"to": "<EMAIL>", "subject": "test subject", "body": "test body"},
			emailErr:      errors.New("invalid email address"),
			wantDLQ:       1,
			wantBatchAdds: 1,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "email send error - permanent and DLQ fails",
			notifType:     "email",
			payload:       map[string]interface{}{"to": "<EMAIL>", "subject": "test subject", "body": "test body"},
			emailErr:      errors.New("invalid email address"),
			dlqErr:        errors.New("dlq failed"),
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   false,
			wantEmailSent: false,
		},
		{
			name:          "happy path - email sent successfully",
			notifType:     "email",
			payload:       map[string]interface{}{"to": "<EMAIL>", "subject": "test subject", "body": "test body"},
			metadata:      map[string]interface{}{"source": "test"},
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   false,
			wantEmailSent: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()

			// Setup mocks
			conns := mocks.FakeConns()
			psc := conns.Pubsub.(*mocks.FakePubsubClient)
			psc.ReceiveError = tc.recvErr

			// Create subscription
			topic := psc.Topic("topic-" + tc.name)
			sub, err := psc.CreateSubscription(ctx, "sub-"+tc.name, connect.SubscriptionConfig{Topic: topic})
			if err != nil {
				t.Fatal(err)
			}

			// Publish message if connector & receive ok
			if tc.connErr == nil && tc.recvErr == nil {
				var msg *pubsub.Message
				if tc.name == "invalid JSON message" {
					// Create invalid JSON message
					msg = &pubsub.Message{
						ID:   "test-msg-id" + tc.name,
						Data: []byte(`{"type": "sms", "payload": {invalid json}`),
						Attributes: map[string]string{
							"organizationIdentifier": "test-org",
							"topic":                  "test-topic",
						},
						PublishTime: time.Now(),
					}
				} else {
					msg = createTestMessage(t, tc.notifType, tc.payload, tc.metadata)
				}
				topic.Publish(ctx, msg)
			}

			// Setup fake batcher
			added := 0
			fakeBatch, _ := mocks.FakeBatch(ctx)
			fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				if tc.batchAddErr != nil {
					return tc.batchAddErr
				}
				// Verify the row is of correct type
				_, ok := row.(schemas.NotificationMessages)
				assert.True(t, ok, "Row should be of type schemas.NotificationMessages")
				added++
				return nil
			}

			// Setup mock notification service
			mockService := &mocknotif.MockNotificationService{}
			if tc.payload != nil && tc.payload["to"] != nil && tc.payload["message"] != nil {
				mockService.On("SendSMS", mock.Anything, tc.payload["to"].(string), tc.payload["message"].(string)).Return(tc.smsErr)
			}
			if tc.payload != nil && tc.payload["to"] != nil && tc.payload["subject"] != nil && tc.payload["body"] != nil {
				mockService.On("SendEmail", mock.Anything, tc.payload["to"].(string), tc.payload["subject"].(string), tc.payload["body"].(string)).Return(tc.emailErr)
			}

			// Count DLQ calls
			dlq := 0

			// Build deps
			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conns, tc.connErr
				},
				SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, reason string) error {
					if tc.dlqErr != nil {
						return tc.dlqErr
					}
					dlq++
					return nil
				},
				jsonUnmarshal: func(data []byte, v any) error {
					if tc.jsonErr != nil {
						return tc.jsonErr
					}
					return json.Unmarshal(data, v)
				},
				jsonMarshal: func(v interface{}) ([]byte, error) {
					// Check if we should simulate marshaling errors
					if tc.payloadMarshalErr != nil {
						// Look at the payload content to identify if this is the payload marshal call
						if payloadMap, ok := v.(map[string]interface{}); ok {
							if tc.payload != nil && len(payloadMap) == len(tc.payload) {
								// This looks like our payload
								return nil, tc.payloadMarshalErr
							}
						}
					}
					if tc.metadataMarshalErr != nil {
						// Look at the metadata content to identify if this is the metadata marshal call
						if metadataMap, ok := v.(map[string]interface{}); ok {
							if tc.metadata != nil && len(metadataMap) == len(tc.metadata) {
								// This looks like our metadata
								return nil, tc.metadataMarshalErr
							}
						}
					}
					return json.Marshal(v)
				},
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return fakeBatch, tc.batchErr
				},
				ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{
						OrganizationIdentifier: "test-org",
						Topic:                  "test-topic",
					}, pubsubdata.HeaderDetails{}, nil
				},
				NewService: func() NotificationService {
					return mockService
				},
			}

			// Run handler
			handler := HandlerWithDeps(deps)
			handler(ctx, sub)

			// Verify expectations
			assert.Equal(t, tc.wantDLQ, dlq, "DLQ count mismatch")
			assert.Equal(t, tc.wantBatchAdds, added, "Batch adds count mismatch")

			if tc.wantSMSSent && tc.payload != nil && tc.payload["to"] != nil && tc.payload["message"] != nil {
				mockService.AssertCalled(t, "SendSMS", mock.Anything, tc.payload["to"].(string), tc.payload["message"].(string))
			}
			if tc.wantEmailSent && tc.payload != nil && tc.payload["to"] != nil && tc.payload["subject"] != nil && tc.payload["body"] != nil {
				mockService.AssertCalled(t, "SendEmail", mock.Anything, tc.payload["to"].(string), tc.payload["subject"].(string), tc.payload["body"].(string))
			}
		})
	}
}

// TestDeduplicationScenarios tests various deduplication-related scenarios
func TestDeduplicationScenarios(t *testing.T) {
	tests := []struct {
		name              string
		messageCount      int
		useSameMessageID  bool
		closeRedis        bool
		expectedBatchAdds int
		expectedSMSSent   int
		expectedDLQCalls  int
		expectedDLQReason string
	}{
		{
			name:              "message reprocessing - duplicate messages with same ID are deduplicated",
			messageCount:      2,
			useSameMessageID:  true,
			closeRedis:        false,
			expectedBatchAdds: 1,
			expectedSMSSent:   1,
			expectedDLQCalls:  0,
			expectedDLQReason: "",
		},
		{
			name:              "redis failure - messages do not go to DLQ when Redis fails",
			messageCount:      1,
			useSameMessageID:  false,
			closeRedis:        true,
			expectedBatchAdds: 0,
			expectedSMSSent:   0,
			expectedDLQCalls:  0,
			expectedDLQReason: "",
		},
		{
			name:              "concurrent processing - multiple different messages are all processed",
			messageCount:      5,
			useSameMessageID:  false,
			closeRedis:        false,
			expectedBatchAdds: 5,
			expectedSMSSent:   5,
			expectedDLQCalls:  0,
			expectedDLQReason: "",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()

			// Setup mocks
			conns := mocks.FakeConns()

			// Close Redis if requested for this test case
			if tc.closeRedis {
				conns.Redis.Close()
			}

			// Create subscription with unique name for this test
			psc := conns.Pubsub.(*mocks.FakePubsubClient)
			// Use a cleaner name for test resources
			testID := fmt.Sprintf("test-%d", time.Now().UnixNano())
			topicName := "topic-" + testID
			subName := "sub-" + testID
			topic := psc.Topic(topicName)
			sub, err := psc.CreateSubscription(ctx, subName, connect.SubscriptionConfig{Topic: topic})
			if err != nil {
				t.Fatal(err)
			}

			// Create and publish messages based on test configuration
			baseID := "msg-" + testID
			for i := 0; i < tc.messageCount; i++ {
				msg := createTestMessage(t, "sms", map[string]interface{}{
					"to":      fmt.Sprintf("123456789%d", i),
					"message": fmt.Sprintf("test message %d", i),
				}, nil)

				// Use same ID for all messages if requested, otherwise unique IDs
				if tc.useSameMessageID {
					msg.ID = baseID
				} else {
					msg.ID = fmt.Sprintf("%s-%d", baseID, i)
				}

				topic.Publish(ctx, msg)
			}

			// Setup fake batcher
			batchAddCount := 0
			fakeBatch, _ := mocks.FakeBatch(ctx)
			fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				batchAddCount++
				return nil
			}

			// Setup mock notification service
			mockService := &mocknotif.MockNotificationService{}
			smsCallCount := 0

			// Only set up SMS mock expectation if we expect SMS to be sent
			if tc.expectedSMSSent > 0 {
				mockService.On("SendSMS", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return(nil).Run(func(args mock.Arguments) {
					smsCallCount++
				}).Times(tc.expectedSMSSent)
			}

			// Count DLQ calls and capture reason
			dlqCallCount := 0
			var dlqReason string

			// Build deps
			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conns, nil
				},
				ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					return pubsubdata.CommonAttributes{
						OrganizationIdentifier: "test-org",
						Topic:                  "test-topic",
					}, pubsubdata.HeaderDetails{}, nil
				},
				SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, reason string) error {
					dlqCallCount++
					dlqReason = reason
					return nil
				},
				jsonUnmarshal: json.Unmarshal,
				jsonMarshal:   json.Marshal,
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return fakeBatch, nil
				},
				NewService: func() NotificationService {
					return mockService
				},
			}

			// Run the handler
			HandlerWithDeps(deps)(ctx, sub)

			// Give some time for processing
			time.Sleep(200 * time.Millisecond)

			// Verify expectations
			assert.Equal(t, tc.expectedBatchAdds, batchAddCount, "Batch add count mismatch")
			assert.Equal(t, tc.expectedSMSSent, smsCallCount, "SMS sent count mismatch")
			assert.Equal(t, tc.expectedDLQCalls, dlqCallCount, "DLQ call count mismatch")

			if tc.expectedDLQReason != "" {
				assert.Contains(t, dlqReason, tc.expectedDLQReason, "DLQ reason should contain expected text")
			}

			// Verify mock expectations only if we set up expectations
			if tc.expectedSMSSent > 0 {
				mockService.AssertExpectations(t)
			}
		})
	}
}

func Test_notificationService(t *testing.T) {
	tests := []struct {
		name     string
		expected bool
	}{
		{
			name:     "returns valid NotificationService",
			expected: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Call the notificationService function
			service := notificationService()

			// Verify service is not nil
			assert.NotNil(t, service, "notificationService should return a non-nil service")

			// Verify it implements NotificationService interface
			var _ NotificationService = service

			// Verify it's the expected type (Twilio service)
			// We can't directly check the type since it might be an interface,
			// but we can verify it has the expected behavior
			ctx := context.Background()

			// Test that SendSMS method exists and can be called
			// (even if it fails due to missing credentials, the method should exist)
			err := service.SendSMS(ctx, "test", "test")
			// We don't check the error since we don't have real credentials
			// We just verify the method can be called without panic
			_ = err
		})
	}
}

func Test_Handler(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "Handler variable is initialized",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Verify Handler is not nil
			assert.NotNil(t, Handler, "Handler should be initialized")

			// Verify Handler is a function
			assert.IsType(t, func(context.Context, connect.PsSubscription) {}, Handler, "Handler should be a function")
		})
	}
}
