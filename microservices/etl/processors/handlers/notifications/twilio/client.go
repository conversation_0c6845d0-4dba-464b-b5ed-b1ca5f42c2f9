package twilio

import (
	"context"
	"io"
	"net/http"
	"os"
	"strings"

	twilio "github.com/twilio/twilio-go"
	client "github.com/twilio/twilio-go/client"
	"synapse-its.com/shared/logger"
)

// Store the Twilio client in context
const twilioClientKey string = "twilio_client"

var osGetenv = os.Getenv

// NewClient reads TWILIO_ACCOUNT_SID, TWILIO_API_KEY and TWILIO_API_SECRET from the environment
// and returns an initialized *twilio.RestClient, or an error if credentials are missing
func NewClient(ctx context.Context) (*twilio.RestClient, error) {
	// Get accountSid from env
	accountSid := osGetenv("TWILIO_ACCOUNT_SID")
	if accountSid == "" {
		return nil, ErrMissingAccountSid
	}

	// Get apiKey from env
	apiKey := osGetenv("TWILIO_API_KEY")
	if apiKey == "" {
		return nil, ErrMissingAPIKey
	}

	// Get apiSecret from env
	apiSecret := osGetenv("TWILIO_API_SECRET")
	if apiSecret == "" {
		return nil, ErrMissingAPISecret
	}

	// Create Twilio client parameters
	params := twilio.ClientParams{
		Username:   apiKey,
		Password:   apiSecret,
		AccountSid: accountSid,
	}

	// Only add custom HTTP client if getCustomHTTPClient returns a value
	if customHTTPClient := getCustomHTTPClient(); customHTTPClient != nil {
		params.Client = &client.Client{
			Credentials: client.NewCredentials(apiKey, apiSecret),
			HTTPClient:  customHTTPClient,
		}
	}

	// Create Twilio client with API key and secret and accountSid
	client := twilio.NewRestClientWithParams(params)

	return client, nil
}

// WithClient stores the *twilio.RestClient in a context under a private key
func WithClient(ctx context.Context, client *twilio.RestClient) context.Context {
	return context.WithValue(ctx, twilioClientKey, client)
}

// FromContext retrieves the *twilio.RestClient from the context
func FromContext(ctx context.Context) *twilio.RestClient {
	if client, ok := ctx.Value(twilioClientKey).(*twilio.RestClient); ok {
		return client
	}
	return nil
}

// getCustomHTTPClient returns an *http.Client configured based on environment overrides.
// If TWILIO_STUB is set, it returns a stub client for testing.
// If TWILIO_HOST is set, it returns a client that redirects requests to the given host.
// Otherwise, it returns http.DefaultClient for normal Twilio API usage.
func getCustomHTTPClient() *http.Client {
	switch {
	case os.Getenv("TWILIO_STUB") != "":
		logger.Infof("Twilio Client: Using stub client")
		return newStubClient(200, `{"sid":"SM_STUBBED","status":"sent"}`)
	case os.Getenv("TWILIO_HOST") != "":
		logger.Infof("Twilio Client: Using redirect client to host: %s", os.Getenv("TWILIO_HOST"))
		return newRedirectClient(os.Getenv("TWILIO_HOST"))
	default:
		logger.Infof("Twilio Client: Using production Twilio API")
		return nil
	}
}

// newRedirectClient returns a custom http client that adjusts requests to a mock server
// We do not want to accidentally call the real twilio endpoint during development
// TODO: use this function and redirect to mock twilio microservice when created
//
// url example: "localhost:3000"
func newRedirectClient(url string) *http.Client {
	return &http.Client{
		Transport: roundTripperFunc(func(req *http.Request) (*http.Response, error) {
			// Redirect all API traffic to mock server
			req.URL.Scheme = "http"
			req.URL.Host = url
			req.Host = url
			return http.DefaultTransport.RoundTrip(req)
		}),
	}
}

// newStubClient returns an *http.Client that always returns the given status and body,
// without making any real network requests.
// TODO: remove this function when a mock for twilio is implemented
func newStubClient(status int, body string) *http.Client {
	return &http.Client{
		// Use a custom RoundTripper that ignores req and returns a fake response.
		Transport: roundTripperFunc(func(req *http.Request) (*http.Response, error) {
			// Build a minimal HTTP response with the given status & body.
			return &http.Response{
				StatusCode:    status,
				Body:          io.NopCloser(strings.NewReader(body)),
				ContentLength: int64(len(body)),
				Header:        make(http.Header),
				Request:       req,
			}, nil
		}),
	}
}

// roundTripperFunc is a helper type that simplifies custom RoundTripper creation.
// Instead of defining a full struct, we can just write a function.
type roundTripperFunc func(*http.Request) (*http.Response, error)

// RoundTrip satisfies the http.RoundTripper interface.
// It calls the function to handle the HTTP request.
func (f roundTripperFunc) RoundTrip(req *http.Request) (*http.Response, error) {
	return f(req)
}
