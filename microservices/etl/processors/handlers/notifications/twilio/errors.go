package twilio

import "errors"

var (
	ErrMissingAccountSid = errors.New("TWILIO_ACCOUNT_SID environment variable not set")
	ErrMissingAPIKey     = errors.New("TWILIO_API_KEY environment variable not set")
	ErrMissingAPISecret  = errors.New("TWILIO_API_SECRET environment variable not set")
	ErrClientNotFound    = errors.New("twilio client not found in context")
	ErrMissingFromPhone  = errors.New("TWILIO_FROM_PHONE environment variable not set")
	ErrCreateMessage     = errors.New("error creating message")
)
