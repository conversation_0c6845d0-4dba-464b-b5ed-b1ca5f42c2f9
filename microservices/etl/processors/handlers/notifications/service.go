package notifications

import "context"

// SMSService defines methods for sending SMS notifications
type SMSService interface {
	SendSMS(ctx context.Context, toPhone string, messageBody string) error
}

// EmailService defines methods for sending email notifications
type EmailService interface {
	SendEmail(ctx context.Context, to, subject, body string) error
}

// NotificationService is a composite service that can handle multiple notification types
// It's implemented by CompositeNotificationService, not by individual services
type NotificationService interface {
	SendSMS(ctx context.Context, toPhone string, messageBody string) error
	SendEmail(ctx context.Context, to, subject, body string) error
}

// CompositeNotificationService implements NotificationService by delegating to specific services
type CompositeNotificationService struct {
	smsService   SMSService
	emailService EmailService
}

// NewCompositeNotificationService creates a new composite notification service
func NewCompositeNotificationService(smsService SMSService, emailService EmailService) *CompositeNotificationService {
	return &CompositeNotificationService{
		smsService:   smsService,
		emailService: emailService,
	}
}

// SendSMS delegates to the SMS service
func (c *CompositeNotificationService) SendSMS(ctx context.Context, toPhone string, messageBody string) error {
	if c.smsService == nil {
		return ErrSMSNotSupported
	}
	return c.smsService.SendSMS(ctx, toPhone, messageBody)
}

// SendEmail delegates to the email service
func (c *CompositeNotificationService) SendEmail(ctx context.Context, to, subject, body string) error {
	if c.emailService == nil {
		return ErrEmailNotSupported
	}
	return c.emailService.SendEmail(ctx, to, subject, body)
}
