package notifications

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/pubsub"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/etl/processors/handlers/notifications/mailgun"
	"synapse-its.com/etl/processors/handlers/notifications/twilio"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	Connector       func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributes func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	SendToDLQ       func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
	jsonUnmarshal   func(data []byte, v any) error
	jsonMarshal     func(v any) ([]byte, error)
	GetBatch        func(ctx context.Context) (bqbatch.Batcher, error)
	NewService      func() NotificationService
}

// Implement subscription handler
func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, sub connect.PsSubscription) {
	return func(ctx context.Context, sub connect.PsSubscription) {
		// Get batch for BQ insert
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}

		// Get connections
		connections, err := deps.Connector(ctx)
		if err != nil {
			logger.Errorf("Error getting connections%v", err)
			return
		}

		// Create notification service
		notificationService := deps.NewService()

		// Set up subscription
		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			redisMessageID := "msg:" + string(msg.ID)
			logger.Debugf("Received message on %s MessageID: %s. Message Data:%s", sub.ID(), string(msg.ID), string(msg.Data))
			nackMsg := false
			defer func() {
				if nackMsg {
					msg.Nack()
					connections.Redis.Del(ctx, redisMessageID)
					return
				}
				msg.Ack()
			}()

			// Check if message has already been processed
			isFirstTimeProcessedCmd := connections.Redis.SetNX(ctx, redisMessageID, "1", 20*time.Minute)
			isFirstTimeProcessed, RedisErr := isFirstTimeProcessedCmd.Result()
			if RedisErr != nil {
				nackMsg = true
				return
			}

			// Redis communication successful, check if message has already been processed
			if !isFirstTimeProcessed {
				logger.Debugf("Message %s has already been processed on subscription %s. Skipping.", string(msg.ID), sub.ID())
				return
			}

			// This is the first time the message is being processed on this subscription
			logger.Debugf("Received message on %s MessageID: %s. Message Data:%s", sub.ID(), string(msg.ID), string(msg.Data))

			// Parse Attributes
			commonAttrs, _, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				if err != nil {
					nackMsg = true
					logger.Errorf("Error sending message to the DLQ topic %v", err)
				}
				return
			}

			// Parse notification message
			var notif pubsubdata.NotificationRequest
			if err := deps.jsonUnmarshal(msg.Data, &notif); err != nil {
				logger.Errorf("Failed to parse notification message: %v", err)
				// Send to DLQ for invalid JSON
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Failed to parse notification: %v", err))
				if err != nil {
					nackMsg = true
					logger.Errorf("Error sending message to DLQ: %v", err)
				}
				return
			}

			// Add to BigQuery batch
			// Convert payload and metadata to bigquery.NullJSON
			payloadJSON, err := deps.jsonMarshal(notif.Payload)
			if err != nil {
				logger.Errorf("Failed to marshal payload: %v", err)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Failed to marshal payload: %v", err))
				if err != nil {
					nackMsg = true
					logger.Errorf("Error sending message to DLQ: %v", err)
				}
				return
			}

			metadataJSON, err := deps.jsonMarshal(notif.Metadata)
			if err != nil {
				logger.Errorf("Failed to marshal metadata: %v", err)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Failed to marshal metadata: %v", err))
				if err != nil {
					nackMsg = true
					logger.Errorf("Error sending message to the DLQ topic %v", err)
				}
				return
			}

			// Determine if payload and metadata are valid (non-empty)
			payloadValid := isValidJSON(notif.Payload)
			metadataValid := isValidJSON(notif.Metadata)

			bqItem := schemas.NotificationMessages{
				OrganizationIdentifier: commonAttrs.OrganizationIdentifier,
				Topic:                  commonAttrs.Topic,
				PubsubTimestamp:        msg.PublishTime.UTC(),
				PubsubID:               msg.ID,
				NotificationType:       notif.Type,
				Payload:                bigquery.NullJSON{JSONVal: string(payloadJSON), Valid: payloadValid},
				Metadata:               bigquery.NullJSON{JSONVal: string(metadataJSON), Valid: metadataValid},
				RawMessage:             msg.Data,
			}
			if err = batch.Add(bqItem); err != nil {
				nackMsg = true
				logger.Errorf("Error adding message to batch: %v", err)
				return
			}

			// Process based on notification type
			switch notif.Type {
			case "sms":
				// Extract SMS-specific fields
				to, ok := notif.Payload["to"].(string)
				if !ok {
					logger.Errorf("Missing 'to' field in SMS notification")
					return
				}

				message, ok := notif.Payload["message"].(string)
				if !ok {
					logger.Errorf("Missing 'message' field in SMS notification")
					return
				}

				// Send SMS
				err := notificationService.SendSMS(ctx, to, message)
				if err != nil {
					logger.Errorf("Failed to send SMS: %v", err)
					err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Failed to send SMS: %v", err))
					if err != nil {
						logger.Errorf("Error sending message to DLQ: %v", err)
					}
					return
				}

				logger.Infof("Successfully sent SMS to %s", to)

			case "email":
				// Extract email-specific fields
				to, ok := notif.Payload["to"].(string)
				if !ok {
					logger.Errorf("Missing 'to' field in email notification")
					return
				}

				subject, ok := notif.Payload["subject"].(string)
				if !ok {
					logger.Errorf("Missing 'subject' field in email notification")
					return
				}

				body, ok := notif.Payload["body"].(string)
				if !ok {
					logger.Errorf("Missing 'body' field in email notification")
					return
				}

				// Send email
				err := notificationService.SendEmail(ctx, to, subject, body)
				if err != nil {
					logger.Errorf("Failed to send email: %v", err)
					err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Failed to send email: %v", err))
					if err != nil {
						logger.Errorf("Error sending message to DLQ: %v", err)
					}
					return
				}

				logger.Infof("Successfully sent email to %s", to)

			default:
				logger.Errorf("Unsupported notification type: %s", notif.Type)
			}
		})
		if err != nil {
			logger.Errorf("Error receiving messages: %v", err)
		}
	}
}

// isValidJSON checks if a map contains meaningful data (not nil or empty)
func isValidJSON(data map[string]any) bool {
	return len(data) > 0
}

var notificationService = func() NotificationService {
	// Create composite service with both SMS and email capabilities
	return NewCompositeNotificationService(
		twilio.NewService(),  // SMS service
		mailgun.NewService(), // Email service
	)
}

// Handler is the production-ready Pub/Sub processor using real dependencies
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:       connect.GetConnections,
	ParseAttributes: pubsubdata.ParseAttributes,
	SendToDLQ:       etlShared.SendToDLQ,
	GetBatch:        bqbatch.GetBatch,
	jsonUnmarshal:   json.Unmarshal,
	jsonMarshal:     json.Marshal,
	NewService:      notificationService,
})
