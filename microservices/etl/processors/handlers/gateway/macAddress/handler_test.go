package macAddress

import (
	"context"
	"errors"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/gateway/v1"
	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/shared/bqbatch"
	connect "synapse-its.com/shared/connect"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
)

// Mock implementations
type mockConnector struct {
	mock.Mock
}

func (m *mockConnector) GetConnections(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
	args := m.Called(ctx, checkConnections)
	return args.Get(0).(*connect.Connections), args.Error(1)
}

type mockPubsubClient struct {
	mock.Mock
	connect.PsClient
}

func (m *mockPubsubClient) Subscription(name string) connect.PsSubscription {
	args := m.Called(name)
	return args.Get(0).(connect.PsSubscription)
}

type mockSubscription struct {
	mock.Mock
	connect.PsSubscription
}

func (m *mockSubscription) Receive(ctx context.Context, f func(context.Context, *pubsub.Message)) error {
	args := m.Called(ctx, f)
	return args.Error(0)
}

type mockBatcher struct {
	mock.Mock
	bqbatch.Batcher
}

func (m *mockBatcher) Add(item interface{}) error {
	args := m.Called(item)
	return args.Error(0)
}

func (m *mockBatcher) Close() error {
	args := m.Called()
	return args.Error(0)
}

func TestHandlerWithDeps(t *testing.T) {
	ctx := context.Background()
	now := time.Now()

	cases := []struct {
		name          string
		connErr       error
		recvErr       error
		attrErr       error
		unmarshalErr  error
		batchErr      error
		batchAddErr   error
		marshalErr    error
		dlqErr        error
		upsertErr     bool
		upsertMAErr   error
		wantDLQ       int
		wantBatchAdds int
	}{
		{
			name:    "connector error",
			connErr: errors.New("no conn"),
			wantDLQ: 0, wantBatchAdds: 0,
		},
		{
			name:    "receive error",
			recvErr: errors.New("recv fail"),
			wantDLQ: 0, wantBatchAdds: 0,
		},
		{
			name:    "json parse error",
			attrErr: errors.New("fail attr parse"),
			wantDLQ: 1, wantBatchAdds: 0,
		},
		{
			name:         "unmarshal error",
			unmarshalErr: errors.New("bad proto"),
			wantDLQ:      1, wantBatchAdds: 0,
		},
		{
			name:         "get batch error",
			unmarshalErr: nil,
			batchErr:     errors.New("no batch"),
			wantDLQ:      0, wantBatchAdds: 0,
		},
		{
			name:        "batch add error",
			batchAddErr: errors.New("add fail"),
			wantDLQ:     1, wantBatchAdds: 0,
		},
		{
			name:        "marshal error",
			batchAddErr: errors.New("any"),
			marshalErr:  errors.New("marshal fail"),
			wantDLQ:     0, wantBatchAdds: 0,
		},
		{
			name:    "JSON parse + DLQ error",
			attrErr: errors.New("fail attr parse"),
			dlqErr:  errors.New("dlq failed"),
			wantDLQ: 1, wantBatchAdds: 0,
		},
		{
			name:         "proto unmarshal + DLQ error",
			unmarshalErr: errors.New("bad proto"),
			dlqErr:       errors.New("dlq failed"),
			wantDLQ:      1, wantBatchAdds: 0,
		},
		{
			name:        "final DLQ error path",
			batchAddErr: errors.New("any"),
			dlqErr:      errors.New("dlq failed"),
			wantDLQ:     1, wantBatchAdds: 0,
		},
		{
			name:    "happy path",
			wantDLQ: 0, wantBatchAdds: 1,
		},
		{
			name:        "upsert error",
			upsertMAErr: errors.New("upsert failed"),
			wantDLQ:     0, wantBatchAdds: 1,
		},
	}

	for _, tc := range cases {
		tc := tc

		t.Run(tc.name, func(t *testing.T) {
			// --- setup fakes ---
			conns := mocks.FakeConns()
			psc := conns.Pubsub.(*mocks.FakePubsubClient)
			psc.ReceiveError = tc.recvErr

			fdb := &mocks.FakeDBExecutor{}
			if tc.upsertErr {
				fdb.EnableFailAfter = true
				fdb.ExecCallFailAfter = 0
			}
			conns.Postgres = fdb

			// create subscription
			topic := psc.Topic("topic-" + tc.name)
			sub, err := psc.CreateSubscription(ctx, "sub-"+tc.name, connect.SubscriptionConfig{Topic: topic})
			if err != nil {
				t.Fatal(err)
			}

			// publish message if connector & receive ok
			if tc.connErr == nil && tc.recvErr == nil {
				msg := &pubsub.Message{ID: "m-" + tc.name, Data: []byte{1, 2, 3}, PublishTime: now, Attributes: map[string]string{}}
				topic.Publish(ctx, msg)
			}

			// fake batcher
			added := 0
			fakeBatch, _ := mocks.FakeBatch(ctx)
			fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				if tc.batchAddErr != nil {
					return tc.batchAddErr
				}
				added++
				return nil
			}

			// collect DLQ calls
			dlq := 0

			// --- build deps ---
			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conns, tc.connErr
				},
				SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, recieve string) error {
					dlq++
					return tc.dlqErr
				},
				UnmarshalDevice: func(_ []byte) (*gatewayv1.DeviceData, error) {
					if tc.unmarshalErr != nil {
						return nil, tc.unmarshalErr
					}
					// default single message with MAC address
					return &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{
						{DeviceId: "dev1", Message: []byte("00:11:22:33:44:55")},
					}}, nil
				},
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return fakeBatch, tc.batchErr
				},
				ToBQ:          edihelper.MacAddressToBQ,
				MarshalDevice: func(msg proto.Message) ([]byte, error) { return []byte{1, 2, 3}, tc.marshalErr },
				ParseAttributes: ParseAttributesFunc(func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, nil
				}),
				UpsertDevice: func(pg connect.DatabaseExecutor, rec map[string]*edihelper.DeviceMacAddress) error {
					if tc.upsertMAErr != nil {
						return tc.upsertMAErr
					}
					return upsertDeviceMacAddress(pg, rec)
				},
			}

			// --- invoke handler ---
			h := HandlerWithDeps(deps)
			h(ctx, sub)

			// --- asserts ---
			assert.Equal(t, tc.wantDLQ, dlq, "DLQ calls should match expected")
			assert.Equal(t, tc.wantBatchAdds, added, "batch.Add calls should match expected")
		})
	}
}

type MockResult struct{}

func (m MockResult) LastInsertId() (int64, error) { return 0, nil }
func (m MockResult) RowsAffected() (int64, error) { return 1, nil }

func TestUpsertDeviceMacAddress_Batch(t *testing.T) {
	now := time.Now()
	records := map[string]*edihelper.DeviceMacAddress{
		"dev-1": {
			MacAddress:      "AA:BB:CC:DD:01",
			PubsubTimestamp: now.Add(-1 * time.Hour),
			UpdatedAt:       now,
		},
		"dev-2": {
			MacAddress:      "AA:BB:CC:DD:02",
			PubsubTimestamp: now.Add(-30 * time.Minute),
			UpdatedAt:       now,
		},
	}

	// Setup expected query & arguments
	fdb := &mocks.FakeDBExecutor{}
	// Call the function
	err := upsertDeviceMacAddress(fdb, records)
	assert.NoError(t, err)
}

func Test_upsertDeviceMacAddress_emptyInput(t *testing.T) {
	fdb := &mocks.FakeDBExecutor{}
	rec := map[string]*edihelper.DeviceMacAddress{}
	// err := upsertDeviceRMSEngine(fdb, rec)
	err := upsertDeviceMacAddress(fdb, rec)
	assert.NoError(t, err, "expected no error when input map is empty")
}
