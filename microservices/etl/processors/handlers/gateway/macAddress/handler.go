package macAddress

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/gateway/v1"
	"cloud.google.com/go/pubsub"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc           func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc     func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	DLQSender               func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
	UnmarshalDeviceDataFunc func(raw []byte) (*gatewayv1.DeviceData, error)
	BatchGetter             func(ctx context.Context) (bqbatch.Batcher, error)
	ToBQConverter           func(orgID string, sgwID string, tz string, topic string, pubsubID string, deviceID string, pubsubTS time.Time, macAddress string) schemas.MacAddress
	MarshalDeviceDataFunc   func(msg proto.Message) ([]byte, error)
	UpsertFunc              func(pg connect.DatabaseExecutor, rec map[string]*edihelper.DeviceMacAddress) error
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector       ConnectorFunc
	ParseAttributes ParseAttributesFunc
	SendToDLQ       DLQSender
	UnmarshalDevice UnmarshalDeviceDataFunc
	GetBatch        BatchGetter
	ToBQ            ToBQConverter
	MarshalDevice   MarshalDeviceDataFunc
	UpsertDevice    UpsertFunc
}

func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, sub connect.PsSubscription) {
	return func(ctx context.Context, sub connect.PsSubscription) {
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}
		connections, err := deps.Connector(ctx)
		pg := connections.Postgres
		if err != nil {
			logger.Errorf("Error getting connections%v", err)
			return
		}
		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s MessageID: %s. Message Data:%s", sub.ID(), string(msg.ID), string(msg.Data))

			// Parse Attributes
			commonAttrs, httpHeader, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Unmarshall protobuf message
			dd, errUm := deps.UnmarshalDevice(msg.Data)
			if errUm != nil {
				logger.Errorf("Error Unmarshaling the Device Data: %v", errUm)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Error Unmarshaling the Device Data: %v", errUm))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Loop through each record
			unprocessedDevices := new(gatewayv1.DeviceData)
			validRecords := make(map[string]*edihelper.DeviceMacAddress)

			for _, d := range dd.GetMessages() {
				msgBytes := d.GetMessage()

				// Add to the bigquery insert batch
				if err = batch.Add(deps.ToBQ(
					commonAttrs.OrganizationIdentifier,
					httpHeader.GatewayDeviceID,
					httpHeader.GatewayTimezone,
					commonAttrs.Topic,
					msg.ID,
					d.DeviceId,
					msg.PublishTime.UTC(),
					string(msgBytes),
				)); err != nil {
					logger.Infof("Error adding message to batch: %v", err)
					unprocessedDevices.Messages = append(unprocessedDevices.Messages, d)
					continue
				}
				validRecords[d.DeviceId] = &edihelper.DeviceMacAddress{
					DeviceID:        d.DeviceId,
					MacAddress:      string(msgBytes),
					PubsubTimestamp: msg.PublishTime.UTC(),
					UpdatedAt:       time.Now(),
				}
			}

			if len(validRecords) > 0 {
				if err := deps.UpsertDevice(pg, validRecords); err != nil {
					logger.Errorf("Error adding message to postgres: %v", err)
				}
			}

			// Send unprocessed device messages to DLQ for further investigation
			if len(unprocessedDevices.Messages) != 0 {
				logger.Warnf("Unable to process (%v) device messages", len(unprocessedDevices.Messages))
				msg.Data, err = deps.MarshalDevice(unprocessedDevices)
				if err != nil {
					logger.Errorf("Unable to marshal unprocessed messages")
					msg.Ack() // At this point consider data lost, don't try to reprocess the whole batch
					return
				}

				// Send messages to DLQ
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("unable to process (%v) device messages", len(unprocessedDevices.Messages)))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Ack() // At this point consider data lost, don't try to reprocess the whole batch
					return
				}
			}

			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription %s: %v", sub.ID(), err)
		}
	}
}

func upsertDeviceMacAddress(pg connect.DatabaseExecutor, rec map[string]*edihelper.DeviceMacAddress) error {
	if len(rec) == 0 {
		return nil
	}
	const baseQuery = `
		INSERT INTO {{DeviceMacAddress}} (
			DeviceId,
			MacAddress,
			PubsubTimestamp,
			UpdatedAt
		) VALUES %s
		ON CONFLICT (DeviceId) DO UPDATE SET
			MacAddress      = EXCLUDED.MacAddress,
			PubsubTimestamp = EXCLUDED.PubsubTimestamp,
			UpdatedAt       = CURRENT_TIMESTAMP
		WHERE EXCLUDED.PubsubTimestamp > {{DeviceMacAddress}}.PubsubTimestamp;
	`

	// Sort keys to ensure consistent row locking order
	keys := make([]string, 0, len(rec))
	for k := range rec {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	valueStrings := make([]string, 0, len(keys))
	valueArgs := make([]interface{}, 0, len(keys)*4)
	i := 1

	for _, k := range keys {
		r := rec[k]
		valueStrings = append(valueStrings,
			fmt.Sprintf("($%d, $%d, $%d, $%d)", i, i+1, i+2, i+3))
		valueArgs = append(valueArgs,
			r.DeviceID,
			r.MacAddress,
			r.PubsubTimestamp,
			r.UpdatedAt,
		)
		i += 4
	}

	query := fmt.Sprintf(baseQuery, strings.Join(valueStrings, ", "))
	// Retry exec in case of deadlock
	return connect.WithDeadlockRetry(func() error {
		_, err := pg.Exec(query, valueArgs...)
		return err
	})
}

// Handler is the production-ready Pub/Sub processor using real dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:       connect.GetConnections,
	ParseAttributes: pubsubdata.ParseAttributes,
	SendToDLQ:       etlShared.SendToDLQ,
	UnmarshalDevice: etlShared.UnmarshalDeviceData,
	GetBatch:        bqbatch.GetBatch,
	ToBQ:            edihelper.MacAddressToBQ,
	MarshalDevice:   etlShared.ProtoMarshal,
	UpsertDevice:    upsertDeviceMacAddress,
})
