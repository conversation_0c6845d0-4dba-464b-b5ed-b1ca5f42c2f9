package faultNotification

import (
	"testing"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/gateway/v1"
	"synapse-its.com/shared/pubsubdata"

	"github.com/stretchr/testify/assert"
)

func TestLegacyRmsDataProcessor_ProcessRmsData(t *testing.T) {
	tests := []struct {
		name       string
		httpHeader *pubsubdata.HeaderDetails
		byteMsg    []byte
	}{
		{
			name:       "Success case with valid data",
			httpHeader: &pubsubdata.HeaderDetails{},
			byteMsg:    []byte("test message"),
		},
		{
			name:       "Nil header case",
			httpHeader: nil,
			byteMsg:    []byte("test message"),
		},
		{
			name:       "Empty message case",
			httpHeader: &pubsubdata.HeaderDetails{},
			byteMsg:    []byte{},
		},
		{
			name:       "Nil message case",
			httpHeader: &pubsubdata.HeaderDetails{},
			byteMsg:    nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create processor
			processor := &legacyRmsDataProcessor{}

			// Call the method - we're just testing that it doesn't panic
			// The actual logic is tested elsewhere as mentioned in requirements
			assert.NotPanics(t, func() {
				processor.ProcessRmsData(tt.httpHeader, tt.byteMsg)
			})
		})
	}
}

func TestNextGenRmsDataProcessor_ProcessRmsData(t *testing.T) {
	tests := []struct {
		name       string
		httpHeader *pubsubdata.HeaderDetails
		byteMsg    []byte
	}{
		{
			name:       "Success case with valid data",
			httpHeader: &pubsubdata.HeaderDetails{},
			byteMsg:    []byte("test message"),
		},
		{
			name:       "Nil header case",
			httpHeader: nil,
			byteMsg:    []byte("test message"),
		},
		{
			name:       "Empty message case",
			httpHeader: &pubsubdata.HeaderDetails{},
			byteMsg:    []byte{},
		},
		{
			name:       "Nil message case",
			httpHeader: &pubsubdata.HeaderDetails{},
			byteMsg:    nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create processor
			processor := &nextGenRmsDataProcessor{}

			// Call the method - we're just testing that it doesn't panic
			// The actual logic is tested elsewhere as mentioned in requirements
			assert.NotPanics(t, func() {
				processor.ProcessRmsData(tt.httpHeader, tt.byteMsg)
			})
		})
	}
}

func TestNewRmsDataProcessor(t *testing.T) {
	tests := []struct {
		name         string
		deviceType   gatewayv1.DeviceType
		expectedType string
	}{
		{
			name:         "Unknown device type returns legacy processor",
			deviceType:   gatewayv1.DeviceType_UNKNOWN_DEVICE,
			expectedType: "*faultNotification.legacyRmsDataProcessor",
		},
		{
			name:         "EDI legacy device type returns legacy processor",
			deviceType:   gatewayv1.DeviceType_EDI_LEGACY,
			expectedType: "*faultNotification.legacyRmsDataProcessor",
		},
		{
			name:         "EDI next gen device type returns next gen processor",
			deviceType:   gatewayv1.DeviceType_EDI_NEXT_GEN,
			expectedType: "*faultNotification.nextGenRmsDataProcessor",
		},
		{
			name:         "Unhandled device type returns legacy processor as default",
			deviceType:   gatewayv1.DeviceType(999), // Some unhandled value
			expectedType: "*faultNotification.legacyRmsDataProcessor",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Call factory function
			processor := NewRmsDataProcessor(tt.deviceType)

			// Assert correct type is returned
			assert.NotNil(t, processor)
			assert.Equal(t, tt.expectedType, getTypeName(processor))

			// Verify it implements the interface
			_, ok := processor.(rmsDataProcessor)
			assert.True(t, ok, "Processor should implement rmsDataProcessor interface")
		})
	}
}

// Helper function to get type name for comparison
func getTypeName(processor rmsDataProcessor) string {
	switch processor.(type) {
	case *legacyRmsDataProcessor:
		return "*faultNotification.legacyRmsDataProcessor"
	case *nextGenRmsDataProcessor:
		return "*faultNotification.nextGenRmsDataProcessor"
	default:
		return "unknown"
	}
}

// Test interface compliance
func TestInterfaceCompliance(t *testing.T) {
	// Test that both processors implement the interface
	var _ rmsDataProcessor = &legacyRmsDataProcessor{}
	var _ rmsDataProcessor = &nextGenRmsDataProcessor{}
}

// Test that ProcessRmsData methods can be called on the returned processors
func TestProcessRmsDataCallable(t *testing.T) {
	t.Run("Legacy processor can call ProcessRmsData", func(t *testing.T) {
		processor := NewRmsDataProcessor(gatewayv1.DeviceType_EDI_LEGACY)

		// Verify it's the right type
		legacyProcessor, ok := processor.(*legacyRmsDataProcessor)
		assert.True(t, ok, "Should be legacy processor")

		// Test that the method can be called
		assert.NotPanics(t, func() {
			legacyProcessor.ProcessRmsData(&pubsubdata.HeaderDetails{}, []byte("test"))
		})
	})

	t.Run("NextGen processor can call ProcessRmsData", func(t *testing.T) {
		processor := NewRmsDataProcessor(gatewayv1.DeviceType_EDI_NEXT_GEN)

		// Verify it's the right type
		nextGenProcessor, ok := processor.(*nextGenRmsDataProcessor)
		assert.True(t, ok, "Should be next gen processor")

		// Test that the method can be called
		assert.NotPanics(t, func() {
			nextGenProcessor.ProcessRmsData(&pubsubdata.HeaderDetails{}, []byte("test"))
		})
	})
}

// Test edge cases
func TestEdgeCases(t *testing.T) {
	t.Run("Nil header and message", func(t *testing.T) {
		processor := &legacyRmsDataProcessor{}

		// This should not panic
		assert.NotPanics(t, func() {
			processor.ProcessRmsData(nil, nil)
		})
	})

	t.Run("Empty byte message", func(t *testing.T) {
		processor := &nextGenRmsDataProcessor{}

		// This should not panic
		assert.NotPanics(t, func() {
			processor.ProcessRmsData(&pubsubdata.HeaderDetails{}, []byte{})
		})
	})
}
