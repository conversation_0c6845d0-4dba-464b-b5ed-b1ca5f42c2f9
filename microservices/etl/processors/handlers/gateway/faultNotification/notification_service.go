package faultNotification

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	"unicode/utf8"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
)

// NotificationService defines the interface for processing fault notifications
type NotificationService interface {
	ProcessFaultNotification(ctx context.Context, faultData *FaultNotificationData) error
}

// NotificationPublisher defines the interface for publishing notifications
type NotificationPublisher interface {
	PublishNotification(ctx context.Context, notification *pubsubdata.NotificationRequest) error
}

// notificationService implements NotificationService interface
type notificationService struct {
	db             connect.DatabaseExecutor
	publisher      NotificationPublisher
	websiteAppsURL string
}

// NewNotificationService creates a new NotificationService instance
func NewNotificationService(db connect.DatabaseExecutor, publisher NotificationPublisher, websiteAppsURL string) NotificationService {
	return &notificationService{
		db:             db,
		publisher:      publisher,
		websiteAppsURL: websiteAppsURL,
	}
}

// notificationPublisher implements NotificationPublisher interface
type notificationPublisher struct {
	pubsubClient connect.PsClient
	topicName    string
}

func NewNotificationPublisher(pubsubClient connect.PsClient, topicName string) NotificationPublisher {
	return &notificationPublisher{
		pubsubClient: pubsubClient,
		topicName:    topicName,
	}
}

// ProcessFaultNotification processes a fault notification
func (s *notificationService) ProcessFaultNotification(ctx context.Context, faultData *FaultNotificationData) error {
	// Get eligible users for the device using authorizer method
	users, err := authorizer.GetEligibleUsersForDevice(ctx, s.db, faultData.DeviceID)
	if err != nil {
		logger.Errorf("Failed to get eligible users for device %s: %v", faultData.DeviceID, err)
		return ErrUserLookup
	}

	if len(users) == 0 {
		logger.Infof("No eligible users found for device %s", faultData.DeviceID)
		return nil // No notifications to send
	}

	logger.Infof("Found %d eligible users for device %s", len(users), faultData.DeviceID)

	// Process notifications for each user
	successCount := 0
	for _, user := range users {
		if len(user.Mobile) < 10 {
			logger.Error(ErrInvalidMobilePhone)
			continue
		}

		// Construct notification for this user
		notification := s.constructNotification(faultData, &user)

		// Publish notification
		err := s.publisher.PublishNotification(ctx, notification)
		if err != nil {
			logger.Errorf("Failed to publish notification for user %s, device %s: %v", user.ID, faultData.DeviceID, err)
			continue
		}

		logger.Debugf("Successfully published notification for user %s, device %s", user.ID, faultData.DeviceID)
		successCount++
	}

	// Check result
	if successCount == 0 {
		logger.Errorf("Failed to send any notifications for device %s", faultData.DeviceID)
		return ErrSendAllNotificationsFailed
	}

	if successCount < len(users) {
		logger.Warnf("Sent %d/%d notifications for device %s", successCount, len(users), faultData.DeviceID)
	} else {
		logger.Debugf("Successfully sent all %d notifications for device %s", successCount, faultData.DeviceID)
	}

	return nil
}

// constructNotification creates a notification request for a specific user
func (s *notificationService) constructNotification(faultData *FaultNotificationData, user *authorizer.User) *pubsubdata.NotificationRequest {
	// Get device OrigId for the URL
	deviceOrigId, monitorName, monitorId, err := s.getDeviceOrigId(faultData.DeviceID)
	if err != nil {
		logger.Errorf("Failed to get device OrigId for device %s: %v", faultData.DeviceID, err)
		// Fall back to using DeviceID if OrigId lookup fails
		deviceOrigId = faultData.DeviceID
		monitorName = ""
		monitorId = faultData.DeviceID
	}

	message := fmt.Sprintf("EDIFSA\n\nDevice:\nID: %s\nName: %s\n\nMsg:\n%s\n\nDetail:\n%s",
		monitorId,
		shortenString(monitorName, 25),
		shortenString(faultData.FaultReason, 23),
		s.websiteAppsURL+deviceOrigId,
	)

	return &pubsubdata.NotificationRequest{
		Type: "sms",
		Payload: map[string]interface{}{
			"to":      user.Mobile,
			"message": message,
		},
		Metadata: map[string]interface{}{
			// Source and processing info
			"source":          "fault_notification_service",
			"notification_id": fmt.Sprintf("fault-%s-%s", faultData.DeviceID, user.ID),
			"created_at":      time.Now().UTC(),

			// Device information
			"device_id":        faultData.DeviceID,
			"user_device_id":   faultData.UserDeviceID,
			"user_device_name": faultData.UserDeviceName,
			"device_orig_id":   deviceOrigId,
			"monitor_name":     monitorName,
			"monitor_id":       monitorId,

			// Fault information
			"fault_reason": faultData.FaultReason,
			"faulted_at":   faultData.FaultedAt,

			// User information
			"user_id":       user.ID,
			"user_mobile":   user.Mobile, // For logging/debugging - helps track delivery
			"user_timezone": user.IanaTimezone,

			// Message information
			"message_length": len(message),
		},
	}
}

// getDeviceOrigId retrieves the OrigId for a device using its UUID
func (s *notificationService) getDeviceOrigId(deviceID string) (string, string, string, error) {
	query := `SELECT d.OrigId, COALESCE(m.MonitorName, '') AS MonitorName, COALESCE(m.MonitorId, 0) AS MonitorId FROM {{Device}} d LEFT JOIN {{DeviceMonitorName}} m ON d.Id = m.DeviceId WHERE d.Id = $1 AND NOT d.IsDeleted`
	type Device struct {
		OrigID      int64  `db:"origid"`
		MonitorName string `db:"monitorname"`
		MonitorID   int64  `db:"monitorid"`
	}
	var device Device
	err := s.db.QueryRowStruct(&device, query, deviceID)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to query device: %w", err)
	}

	return fmt.Sprintf("%d", device.OrigID), device.MonitorName, fmt.Sprintf("%d", device.MonitorID), nil
}

// PublishNotification publishes a notification to the notification-alerts-topic
func (p *notificationPublisher) PublishNotification(ctx context.Context, notification *pubsubdata.NotificationRequest) error {
	logger.Debugf("Publishing notification to topic: %s", p.topicName)

	// Marshal notification to JSON
	data, err := json.Marshal(notification)
	if err != nil {
		logger.Errorf("Failed to marshal notification: %v", err)
		return ErrMarshalJSONNotification
	}

	// Build proper attributes using pubsubdata.BuildAttributes
	attrs := pubsubdata.BuildAttributes(
		pubsubdata.CommonAttributes{
			Topic: p.topicName,
		},
		pubsubdata.HeaderDetails{}, // Empty headers for notification messages
	)

	// Publish to notification-alerts-topic
	topic := p.pubsubClient.Topic(p.topicName)
	result := topic.Publish(ctx, &pubsub.Message{
		Data:       data,
		Attributes: attrs,
	})

	// Wait for publish result
	_, err = result.Get(ctx)
	if err != nil {
		logger.Errorf("Failed to publish notification: %v", err)
		return ErrNotificationPublish
	}

	logger.Debugf("Successfully published notification to topic: %s", p.topicName)
	return nil
}

// shortenString truncates a string to maxChars length, preserving UTF-8 encoding
func shortenString(input string, maxChars int) string {
	// Handle edge cases for zero or negative maxChars
	if maxChars <= 0 {
		return "..."
	}

	// Return the input string if it's already within the limit
	runeCount := utf8.RuneCountInString(input)
	if runeCount <= maxChars {
		return input
	}

	// Truncate to maxChars characters and add "..."
	runes := []rune(input)
	shortened := string(runes[:maxChars])
	return shortened + "..."
}
