package faultNotification

import (
	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/gateway/v1"
	"synapse-its.com/shared/devices"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

type rmsDataProcessor interface {
	ProcessRmsData(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (rmsStatusRecord *edihelper.RmsStatusRecord, headerDetails *edihelper.HeaderRecord, err error)
}

type legacyRmsDataProcessor struct{}

func (p *legacyRmsDataProcessor) ProcessRmsData(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (rmsStatusRecord *edihelper.RmsStatusRecord, headerDetails *edihelper.HeaderRecord, err error) {
	return devices.ProcessRmsData(httpHeader, byteMsg)
}

type nextGenRmsDataProcessor struct{}

func (p *nextGenRmsDataProcessor) ProcessRmsData(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (rmsStatusRecord *edihelper.RmsStatusRecord, headerDetails *edihelper.HeaderRecord, err error) {
	return devices.ProcessNextGenRmsData(httpHeader, byteMsg)
}

func NewRmsDataProcessor(deviceType gatewayv1.DeviceType) rmsDataProcessor {
	switch deviceType {
	case gatewayv1.DeviceType_UNKNOWN_DEVICE:
		return &legacyRmsDataProcessor{}
	case gatewayv1.DeviceType_EDI_LEGACY:
		return &legacyRmsDataProcessor{}
	case gatewayv1.DeviceType_EDI_NEXT_GEN:
		return &nextGenRmsDataProcessor{}
	default:
		return &legacyRmsDataProcessor{}
	}
}
