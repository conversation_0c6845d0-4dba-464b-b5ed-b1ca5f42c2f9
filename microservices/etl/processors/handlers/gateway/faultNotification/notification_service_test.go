package faultNotification

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"
	"unicode/utf8"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	mockspubsub "synapse-its.com/shared/mocks/pubsub"
	"synapse-its.com/shared/pubsubdata"
)

// Test_NewNotificationService tests the NewNotificationService constructor function
func Test_NewNotificationService(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		db             connect.DatabaseExecutor
		publisher      NotificationPublisher
		websiteAppsURL string
		expectedType   string
	}{
		{
			name:           "success_with_valid_dependencies",
			db:             &mocks.FakeDBExecutor{},
			publisher:      &MockNotificationPublisher{},
			websiteAppsURL: "https://example.com/apps/",
			expectedType:   "*faultNotification.notificationService",
		},
		{
			name:           "success_with_nil_dependencies",
			db:             nil,
			publisher:      nil,
			websiteAppsURL: "",
			expectedType:   "*faultNotification.notificationService",
		},
		{
			name:           "success_with_empty_url",
			db:             &mocks.FakeDBExecutor{},
			publisher:      &MockNotificationPublisher{},
			websiteAppsURL: "",
			expectedType:   "*faultNotification.notificationService",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute function under test
			result := NewNotificationService(tt.db, tt.publisher, tt.websiteAppsURL)

			// Assert results
			assert.NotNil(t, result, "should return non-nil NotificationService")
			assert.Implements(t, (*NotificationService)(nil), result, "should implement NotificationService interface")

			// Verify internal state
			service := result.(*notificationService)
			assert.Equal(t, tt.db, service.db, "should set db correctly")
			assert.Equal(t, tt.publisher, service.publisher, "should set publisher correctly")
			assert.Equal(t, tt.websiteAppsURL, service.websiteAppsURL, "should set websiteAppsURL correctly")
		})
	}
}

// Test_NewNotificationPublisher tests the NewNotificationPublisher constructor function
func Test_NewNotificationPublisher(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		pubsubClient connect.PsClient
		topicName    string
		expectedType string
	}{
		{
			name:         "success_with_valid_client",
			pubsubClient: mockspubsub.NewFakePubsubClient(),
			topicName:    "test-topic",
			expectedType: "*faultNotification.notificationPublisher",
		},
		{
			name:         "success_with_nil_client",
			pubsubClient: nil,
			topicName:    "test-topic",
			expectedType: "*faultNotification.notificationPublisher",
		},
		{
			name:         "success_with_empty_topic",
			pubsubClient: mockspubsub.NewFakePubsubClient(),
			topicName:    "",
			expectedType: "*faultNotification.notificationPublisher",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute function under test
			result := NewNotificationPublisher(tt.pubsubClient, tt.topicName)

			// Assert results
			assert.NotNil(t, result, "should return non-nil NotificationPublisher")
			assert.Implements(t, (*NotificationPublisher)(nil), result, "should implement NotificationPublisher interface")

			// Verify internal state
			publisher := result.(*notificationPublisher)
			assert.Equal(t, tt.pubsubClient, publisher.pubsubClient, "should set pubsubClient correctly")
			assert.Equal(t, tt.topicName, publisher.topicName, "should set topicName correctly")
		})
	}
}

// Test_ProcessFaultNotification tests the ProcessFaultNotification method
func Test_ProcessFaultNotification(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		faultData      *FaultNotificationData
		setupDB        func() connect.DatabaseExecutor
		setupPublisher func() NotificationPublisher
		websiteAppsURL string
		expectedError  error
		wantErr        bool
	}{
		{
			name: "success_single_user_valid_mobile",
			faultData: &FaultNotificationData{
				DeviceID:       "device-123",
				UserDeviceID:   "device-123",
				UserDeviceName: "Test Device",
				FaultReason:    "High Temperature",
				FaultedAt:      time.Now().UTC(),
			},
			setupDB: func() connect.DatabaseExecutor {
				mockDB := &mocks.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					users := dest.(*[]authorizer.User)
					*users = []authorizer.User{
						{
							ID:           "27c68173-289a-5e53-92ae-c598d847038c",
							Mobile:       "1234567890",
							IanaTimezone: "America/New_York",
						},
					}
					return nil
				}
				return mockDB
			},
			setupPublisher: func() NotificationPublisher {
				pub := &MockNotificationPublisher{}
				pub.PublishNotificationFunc = func(ctx context.Context, notification *pubsubdata.NotificationRequest) error {
					return nil
				}
				return pub
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedError:  nil,
			wantErr:        false,
		},
		{
			name: "success_multiple_users",
			faultData: &FaultNotificationData{
				DeviceID:       "device-456",
				UserDeviceID:   "device-456",
				UserDeviceName: "Multi Device",
				FaultReason:    "Low Voltage",
				FaultedAt:      time.Now().UTC(),
			},
			setupDB: func() connect.DatabaseExecutor {
				mockDB := &mocks.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					users := dest.(*[]authorizer.User)
					*users = []authorizer.User{
						{ID: "27c68173-289a-5e53-92ae-c598d847038c", Mobile: "1234567890", IanaTimezone: "America/New_York"},
						{ID: "8f4e2b1a-3c9d-4e6f-a2b8-1d5e7f9c3a2b", Mobile: "0987654321", IanaTimezone: "America/Los_Angeles"},
					}
					return nil
				}
				return mockDB
			},
			setupPublisher: func() NotificationPublisher {
				pub := &MockNotificationPublisher{}
				pub.PublishNotificationFunc = func(ctx context.Context, notification *pubsubdata.NotificationRequest) error {
					return nil
				}
				return pub
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedError:  nil,
			wantErr:        false,
		},
		{
			name: "error_user_lookup_failed",
			faultData: &FaultNotificationData{
				DeviceID:       "device-error",
				UserDeviceID:   "device-error",
				UserDeviceName: "Error Device",
				FaultReason:    "Connection Lost",
				FaultedAt:      time.Now().UTC(),
			},
			setupDB: func() connect.DatabaseExecutor {
				mockDB := &mocks.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
				return mockDB
			},
			setupPublisher: func() NotificationPublisher {
				return &MockNotificationPublisher{}
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedError:  ErrUserLookup,
			wantErr:        true,
		},
		{
			name: "success_no_users_found",
			faultData: &FaultNotificationData{
				DeviceID:       "device-no-users",
				UserDeviceID:   "device-no-users",
				UserDeviceName: "No Users Device",
				FaultReason:    "System Error",
				FaultedAt:      time.Now().UTC(),
			},
			setupDB: func() connect.DatabaseExecutor {
				mockDB := &mocks.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					users := dest.(*[]authorizer.User)
					*users = []authorizer.User{}
					return nil
				}
				return mockDB
			},
			setupPublisher: func() NotificationPublisher {
				return &MockNotificationPublisher{}
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedError:  nil,
			wantErr:        false,
		},
		{
			name: "error_invalid_mobile_phone",
			faultData: &FaultNotificationData{
				DeviceID:       "device-invalid-mobile",
				UserDeviceID:   "device-invalid-mobile",
				UserDeviceName: "Invalid Mobile Device",
				FaultReason:    "Test Error",
				FaultedAt:      time.Now().UTC(),
			},
			setupDB: func() connect.DatabaseExecutor {
				mockDB := &mocks.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					users := dest.(*[]authorizer.User)
					*users = []authorizer.User{
						{ID: "b1c2d3e4-f5a6-7b8c-9d0e-1f2a3b4c5d6e", Mobile: "123", IanaTimezone: "America/New_York"}, // Invalid mobile - too short
					}
					return nil
				}
				return mockDB
			},
			setupPublisher: func() NotificationPublisher {
				return &MockNotificationPublisher{}
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedError:  ErrSendAllNotificationsFailed,
			wantErr:        true,
		},
		{
			name: "error_publish_notification_failed",
			faultData: &FaultNotificationData{
				DeviceID:       "device-publish-error",
				UserDeviceID:   "device-publish-error",
				UserDeviceName: "Publish Error Device",
				FaultReason:    "Publish Test",
				FaultedAt:      time.Now().UTC(),
			},
			setupDB: func() connect.DatabaseExecutor {
				mockDB := &mocks.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					users := dest.(*[]authorizer.User)
					*users = []authorizer.User{
						{ID: "c1d2e3f4-a5b6-7c8d-9e0f-1a2b3c4d5e6f", Mobile: "1234567890", IanaTimezone: "America/New_York"},
					}
					return nil
				}
				return mockDB
			},
			setupPublisher: func() NotificationPublisher {
				pub := &MockNotificationPublisher{}
				pub.PublishNotificationFunc = func(ctx context.Context, notification *pubsubdata.NotificationRequest) error {
					return errors.New("publish failed")
				}
				return pub
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedError:  ErrSendAllNotificationsFailed,
			wantErr:        true,
		},
		{
			name: "partial_success_some_notifications_failed",
			faultData: &FaultNotificationData{
				DeviceID:       "device-partial",
				UserDeviceID:   "device-partial",
				UserDeviceName: "Partial Device",
				FaultReason:    "Partial Test",
				FaultedAt:      time.Now().UTC(),
			},
			setupDB: func() connect.DatabaseExecutor {
				mockDB := &mocks.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					users := dest.(*[]authorizer.User)
					*users = []authorizer.User{
						{ID: "d1e2f3a4-b5c6-7d8e-9f0a-1b2c3d4e5f6a", Mobile: "1234567890", IanaTimezone: "America/New_York"},
						{ID: "e1f2a3b4-c5d6-7e8f-9a0b-1c2d3e4f5a6b", Mobile: "123", IanaTimezone: "America/Los_Angeles"}, // Invalid mobile
					}
					return nil
				}
				return mockDB
			},
			setupPublisher: func() NotificationPublisher {
				pub := &MockNotificationPublisher{}
				pub.PublishNotificationFunc = func(ctx context.Context, notification *pubsubdata.NotificationRequest) error {
					return nil
				}
				return pub
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedError:  nil,
			wantErr:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup service
			db := tt.setupDB()
			publisher := tt.setupPublisher()
			service := NewNotificationService(db, publisher, tt.websiteAppsURL)

			// Execute function under test
			ctx := context.Background()
			err := service.ProcessFaultNotification(ctx, tt.faultData)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "expected error but got nil")
				if tt.expectedError != nil {
					assert.Equal(t, tt.expectedError, err, "should return expected error")
				}
			} else {
				assert.NoError(t, err, "unexpected error")
			}
		})
	}
}

// Test_constructNotification tests the constructNotification method
func Test_constructNotification(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name             string
		faultData        *FaultNotificationData
		user             *authorizer.User
		websiteAppsURL   string
		mockOrigId       int64
		mockMonitorName  string
		mockMonitorID    int64
		mockDBError      error
		expectedType     string
		expectedPayload  map[string]interface{}
		expectedMetadata map[string]interface{}
	}{
		{
			name: "success_normal_case",
			faultData: &FaultNotificationData{
				DeviceID:       "device-123",
				UserDeviceID:   "user-device-123",
				UserDeviceName: "Test Device Name",
				FaultReason:    "High Temperature Alert",
				FaultedAt:      time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC),
			},
			user: &authorizer.User{
				ID:           "f1a2b3c4-d5e6-7f8a-9b0c-1d2e3f4a5b6c",
				Mobile:       "1234567890",
				IanaTimezone: "America/New_York",
			},
			websiteAppsURL:  "https://example.com/apps/",
			mockOrigId:      12345,
			mockMonitorName: "Test Device Name",
			mockMonitorID:   12345,
			mockDBError:     nil,
			expectedType:    "sms",
			expectedPayload: map[string]interface{}{
				"to":      "1234567890",
				"message": "EDIFSA\n\nDevice:\nID: 12345\nName: Test Device Name\n\nMsg:\nHigh Temperature Alert\n\nDetail:\nhttps://example.com/apps/12345",
			},
			expectedMetadata: map[string]interface{}{
				// Source and processing info
				"source":          "fault_notification_service",
				"notification_id": "fault-device-123-f1a2b3c4-d5e6-7f8a-9b0c-1d2e3f4a5b6c",

				// Device information
				"device_id":        "device-123",
				"user_device_id":   "user-device-123",
				"user_device_name": "Test Device Name",
				"device_orig_id":   "12345",
				"monitor_name":     "Test Device Name",
				"monitor_id":       "12345",

				// Fault information
				"fault_reason": "High Temperature Alert",
				"faulted_at":   time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC),

				// User information
				"user_id":       "f1a2b3c4-d5e6-7f8a-9b0c-1d2e3f4a5b6c",
				"user_mobile":   "1234567890",
				"user_timezone": "America/New_York",
			},
		},
		{
			name: "success_long_device_name_truncated",
			faultData: &FaultNotificationData{
				DeviceID:       "device-456",
				UserDeviceID:   "user-device-456",
				UserDeviceName: "This is a very long device name that should be truncated",
				FaultReason:    "Error",
				FaultedAt:      time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC),
			},
			user: &authorizer.User{
				ID:           "a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d",
				Mobile:       "0987654321",
				IanaTimezone: "UTC",
			},
			websiteAppsURL:  "https://test.com/",
			mockOrigId:      67890,
			mockMonitorName: "This is a very long device name that should be truncated",
			mockMonitorID:   67890,
			mockDBError:     nil,
			expectedType:    "sms",
			expectedPayload: map[string]interface{}{
				"to":      "0987654321",
				"message": "EDIFSA\n\nDevice:\nID: 67890\nName: This is a very long devic...\n\nMsg:\nError\n\nDetail:\nhttps://test.com/67890",
			},
			expectedMetadata: map[string]interface{}{
				// Source and processing info
				"source":          "fault_notification_service",
				"notification_id": "fault-device-456-a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d",

				// Device information
				"device_id":        "device-456",
				"user_device_id":   "user-device-456",
				"user_device_name": "This is a very long device name that should be truncated",
				"device_orig_id":   "67890",
				"monitor_name":     "This is a very long device name that should be truncated",
				"monitor_id":       "67890",

				// Fault information
				"fault_reason": "Error",
				"faulted_at":   time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC),

				// User information
				"user_id":       "a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d",
				"user_mobile":   "0987654321",
				"user_timezone": "UTC",
			},
		},
		{
			name: "success_long_fault_reason_truncated",
			faultData: &FaultNotificationData{
				DeviceID:       "device-789",
				UserDeviceID:   "user-device-789",
				UserDeviceName: "Device",
				FaultReason:    "This is a very long fault reason that should be truncated properly",
				FaultedAt:      time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC),
			},
			user: &authorizer.User{
				ID:           "b1c2d3e4-f5a6-9b7c-8d0e-1f2a3b4c5d99",
				Mobile:       "5555555555",
				IanaTimezone: "Europe/London",
			},
			websiteAppsURL:  "https://app.example.com/",
			mockOrigId:      99999,
			mockMonitorName: "Device",
			mockMonitorID:   99999,
			mockDBError:     nil,
			expectedType:    "sms",
			expectedPayload: map[string]interface{}{
				"to":      "5555555555",
				"message": "EDIFSA\n\nDevice:\nID: 99999\nName: Device\n\nMsg:\nThis is a very long fau...\n\nDetail:\nhttps://app.example.com/99999",
			},
			expectedMetadata: map[string]interface{}{
				// Source and processing info
				"source":          "fault_notification_service",
				"notification_id": "fault-device-789-b1c2d3e4-f5a6-9b7c-8d0e-1f2a3b4c5d99",

				// Device information
				"device_id":        "device-789",
				"user_device_id":   "user-device-789",
				"user_device_name": "Device",
				"device_orig_id":   "99999",
				"monitor_name":     "Device",
				"monitor_id":       "99999",

				// Fault information
				"fault_reason": "This is a very long fault reason that should be truncated properly",
				"faulted_at":   time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC),

				// User information
				"user_id":       "b1c2d3e4-f5a6-9b7c-8d0e-1f2a3b4c5d99",
				"user_mobile":   "5555555555",
				"user_timezone": "Europe/London",
			},
		},
		{
			name: "success_database_error_fallback",
			faultData: &FaultNotificationData{
				DeviceID:       "device-error",
				UserDeviceID:   "user-device-error",
				UserDeviceName: "Error Device",
				FaultReason:    "Database Error",
				FaultedAt:      time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC),
			},
			user: &authorizer.User{
				ID:           "c1d2e3f4-a5b6-7c8d-9e0f-1a2b3c4d5e6f",
				Mobile:       "1111111111",
				IanaTimezone: "UTC",
			},
			websiteAppsURL:  "https://error.com/",
			mockOrigId:      0,
			mockMonitorName: "",
			mockMonitorID:   0,
			mockDBError:     errors.New("database error"),
			expectedType:    "sms",
			expectedPayload: map[string]interface{}{
				"to":      "1111111111",
				"message": "EDIFSA\n\nDevice:\nID: device-error\nName: \n\nMsg:\nDatabase Error\n\nDetail:\nhttps://error.com/device-error",
			},
			expectedMetadata: map[string]interface{}{
				// Source and processing info
				"source":          "fault_notification_service",
				"notification_id": "fault-device-error-c1d2e3f4-a5b6-7c8d-9e0f-1a2b3c4d5e6f",

				// Device information
				"device_id":        "device-error",
				"user_device_id":   "user-device-error",
				"user_device_name": "Error Device",
				"device_orig_id":   "device-error", // Fallback value when DB error occurs
				"monitor_name":     "",             // Empty when DB error occurs
				"monitor_id":       "device-error", // Fallback value when DB error occurs

				// Fault information
				"fault_reason": "Database Error",
				"faulted_at":   time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC),

				// User information
				"user_id":       "c1d2e3f4-a5b6-7c8d-9e0f-1a2b3c4d5e6f",
				"user_mobile":   "1111111111",
				"user_timezone": "UTC",
			},
		},
		{
			name: "success_empty_strings",
			faultData: &FaultNotificationData{
				DeviceID:       "",
				UserDeviceID:   "",
				UserDeviceName: "",
				FaultReason:    "",
				FaultedAt:      time.Time{},
			},
			user: &authorizer.User{
				ID:           "",
				Mobile:       "",
				IanaTimezone: "",
			},
			websiteAppsURL:  "",
			mockOrigId:      0,
			mockMonitorName: "",
			mockMonitorID:   0,
			mockDBError:     errors.New("empty device ID"),
			expectedType:    "sms",
			expectedPayload: map[string]interface{}{
				"to":      "",
				"message": "EDIFSA\n\nDevice:\nID: \nName: \n\nMsg:\n\n\nDetail:\n",
			},
			expectedMetadata: map[string]interface{}{
				// Source and processing info
				"source":          "fault_notification_service",
				"notification_id": "fault--",

				// Device information
				"device_id":        "",
				"user_device_id":   "",
				"user_device_name": "",
				"device_orig_id":   "", // Fallback value when DB error occurs
				"monitor_name":     "", // Empty when DB error occurs
				"monitor_id":       "", // Fallback value when DB error occurs

				// Fault information
				"fault_reason": "",
				"faulted_at":   time.Time{},

				// User information
				"user_id":       "",
				"user_mobile":   "",
				"user_timezone": "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database
			mockDB := &mocks.FakeDBExecutor{}
			mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
				if tt.mockDBError != nil {
					return tt.mockDBError
				}
				// Use reflection to set the fields on any struct that has them
				v := reflect.ValueOf(dest)
				if v.Kind() == reflect.Ptr && v.Elem().Kind() == reflect.Struct {
					elem := v.Elem()
					origIDField := elem.FieldByName("OrigID")
					if origIDField.IsValid() && origIDField.CanSet() && origIDField.Kind() == reflect.Int64 {
						origIDField.SetInt(tt.mockOrigId)
					}
					monitorNameField := elem.FieldByName("MonitorName")
					if monitorNameField.IsValid() && monitorNameField.CanSet() && monitorNameField.Kind() == reflect.String {
						monitorNameField.SetString(tt.mockMonitorName)
					}
					monitorIDField := elem.FieldByName("MonitorID")
					if monitorIDField.IsValid() && monitorIDField.CanSet() && monitorIDField.Kind() == reflect.Int64 {
						monitorIDField.SetInt(tt.mockMonitorID)
					}
				}
				return nil
			}

			// Create service instance
			service := &notificationService{
				db:             mockDB,
				websiteAppsURL: tt.websiteAppsURL,
			}

			// Execute function under test
			result := service.constructNotification(tt.faultData, tt.user)

			// Assert results
			assert.NotNil(t, result, "should return non-nil NotificationRequest")
			assert.Equal(t, tt.expectedType, result.Type, "should set correct type")
			assert.Equal(t, tt.expectedPayload, result.Payload, "should set correct payload")

			// Assert metadata fields individually to handle dynamic values
			assert.Equal(t, tt.expectedMetadata["source"], result.Metadata["source"], "should set correct source")
			assert.Equal(t, tt.expectedMetadata["notification_id"], result.Metadata["notification_id"], "should set correct notification_id")
			assert.IsType(t, time.Time{}, result.Metadata["created_at"], "created_at should be time.Time")
			assert.Equal(t, tt.expectedMetadata["device_id"], result.Metadata["device_id"], "should set correct device_id")
			assert.Equal(t, tt.expectedMetadata["user_device_id"], result.Metadata["user_device_id"], "should set correct user_device_id")
			assert.Equal(t, tt.expectedMetadata["user_device_name"], result.Metadata["user_device_name"], "should set correct user_device_name")
			assert.Equal(t, tt.expectedMetadata["device_orig_id"], result.Metadata["device_orig_id"], "should set correct device_orig_id")
			assert.Equal(t, tt.expectedMetadata["monitor_name"], result.Metadata["monitor_name"], "should set correct monitor_name")
			assert.Equal(t, tt.expectedMetadata["monitor_id"], result.Metadata["monitor_id"], "should set correct monitor_id")
			assert.Equal(t, tt.expectedMetadata["fault_reason"], result.Metadata["fault_reason"], "should set correct fault_reason")
			assert.Equal(t, tt.expectedMetadata["faulted_at"], result.Metadata["faulted_at"], "should set correct faulted_at")
			assert.Equal(t, tt.expectedMetadata["user_id"], result.Metadata["user_id"], "should set correct user_id")
			assert.Equal(t, tt.expectedMetadata["user_mobile"], result.Metadata["user_mobile"], "should set correct user_mobile")
			assert.Equal(t, tt.expectedMetadata["user_timezone"], result.Metadata["user_timezone"], "should set correct user_timezone")
			assert.IsType(t, 0, result.Metadata["message_length"], "message_length should be int")
			assert.GreaterOrEqual(t, result.Metadata["message_length"], 0, "message_length should be non-negative")
		})
	}
}

// Test_PublishNotification tests the PublishNotification method
func Test_PublishNotification(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		notification  *pubsubdata.NotificationRequest
		setupPubsub   func() connect.PsClient
		topicName     string
		expectedError error
		wantErr       bool
	}{
		{
			name: "success_valid_notification",
			notification: &pubsubdata.NotificationRequest{
				Type: "sms",
				Payload: map[string]interface{}{
					"to":      "1234567890",
					"message": "Test message",
				},
				Metadata: map[string]interface{}{
					"device_id": "device-123",
				},
			},
			setupPubsub: func() connect.PsClient {
				return mockspubsub.NewFakePubsubClient()
			},
			topicName:     "test-topic",
			expectedError: nil,
			wantErr:       false,
		},
		{
			name: "success_empty_notification",
			notification: &pubsubdata.NotificationRequest{
				Type:     "",
				Payload:  map[string]interface{}{},
				Metadata: map[string]interface{}{},
			},
			setupPubsub: func() connect.PsClient {
				return mockspubsub.NewFakePubsubClient()
			},
			topicName:     "test-topic",
			expectedError: nil,
			wantErr:       false,
		},
		{
			name: "success_nil_maps",
			notification: &pubsubdata.NotificationRequest{
				Type:     "sms",
				Payload:  nil,
				Metadata: nil,
			},
			setupPubsub: func() connect.PsClient {
				return mockspubsub.NewFakePubsubClient()
			},
			topicName:     "test-topic",
			expectedError: nil,
			wantErr:       false,
		},
		{
			name: "error_marshal_json_notification_failed",
			notification: &pubsubdata.NotificationRequest{
				Type: "sms",
				Payload: map[string]interface{}{
					"to":      "1234567890",
					"message": "Test message",
					"invalid": make(chan int), // Channel cannot be marshaled to JSON
				},
				Metadata: map[string]interface{}{
					"device_id": "device-123",
				},
			},
			setupPubsub: func() connect.PsClient {
				return mockspubsub.NewFakePubsubClient()
			},
			topicName:     "test-topic",
			expectedError: ErrMarshalJSONNotification,
			wantErr:       true,
		},
		{
			name: "error_publish_notification_failed",
			notification: &pubsubdata.NotificationRequest{
				Type: "sms",
				Payload: map[string]interface{}{
					"to":      "1234567890",
					"message": "Test message",
				},
				Metadata: map[string]interface{}{
					"device_id": "device-123",
				},
			},
			setupPubsub: func() connect.PsClient {
				client := mockspubsub.NewFakePubsubClient()
				// Configure client to fail on publish
				client.PublishError = errors.New("publish failed")
				return client
			},
			topicName:     "test-topic",
			expectedError: ErrNotificationPublish,
			wantErr:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup publisher
			pubsubClient := tt.setupPubsub()
			publisher := NewNotificationPublisher(pubsubClient, tt.topicName)

			// Execute function under test
			ctx := context.Background()
			err := publisher.PublishNotification(ctx, tt.notification)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "expected error but got nil")
				if tt.expectedError != nil {
					assert.Equal(t, tt.expectedError, err, "should return expected error")
				}
			} else {
				assert.NoError(t, err, "unexpected error")
			}
		})
	}
}

// Test_shortenString tests the shortenString utility function
func Test_shortenString(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		input    string
		maxLen   int
		expected string
	}{
		{
			name:     "string_shorter_than_max",
			input:    "short",
			maxLen:   10,
			expected: "short",
		},
		{
			name:     "string_equal_to_max",
			input:    "exactly10c",
			maxLen:   10,
			expected: "exactly10c",
		},
		{
			name:     "string_longer_than_max",
			input:    "this is a very long string that needs truncation",
			maxLen:   10,
			expected: "this is a ...",
		},
		{
			name:     "empty_string",
			input:    "",
			maxLen:   5,
			expected: "",
		},
		{
			name:     "unicode_string",
			input:    "こんにちは世界",
			maxLen:   5,
			expected: "こんにちは...",
		},
		{
			name:     "max_length_zero",
			input:    "test",
			maxLen:   0,
			expected: "...",
		},
		{
			name:     "max_length_negative",
			input:    "test",
			maxLen:   -1,
			expected: "...",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute function under test
			result := shortenString(tt.input, tt.maxLen)

			// Assert results
			assert.Equal(t, tt.expected, result, "should return expected shortened string")

			// Only check length constraint for positive maxLen values and when input is shorter than maxLen
			if tt.maxLen > 0 && utf8.RuneCountInString(tt.input) <= tt.maxLen {
				assert.LessOrEqual(t, utf8.RuneCountInString(result), tt.maxLen, "result should not exceed max length")
			}
		})
	}
}

// Test interface compliance
func Test_notificationService_interface_compliance(t *testing.T) {
	t.Parallel()

	t.Run("implements_NotificationService_interface", func(t *testing.T) {
		mockDB := &mocks.FakeDBExecutor{}
		service := NewNotificationService(mockDB, &MockNotificationPublisher{}, "")
		var _ NotificationService = service
		assert.Implements(t, (*NotificationService)(nil), service, "notificationService should implement NotificationService interface")
	})
}

func Test_notificationPublisher_interface_compliance(t *testing.T) {
	t.Parallel()

	t.Run("implements_NotificationPublisher_interface", func(t *testing.T) {
		publisher := NewNotificationPublisher(mockspubsub.NewFakePubsubClient(), "test-topic")
		var _ NotificationPublisher = publisher
		assert.Implements(t, (*NotificationPublisher)(nil), publisher, "notificationPublisher should implement NotificationPublisher interface")
	})
}

// Mock implementations
type MockNotificationPublisher struct {
	PublishNotificationFunc      func(ctx context.Context, notification *pubsubdata.NotificationRequest) error
	PublishNotificationCallCount int
}

func (m *MockNotificationPublisher) PublishNotification(ctx context.Context, notification *pubsubdata.NotificationRequest) error {
	m.PublishNotificationCallCount++
	if m.PublishNotificationFunc != nil {
		return m.PublishNotificationFunc(ctx, notification)
	}
	return nil
}

// Test_getDeviceOrigId tests the getDeviceOrigId method
func Test_getDeviceOrigId(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name            string
		deviceID        string
		mockOrigId      int64
		mockMonitorName string
		mockMonitorID   int64
		mockDBError     error
		expectedResult  []string
		wantErr         bool
	}{
		{
			name:            "success_valid_device_id",
			deviceID:        "550e8400-e29b-41d4-a716-446655440000",
			mockOrigId:      12345,
			mockMonitorName: "Test Monitor",
			mockMonitorID:   12345,
			mockDBError:     nil,
			expectedResult:  []string{"12345", "Test Monitor", "12345"},
			wantErr:         false,
		},
		{
			name:            "success_large_orig_id",
			deviceID:        "550e8400-e29b-41d4-a716-446655440001",
			mockOrigId:      999999999,
			mockMonitorName: "Test Monitor",
			mockMonitorID:   12345,
			mockDBError:     nil,
			expectedResult:  []string{"999999999", "Test Monitor", "12345"},
			wantErr:         false,
		},
		{
			name:            "success_zero_orig_id",
			deviceID:        "550e8400-e29b-41d4-a716-446655440002",
			mockOrigId:      0,
			mockMonitorName: "Test Monitor",
			mockMonitorID:   12345,
			mockDBError:     nil,
			expectedResult:  []string{"0", "Test Monitor", "12345"},
			wantErr:         false,
		},
		{
			name:            "error_database_connection_failure",
			deviceID:        "550e8400-e29b-41d4-a716-446655440003",
			mockOrigId:      0,
			mockMonitorName: "Test Monitor",
			mockMonitorID:   12345,
			mockDBError:     errors.New("database connection failed"),
			expectedResult:  []string{"", "", ""},
			wantErr:         true,
		},
		{
			name:            "error_device_not_found",
			deviceID:        "550e8400-e29b-41d4-a716-446655440004",
			mockOrigId:      0,
			mockMonitorName: "Test Monitor",
			mockMonitorID:   12345,
			mockDBError:     errors.New("device not found"),
			expectedResult:  []string{"", "", ""},
			wantErr:         true,
		},
		{
			name:            "success_empty_device_id",
			deviceID:        "",
			mockOrigId:      0,
			mockMonitorName: "Test Monitor",
			mockMonitorID:   12345,
			mockDBError:     errors.New("invalid device ID"),
			expectedResult:  []string{"", "", ""},
			wantErr:         true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database
			mockDB := &mocks.FakeDBExecutor{}
			mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
				if tt.mockDBError != nil {
					return tt.mockDBError
				}
				// Use reflection to set the OrigID field on any struct that has it
				v := reflect.ValueOf(dest)
				if v.Kind() == reflect.Ptr && v.Elem().Kind() == reflect.Struct {
					elem := v.Elem()
					origIDField := elem.FieldByName("OrigID")
					if origIDField.IsValid() && origIDField.CanSet() && origIDField.Kind() == reflect.Int64 {
						origIDField.SetInt(tt.mockOrigId)
					}
					monitorNameField := elem.FieldByName("MonitorName")
					if monitorNameField.IsValid() && monitorNameField.CanSet() && monitorNameField.Kind() == reflect.String {
						monitorNameField.SetString(tt.mockMonitorName)
					}
					monitorIDField := elem.FieldByName("MonitorID")
					if monitorIDField.IsValid() && monitorIDField.CanSet() && monitorIDField.Kind() == reflect.Int64 {
						monitorIDField.SetInt(tt.mockMonitorID)
					}
				}
				return nil
			}

			// Create service instance
			service := &notificationService{
				db: mockDB,
			}

			// Execute function under test
			result, monitorName, monitorId, err := service.getDeviceOrigId(tt.deviceID)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "expected error but got nil")
				assert.Equal(t, tt.expectedResult[0], result, "should return expected result even on error")
			} else {
				assert.NoError(t, err, "unexpected error")
				assert.Equal(t, tt.expectedResult[0], result, "should return expected result")
				assert.Equal(t, tt.expectedResult[1], monitorName, "should return expected monitor name")
				assert.Equal(t, tt.expectedResult[2], monitorId, "should return expected monitor ID")
			}

			// Verify database was called with correct parameters
			assert.Equal(t, 1, mockDB.QueryRowStructCallCount, "should call database exactly once")
		})
	}
}
