package rmsData

import (
	"context"
	"fmt"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/gateway/v1"
	"cloud.google.com/go/pubsub"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/devices"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc           func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc     func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	DLQSender               func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
	UnmarshalDeviceDataFunc func(raw []byte) (*gatewayv1.DeviceData, error)
	BatchGetter             func(ctx context.Context) (bqbatch.Batcher, error)
	ProcessRMSFunc          func(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (rmsStatusRecord *edihelper.RmsStatusRecord, headerDetails *edihelper.HeaderRecord, err error)
	ToBQConverter           func(orgID string, sgwID string, tz string, topic string, pubsubID string, deviceID string, pubsubTS time.Time, header schemas.HeaderRecord, rawMsg []byte, status *edihelper.RmsStatusRecord) schemas.RmsData
	MarshalDeviceDataFunc   func(msg proto.Message) ([]byte, error)
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector       ConnectorFunc
	ParseAttributes ParseAttributesFunc
	SendToDLQ       DLQSender
	UnmarshalDevice UnmarshalDeviceDataFunc
	GetBatch        BatchGetter
	ProcessRMS      ProcessRMSFunc
	ToBQ            ToBQConverter
	MarshalDevice   MarshalDeviceDataFunc
}

// HandlerWithDeps constructs a Pub/Sub processing function with injected dependencies.
func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, sub connect.PsSubscription) {
	return func(ctx context.Context, sub connect.PsSubscription) {
		batch, err := deps.GetBatch(ctx)
		if err != nil {
			logger.Errorf("Error getting batch: %v", err)
			return
		}
		// Acquire connections
		conns, err := deps.Connector(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			return
		}

		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s MessageID: %s. Data: %s", sub.ID(), msg.ID, string(msg.Data))

			// Parse attributers
			commonAttrs, httpHeader, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				err = deps.SendToDLQ(ctx, conns.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Protobuf body
			dd, err := deps.UnmarshalDevice(msg.Data)
			if err != nil {
				logger.Errorf("Error unmarshaling device data: %v", err)
				err = deps.SendToDLQ(ctx, conns.Pubsub, msg, fmt.Sprintf("Error Unmarshaling the Device Data: %v", err))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Collect failed messages
			unprocessed := new(gatewayv1.DeviceData)
			for _, d := range dd.GetMessages() {
				rmsData, rmsHdr, perr := deps.ProcessRMS(&httpHeader, d.GetMessage())
				if perr != nil {
					logger.Warnf("Error parsing record: %v", perr)
					unprocessed.Messages = append(unprocessed.Messages, d)
					continue
				}

				item := deps.ToBQ(
					commonAttrs.OrganizationIdentifier,
					httpHeader.GatewayDeviceID,
					httpHeader.GatewayTimezone,
					commonAttrs.Topic,
					msg.ID,
					d.DeviceId,
					msg.PublishTime.UTC(),
					*rmsHdr.ToBigQuerySchema(),
					d.GetMessage(),
					rmsData,
				)
				if err := batch.Add(item); err != nil {
					logger.Warnf("Error adding to batch: %v", err)
					unprocessed.Messages = append(unprocessed.Messages, d)
				}
			}

			// DLQ for any unprocessed
			if len(unprocessed.Messages) > 0 {
				logger.Warnf("Unable to process (%d) device messages", len(unprocessed.Messages))
				msg.Data, err = deps.MarshalDevice(unprocessed)
				if err != nil {
					logger.Errorf("Unable to marshal unprocessed messages: %v", err)
					msg.Ack()
					return
				}

				err = deps.SendToDLQ(ctx, conns.Pubsub, msg, fmt.Sprintf("unable to process (%v) device messages", len(unprocessed.Messages)))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Ack()
					return
				}
			}

			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed receiving from %s: %v", sub.ID(), err)
		}
	}
}

// Handler is the production-ready Pub/Sub processor using real dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:       connect.GetConnections,
	ParseAttributes: pubsubdata.ParseAttributes,
	SendToDLQ:       etlShared.SendToDLQ,
	UnmarshalDevice: etlShared.UnmarshalDeviceData,
	GetBatch:        bqbatch.GetBatch,
	ProcessRMS:      devices.ProcessRmsData,
	ToBQ:            edihelper.RmsStatusToRmsData,
	MarshalDevice:   etlShared.ProtoMarshal,
})
