package wrapperResponse

import (
	"context"
	"errors"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
)

func TestHandlerWithDeps(t *testing.T) {
	ctx := context.Background()
	now := time.Now()

	cases := []struct {
		name    string
		connErr error
		recvErr error
		attrErr error
		dlqErr  error
		wantDLQ int
	}{
		{
			name:    "connector error",
			connErr: errors.New("no conn"),
			wantDLQ: 0,
		},
		{
			name:    "receive error",
			recvErr: errors.New("recv fail"),
			wantDLQ: 0,
		},
		{
			name:    "parse attributes error",
			attrErr: errors.New("fail attr parse"),
			wantDLQ: 1,
		},
		{
			name:    "parse attributes error with DLQ error",
			attrErr: errors.New("fail attr parse"),
			dlqErr:  errors.New("dlq failed"),
			wantDLQ: 1,
		},
		{
			name:    "happy path - stub processing",
			wantDLQ: 0,
		},
	}

	for _, tc := range cases {
		tc := tc

		t.Run(tc.name, func(t *testing.T) {
			// --- setup fakes ---
			conns := mocks.FakeConns()
			psc := conns.Pubsub.(*mocks.FakePubsubClient)
			psc.ReceiveError = tc.recvErr

			// create subscription
			topic := psc.Topic("topic-" + tc.name)
			sub, err := psc.CreateSubscription(ctx, "sub-"+tc.name, connect.SubscriptionConfig{Topic: topic})
			if err != nil {
				t.Fatal(err)
			}

			// publish message if connector & receive ok
			if tc.connErr == nil && tc.recvErr == nil {
				msg := &pubsub.Message{
					ID:          "m-" + tc.name,
					Data:        []byte("wrapper response data"),
					PublishTime: now,
					Attributes:  map[string]string{"test": "value"},
				}
				topic.Publish(ctx, msg)
			}

			// collect DLQ calls
			dlqCalls := 0

			// --- build deps ---
			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conns, tc.connErr
				},
				SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, reason string) error {
					dlqCalls++
					return tc.dlqErr
				},
				ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{
							OrganizationIdentifier: "test-org",
							Topic:                  "test-topic",
						}, pubsubdata.HeaderDetails{
							GatewayDeviceID: "test-gateway",
						}, nil
				},
			}

			// --- invoke handler ---
			h := HandlerWithDeps(deps)
			h(ctx, sub)

			// --- asserts ---
			assert.Equal(t, tc.wantDLQ, dlqCalls, "DLQ calls should match expected")
		})
	}
}

func TestHandler_ProductionDependencies(t *testing.T) {
	// Test that the production Handler variable is properly initialized
	assert.NotNil(t, Handler, "Handler should not be nil")

	// We can't easily test the actual execution without setting up real connections,
	// but we can verify the handler function is callable
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	// Create a fake subscription that will timeout quickly
	conns := mocks.FakeConns()
	psc := conns.Pubsub.(*mocks.FakePubsubClient)
	psc.ReceiveError = errors.New("timeout for test")

	topic := psc.Topic("test-topic")
	sub, err := psc.CreateSubscription(ctx, "test-sub", connect.SubscriptionConfig{Topic: topic})
	assert.NoError(t, err)

	// This should not panic and should handle the error gracefully
	assert.NotPanics(t, func() {
		Handler(ctx, sub)
	}, "Handler should not panic when called with production dependencies")
}
