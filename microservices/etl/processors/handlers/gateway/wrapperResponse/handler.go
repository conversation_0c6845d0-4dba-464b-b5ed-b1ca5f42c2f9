package wrapperResponse

import (
	"context"
	"fmt"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc       func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	DLQSender           func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector       ConnectorFunc
	ParseAttributes ParseAttributesFunc
	SendToDLQ       DLQSender
}

func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, sub connect.PsSubscription) {
	return func(ctx context.Context, sub connect.PsSubscription) {
		connections, err := deps.Connector(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			return
		}

		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			// Parse Attributes
			commonAttrs, httpHeader, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// STUB: wrapperResponse handler - no processing implemented yet
			logger.Infof("STUB: wrapperResponse received from gateway %s (org: %s) - acknowledged without processing",
				httpHeader.GatewayDeviceID, commonAttrs.OrganizationIdentifier)

			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription %s: %v", sub.ID(), err)
		}
	}
}

// Handler is the production-ready Pub/Sub processor using real dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:       connect.GetConnections,
	ParseAttributes: pubsubdata.ParseAttributes,
	SendToDLQ:       etlShared.SendToDLQ,
})
