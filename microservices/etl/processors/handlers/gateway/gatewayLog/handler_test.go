package gatewayLog

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"errors"
	"io"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/gateway/v1"
	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
	"synapse-its.com/shared/api/helper"
	"synapse-its.com/shared/api/softwaregateway"
	"synapse-its.com/shared/bqbatch"
	connect "synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// Mock implementations
type mockConnector struct {
	mock.Mock
}

func (m *mockConnector) GetConnections(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
	args := m.Called(ctx, checkConnections)
	return args.Get(0).(*connect.Connections), args.Error(1)
}

type mockPubsubClient struct {
	mock.Mock
	connect.PsClient
}

func (m *mockPubsubClient) Subscription(name string) connect.PsSubscription {
	args := m.Called(name)
	return args.Get(0).(connect.PsSubscription)
}

type mockSubscription struct {
	mock.Mock
	connect.PsSubscription
}

func (m *mockSubscription) Receive(ctx context.Context, f func(context.Context, *pubsub.Message)) error {
	args := m.Called(ctx, f)
	return args.Error(0)
}

// createTestMessage creates a test message with the given log message
func createTestMessage(t *testing.T, logMessage string) *pubsub.Message {
	// Compress the log message
	compressedLog, err := helper.CompressBytes([]byte(logMessage))
	if err != nil {
		t.Fatal(err)
	}

	// Create test gateway logs
	gatewayLogs := &gatewayv1.GatewayLogs{
		Message:     compressedLog,
		MessageTime: timestamppb.New(time.Now()),
	}
	gatewayLogsBytes, err := proto.Marshal(gatewayLogs)
	if err != nil {
		t.Fatal(err)
	}

	return &pubsub.Message{
		ID:   "test-msg-id",
		Data: gatewayLogsBytes,
		Attributes: map[string]string{
			"organizationIdentifier": "test-org",
			"topic":                  "test-topic",
			"deviceType":             "test-device",
		},
		PublishTime: time.Now(),
	}
}

func TestHandlerWithDeps(t *testing.T) {
	ctx := context.Background()

	cases := []struct {
		name          string
		connErr       error
		recvErr       error
		attrErr       error
		unmarshalErr  error
		batchErr      error
		batchAddErr   error
		dlqErr        error
		decompressErr error
		emptyLogs     bool
		wantDLQ       int
		wantBatchAdds int
	}{
		{
			name:     "get batch error",
			batchErr: errors.New("no batch"),
			wantDLQ:  0, wantBatchAdds: 0,
		},
		{
			name:    "connector error",
			connErr: errors.New("no conn"),
			wantDLQ: 0, wantBatchAdds: 0,
		},
		{
			name:    "receive error",
			recvErr: errors.New("recv fail"),
			wantDLQ: 0, wantBatchAdds: 0,
		},
		{
			name:    "parse attributes error",
			attrErr: errors.New("fail attr parse"),
			wantDLQ: 1, wantBatchAdds: 0,
		},
		{
			name:    "parse attributes error and fail to send to DLQ",
			attrErr: errors.New("fail attr parse"),
			dlqErr:  errors.New("dlq failed"),
			wantDLQ: 0, wantBatchAdds: 0,
		},
		{
			name:         "unmarshal protobuf message error",
			unmarshalErr: errors.New("bad proto"),
			wantDLQ:      1, wantBatchAdds: 0,
		},
		{
			name:         "unmarshal protobuf message error and fail to send to DLQ",
			unmarshalErr: errors.New("bad proto"),
			dlqErr:       errors.New("dlq failed"),
			wantDLQ:      0, wantBatchAdds: 0,
		},
		{
			name:          "decompress error",
			decompressErr: errors.New("decompress failed"),
			wantDLQ:       1, wantBatchAdds: 0,
		},
		{
			name:          "decompress error and fail to send to DLQ",
			decompressErr: errors.New("decompress failed"),
			dlqErr:        errors.New("dlq failed"),
			wantDLQ:       0, wantBatchAdds: 0,
		},
		{
			name:      "no log records (ack and return)",
			emptyLogs: true,
			wantDLQ:   0, wantBatchAdds: 0,
		},
		{
			name:        "batch add error",
			batchAddErr: errors.New("batch add failed"),
			wantDLQ:     0, wantBatchAdds: 0,
		},
		{
			name:    "happy path",
			wantDLQ: 0, wantBatchAdds: 1,
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			tc := tc
			// --- setup fakes ---
			conns := mocks.FakeConns()
			psc := conns.Pubsub.(*mocks.FakePubsubClient)
			psc.ReceiveError = tc.recvErr

			// create subscription
			topic := psc.Topic("topic-" + tc.name)
			sub, err := psc.CreateSubscription(ctx, "sub-"+tc.name, connect.SubscriptionConfig{Topic: topic})
			if err != nil {
				t.Fatal(err)
			}

			// publish message if connector & receive ok
			if tc.connErr == nil && tc.recvErr == nil {
				msg := createTestMessage(t, "test log message")
				topic.Publish(ctx, msg)
			}

			// fake batcher
			added := 0
			fakeBatch, _ := mocks.FakeBatch(ctx)
			fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				if tc.batchAddErr != nil {
					return tc.batchAddErr
				}
				added++
				return nil
			}

			// collect DLQ calls
			dlq := 0

			// --- build deps ---
			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conns, tc.connErr
				},
				SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, reason string) error {
					if tc.dlqErr != nil {
						return tc.dlqErr
					}
					dlq++
					return nil
				},
				UnmarshalGatewayLogs: func(raw []byte) (*gatewayv1.GatewayLogs, error) {
					if tc.unmarshalErr != nil {
						return nil, tc.unmarshalErr
					}
					return &gatewayv1.GatewayLogs{
						Message:     raw,
						MessageTime: timestamppb.New(time.Now()),
					}, nil
				},
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return fakeBatch, tc.batchErr
				},
				ToBQ: func(orgID string, sgwID string, tz string, topic string, pubsubID string, pubsubTS time.Time, messageTime time.Time, records []softwaregateway.GatewayLogRecord) []schemas.GatewayLogMessage {
					// Return one row per record for testing
					if len(records) == 0 {
						return []schemas.GatewayLogMessage{}
					}
					return []schemas.GatewayLogMessage{
						{
							OrganizationIdentifier: orgID,
							SoftwareGatewayID:      sgwID,
							MessageTime:            messageTime,
						},
					}
				},
				ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{
							OrganizationIdentifier: "test-org",
							Topic:                  "test-topic",
							DeviceType:             "test-device",
						}, pubsubdata.HeaderDetails{
							GatewayDeviceID: "test-gateway",
							GatewayTimezone: "UTC",
						}, nil
				},
				ParseLogEntries: func(compressedData []byte) ([]softwaregateway.GatewayLogRecord, error) {
					if tc.decompressErr != nil {
						return nil, tc.decompressErr
					}
					if tc.emptyLogs {
						return []softwaregateway.GatewayLogRecord{}, nil
					}
					// Return a test record for happy path testing
					return []softwaregateway.GatewayLogRecord{
						{
							BootID:    "boot-123",
							EventTime: time.Date(2025, 6, 11, 14, 38, 27, 669000000, time.UTC),
							Level:     "info",
							Msg:       "test message",
						},
					}, nil
				},
			}

			// --- invoke handler ---
			h := HandlerWithDeps(deps)
			h(ctx, sub)

			// --- asserts ---
			assert.Equal(t, tc.wantDLQ, dlq, "DLQ calls should match expected")
			assert.Equal(t, tc.wantBatchAdds, added, "batch.Add calls should match expected")
		})
	}
}

func TestParseLogEntriesWith(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name       string
		uncompress UncompressFunc
		provide    func() []byte
		assertFn   func(t *testing.T, got []softwaregateway.GatewayLogRecord)
		wantErr    bool
	}{
		{
			name: "old format - array of JSON objects parsed and preserved",
			uncompress: func(_ []byte, into interface{}) error {
				if arr, ok := into.(*[]json.RawMessage); ok {
					*arr = []json.RawMessage{
						json.RawMessage([]byte(`{"event_time":"2025-06-11T14:38:27Z","level":"info","msg":"m1"}`)),
						json.RawMessage([]byte(`{"event_time":"2025-06-11T14:38:28Z","level":"warn","msg":"m2"}`)),
					}
					return nil
				}
				return errors.New("wrong type")
			},
			provide: func() []byte { return nil },
			assertFn: func(t *testing.T, got []softwaregateway.GatewayLogRecord) {
				assert.Len(t, got, 2)
				assert.Equal(t, "info", got[0].Level)
				assert.NotEmpty(t, got[0].RawJSON)
				assert.Equal(t, "warn", got[1].Level)
				assert.NotEmpty(t, got[1].RawJSON)
			},
		},
		{
			name: "old format - array contains invalid JSON preserved as raw",
			uncompress: func(_ []byte, into interface{}) error {
				if arr, ok := into.(*[]json.RawMessage); ok {
					*arr = []json.RawMessage{
						json.RawMessage([]byte(`not-json`)),
					}
					return nil
				}
				return errors.New("wrong type")
			},
			provide: func() []byte { return nil },
			assertFn: func(t *testing.T, got []softwaregateway.GatewayLogRecord) {
				assert.Len(t, got, 1)
				assert.True(t, got[0].EventTime.IsZero())
				assert.Equal(t, []byte("not-json"), got[0].RawJSON)
			},
		},
		{
			name:       "ndjson gzip - two objects with embedded newlines",
			uncompress: func(_ []byte, _ interface{}) error { return errors.New("force gzip path") },
			provide: func() []byte {
				var buf bytes.Buffer
				gw := gzip.NewWriter(&buf)
				io.WriteString(gw, `{"event_time":"2025-06-11T14:38:27Z","level":"info","msg":"m1","stacktrace":"a\nb"}`)
				io.WriteString(gw, `{"event_time":"2025-06-11T14:38:28Z","level":"error","msg":"m2"}`)
				gw.Close()
				return buf.Bytes()
			},
			assertFn: func(t *testing.T, got []softwaregateway.GatewayLogRecord) {
				assert.Len(t, got, 2)
				assert.Equal(t, "info", got[0].Level)
				assert.Equal(t, "m2", got[1].Msg)
				assert.NotEmpty(t, got[0].RawJSON)
			},
		},
		{
			name:       "ndjson gzip - error at start captures entire remainder as one raw record",
			uncompress: func(_ []byte, _ interface{}) error { return errors.New("force gzip path") },
			provide: func() []byte {
				var buf bytes.Buffer
				gw := gzip.NewWriter(&buf)
				io.WriteString(gw, `bad-start`)
				io.WriteString(gw, `{"level":"info"}`)
				io.WriteString(gw, `not-json-tail`)
				gw.Close()
				return buf.Bytes()
			},
			assertFn: func(t *testing.T, got []softwaregateway.GatewayLogRecord) {
				// Decoder fails before consuming any bytes; we capture the entire stream as one raw record
				assert.Len(t, got, 1)
				assert.Equal(t, []byte(`bad-start{"level":"info"}not-json-tail`), got[0].RawJSON)
			},
		},
		{
			name:       "ndjson gzip - non-object element preserved then object parsed",
			uncompress: func(_ []byte, _ interface{}) error { return errors.New("force gzip path") },
			provide: func() []byte {
				var buf bytes.Buffer
				gw := gzip.NewWriter(&buf)
				io.WriteString(gw, `"not-an-object"`)
				io.WriteString(gw, `{"level":"info","msg":"ok"}`)
				gw.Close()
				return buf.Bytes()
			},
			assertFn: func(t *testing.T, got []softwaregateway.GatewayLogRecord) {
				assert.Len(t, got, 2)
				assert.Equal(t, []byte(`"not-an-object"`), got[0].RawJSON)
				assert.Equal(t, "ok", got[1].Msg)
			},
		},
		{
			name:       "ndjson gzip - valid object then bad chunk and tail captured as single raw",
			uncompress: func(_ []byte, _ interface{}) error { return errors.New("force gzip path") },
			provide: func() []byte {
				var buf bytes.Buffer
				gw := gzip.NewWriter(&buf)
				io.WriteString(gw, `{"level":"info","msg":"first"}`)
				io.WriteString(gw, `BAD-CHUNK`)
				io.WriteString(gw, `{"level":"warn"}TAIL`)
				gw.Close()
				return buf.Bytes()
			},
			assertFn: func(t *testing.T, got []softwaregateway.GatewayLogRecord) {
				assert.Len(t, got, 2)
				assert.Equal(t, "first", got[0].Msg)
				assert.Equal(t, []byte("BAD-CHUNK{"+"\"level\":\"warn\"}"+"TAIL"), got[1].RawJSON)
			},
		},
		{
			name:       "uncompress error (non-gzip and not array)",
			uncompress: func(_ []byte, _ interface{}) error { return errors.New("decompress failed") },
			provide:    func() []byte { return []byte("not-gzip") },
			wantErr:    true,
		},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			var input []byte
			if tc.provide != nil {
				input = tc.provide()
			}
			got, err := parseLogEntriesWithDeps(tc.uncompress, input)
			if tc.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if tc.assertFn != nil {
					tc.assertFn(t, got)
				}
			}
		})
	}
}

// Just for coverage
func Test_ParseLogEntries_Wrapper(t *testing.T) {
	t.Parallel()
	// Provide gzipped NDJSON two records; wrapper should route to gzip path and parse both
	var buf bytes.Buffer
	gw := gzip.NewWriter(&buf)
	io.WriteString(gw, `{"event_time":"2025-06-11T14:38:27Z","level":"info","msg":"a"}`)
	io.WriteString(gw, `{"event_time":"2025-06-11T14:38:28Z","level":"warn","msg":"b"}`)
	gw.Close()

	got, err := parseLogEntries(buf.Bytes())
	assert.NoError(t, err)
	assert.Len(t, got, 2)
}
