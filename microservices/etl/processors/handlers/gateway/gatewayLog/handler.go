package gatewayLog

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/gateway/v1"
	"cloud.google.com/go/pubsub"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/etl/processors/handlers/gateway/helper"
	"synapse-its.com/shared/api/softwaregateway"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc            func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc      func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	DLQSender                func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
	UnmarshalGatewayLogsFunc func(raw []byte) (*gatewayv1.GatewayLogs, error)
	BatchGetter              func(ctx context.Context) (bqbatch.Batcher, error)
	ToBQConverter            func(orgID string, sgwID string, tz string, topic string, pubsubID string, pubsubTS time.Time, messageTime time.Time, records []softwaregateway.GatewayLogRecord) []schemas.GatewayLogMessage
	ParseLogEntries          func(compressedData []byte) ([]softwaregateway.GatewayLogRecord, error)
	UncompressFunc           func([]byte, interface{}) error
	UncompressGzipFunc       func([]byte, interface{}) error
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector            ConnectorFunc
	ParseAttributes      ParseAttributesFunc
	SendToDLQ            DLQSender
	UnmarshalGatewayLogs UnmarshalGatewayLogsFunc
	GetBatch             BatchGetter
	ToBQ                 ToBQConverter
	ParseLogEntries      ParseLogEntries
	UnmarshalGzip        UncompressGzipFunc
}

func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, sub connect.PsSubscription) {
	return func(ctx context.Context, sub connect.PsSubscription) {
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}
		connections, err := deps.Connector(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			return
		}
		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s MessageID: %s. Message Data:%s", sub.ID(), string(msg.ID), string(msg.Data))

			// Parse Attributes
			commonAttrs, httpHeader, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Unmarshall protobuf message
			gatewayLogs, errUm := deps.UnmarshalGatewayLogs(msg.Data)
			if errUm != nil {
				logger.Errorf("Error Unmarshaling protobuf message: %v", errUm)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Error Unmarshaling the Gateway Logs: %v", errUm))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Parse the compressed gateway log records
			logRecords, err := deps.ParseLogEntries(gatewayLogs.GetMessage())
			if err != nil {
				logger.Errorf("Error parsing the Gateway Logs: %v", err)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Error parsing the Gateway Logs: %v", err))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// If no log records were parsed, just ack and return
			if len(logRecords) == 0 {
				msg.Ack()
				return
			}

			// Convert to BigQuery rows (one row per log entry for flattened structure)
			bqItems := deps.ToBQ(
				commonAttrs.OrganizationIdentifier,
				httpHeader.GatewayDeviceID,
				httpHeader.GatewayTimezone,
				commonAttrs.Topic,
				msg.ID,
				msg.PublishTime.UTC(),
				gatewayLogs.GetMessageTime().AsTime(),
				logRecords,
			)

			// Add each flattened log entry row to the batch
			for _, bqItem := range bqItems {
				if err = batch.Add(bqItem); err != nil {
					logger.Errorf("Error adding message to batch: %v", err)
					msg.Nack()
					return
				}
			}

			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription %s: %v", sub.ID(), err)
		}
	}
}

// Handler is the production-ready Pub/Sub processor using real dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:            connect.GetConnections,
	ParseAttributes:      pubsubdata.ParseAttributes,
	SendToDLQ:            etlShared.SendToDLQ,
	UnmarshalGatewayLogs: etlShared.UnmarshalGatewayLogs,
	GetBatch:             bqbatch.GetBatch,
	ToBQ:                 edihelper.GatewayLogToBQ,
	ParseLogEntries:      parseLogEntries,
	UnmarshalGzip:        helper.UnmarshalGzipBytes,
})

func parseLogEntriesWithDeps(
	uncompress UncompressFunc,
	compressed []byte,
) ([]softwaregateway.GatewayLogRecord, error) {
	// Check if it's the old format (array of JSON strings or JSON objects)
	// If so, capture each element as a record; attempt to unmarshal, but always preserve raw bytes
	var arrRaw []json.RawMessage
	if err := uncompress(compressed, &arrRaw); err == nil && len(arrRaw) > 0 {
		recs := make([]softwaregateway.GatewayLogRecord, 0, len(arrRaw))
		for _, elem := range arrRaw {
			var rec softwaregateway.GatewayLogRecord
			if uerr := json.Unmarshal(elem, &rec); uerr == nil {
				rec.RawJSON = elem
			} else {
				rec.RawJSON = elem
			}
			recs = append(recs, rec)
		}
		return recs, nil
	}

	// Now try to parse it in the new format, close to final BQ format
	// Decompress raw bytes first using gzip-only for gateway logs
	raw, derr := helper.GzipDecompressBytes(compressed)
	if derr != nil {
		return nil, fmt.Errorf("unrecognized gateway log format: gzip-decompress failed: %v", derr)
	}

	// Stream-decode concatenated JSON objects robustly (handles newlines inside strings)
	rdr := bytes.NewReader(raw)
	dec := json.NewDecoder(rdr)
	records := make([]softwaregateway.GatewayLogRecord, 0, 64)
	for {
		var rawObj json.RawMessage
		if err := dec.Decode(&rawObj); err != nil {
			if err == io.EOF {
				break
			}
			// On parse error, capture bytes since last successful decode up to the error offset,
			// then capture any remaining bytes, so 100% of payload is preserved.
			off := dec.InputOffset()
			if off < int64(len(raw)) {
				rest := raw[off:]
				records = append(records, softwaregateway.GatewayLogRecord{RawJSON: append([]byte(nil), rest...)})
			}
			break
		}
		var parsed softwaregateway.GatewayLogRecord
		if err := json.Unmarshal(rawObj, &parsed); err != nil {
			// Preserve this raw object even if it cannot be unmarshaled
			records = append(records, softwaregateway.GatewayLogRecord{RawJSON: rawObj})
			continue
		}
		parsed.RawJSON = rawObj
		records = append(records, parsed)
	}

	return records, nil
}

func parseLogEntries(data []byte) ([]softwaregateway.GatewayLogRecord, error) {
	return parseLogEntriesWithDeps(helper.UnmarshalCompressedBytes, data)
}
