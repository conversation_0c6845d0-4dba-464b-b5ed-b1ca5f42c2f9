package faultLogs

import (
	"context"
	"fmt"
	"strings"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/gateway/v1"
	"cloud.google.com/go/pubsub"
	"github.com/google/uuid"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/devices"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc                             func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc                       func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	DLQSender                                 func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
	UnmarshalDeviceLogsFunc                   func(raw []byte) (*gatewayv1.DeviceLogs, error)
	BatchGetter                               func(ctx context.Context) (bqbatch.Batcher, error)
	MarshalDeviceLogsFunc                     func(msg proto.Message) ([]byte, error)
	InitLogMonitorResetBQConverterFunc        func(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.LogMonitorResetTranslater, logUUID string, deviceID string) edihelper.BQConverter
	InitLogPreviousFailBQConverterFunc        func(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.LogPreviousFailTranslater, logUUID string, deviceID string) edihelper.BQConverter
	InitLogACLineEventBQConverterFunc         func(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.LogACLineEventTranslater, logUUID string, deviceID string) edihelper.BQConverter
	InitLogFaultSignalSequenceBQConverterFunc func(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.FaultSignalSequenceTranslater, existingSeq *schemas.LogFaultSignalSequence, logUUID string, deviceID string) edihelper.BQConverter
	InitLogConfigurationBQConverterFunc       func(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.ConfigurationChangeLogTranslater, logUUID string, deviceID string) edihelper.BQConverter
	UpsertDeviceLogFunc                       func(pg connect.DatabaseExecutor, deviceID string, logId string) error
)

type bqItem struct {
	Data any
	Err  error
}

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector                                    ConnectorFunc
	ParseAttributes                              ParseAttributesFunc
	SendToDLQ                                    DLQSender
	UnmarshalLogs                                UnmarshalDeviceLogsFunc
	GetBatch                                     BatchGetter
	LogMonitorResetBQConverterConstructor        InitLogMonitorResetBQConverterFunc
	LogPreviousFailBQConverterConstructor        InitLogPreviousFailBQConverterFunc
	LogACLineEventBQConverterConstructor         InitLogACLineEventBQConverterFunc
	LogFaultSignalSequenceBQConverterConstructor InitLogFaultSignalSequenceBQConverterFunc
	LogConfigurationBQConverterConstructor       InitLogConfigurationBQConverterFunc
	UpsertDeviceLog                              UpsertDeviceLogFunc
}

// HandlerWithDeps constructs a Pub/Sub processing function with injected dependencies.
func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, sub connect.PsSubscription) {
	return func(ctx context.Context, sub connect.PsSubscription) {
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}
		connections, err := deps.Connector(ctx)
		if err != nil {
			logger.Errorf("Error getting connections%v", err)
			return
		}
		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s MessageID: %s. Message Data:%s", sub.ID(), string(msg.ID), string(msg.Data))

			// Parse Attributes
			commonAttrs, httpHeader, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Unmarshal device logs
			deviceLogs, errUm := deps.UnmarshalLogs(msg.Data)
			if errUm != nil {
				logger.Errorf("Error Unmarshaling the Device Logs: %v", errUm)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Error Unmarshaling the Device Logs: %v", errUm))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Generate a log UUID that can be used to link all of the logs together
			logUUID := uuid.New().String()

			// Create a new FaultLogs struct
			faultLogs := &schemas.FaultLogs{
				OrganizationIdentifier: commonAttrs.OrganizationIdentifier,
				SoftwareGatewayID:      httpHeader.GatewayDeviceID,
				TZ:                     httpHeader.GatewayTimezone,
				Topic:                  commonAttrs.Topic,
				PubsubTimestamp:        msg.PublishTime.UTC(),
				PubsubID:               msg.ID,
				DeviceID:               deviceLogs.GetDeviceId(),
				RawLogMessages: schemas.RawLogMessages{
					LogMonitorReset:        make([][]byte, 0),
					LogPreviousFail:        make([][]byte, 0),
					LogACLineEvent:         make([][]byte, 0),
					LogFaultSignalSequence: make([][]byte, 0),
					LogConfiguration:       make([][]byte, 0),
				},
				LogUUID: logUUID,
			}

			// Process each log entry and add the individual log items to the batch
			individualLogBQItems := make([]bqItem, 0)
			for _, logEntry := range deviceLogs.GetLogs() {
				createIndividualLogBQItems := func(rawLogMessages [][]byte, converter edihelper.BQConverter) []bqItem {
					returnedItems := make([]bqItem, 0)
					numberOfRawLogMessages := len(rawLogMessages)
					for idx, rawLogMessage := range rawLogMessages {
						if len(rawLogMessage) == 0 {
							logger.Infof("[%d/%d] Skipping empty log message for %s log entry device id: %s", idx+1, numberOfRawLogMessages, logEntry.GetLogType(), deviceLogs.GetDeviceId())
							continue
						}
						convertedItem, converterErr := converter(rawLogMessage)
						if converterErr != nil {
							returnedItems = append(
								returnedItems,
								bqItem{Data: convertedItem, Err: fmt.Errorf("[%d/%d] error converting log message for log entry %s: %v", idx+1, numberOfRawLogMessages, logEntry.GetLogType(), converterErr)})
						} else {
							returnedItems = append(returnedItems, bqItem{Data: convertedItem, Err: nil})
						}
					}
					return returnedItems
				}
				// Add the message to the appropriate RawLogMessages field
				switch logEntry.GetLogType() {
				case "MonitorReset":
					faultLogs.RawLogMessages.LogMonitorReset = append(faultLogs.RawLogMessages.LogMonitorReset, logEntry.GetMessage()...)
					individualLogBQItems = append(individualLogBQItems, createIndividualLogBQItems(faultLogs.RawLogMessages.LogMonitorReset, deps.LogMonitorResetBQConverterConstructor(&httpHeader, &commonAttrs, msg, devices.ProcessLogMonitorReset, logUUID, deviceLogs.GetDeviceId()))...)
				case "PreviousFail":
					faultLogs.RawLogMessages.LogPreviousFail = append(faultLogs.RawLogMessages.LogPreviousFail, logEntry.GetMessage()...)
					individualLogBQItems = append(individualLogBQItems, createIndividualLogBQItems(faultLogs.RawLogMessages.LogPreviousFail, deps.LogPreviousFailBQConverterConstructor(&httpHeader, &commonAttrs, msg, devices.ProcessLogPreviousFail, logUUID, deviceLogs.GetDeviceId()))...)
				case "ACLine":
					faultLogs.RawLogMessages.LogACLineEvent = append(faultLogs.RawLogMessages.LogACLineEvent, logEntry.GetMessage()...)
					individualLogBQItems = append(individualLogBQItems, createIndividualLogBQItems(faultLogs.RawLogMessages.LogACLineEvent, deps.LogACLineEventBQConverterConstructor(&httpHeader, &commonAttrs, msg, devices.ProcessLogACLineEvent, logUUID, deviceLogs.GetDeviceId()))...)
				case "FaultSignalSequence":
					faultLogs.RawLogMessages.LogFaultSignalSequence = append(faultLogs.RawLogMessages.LogFaultSignalSequence, logEntry.GetMessage()...)
					existingSeq := schemas.LogFaultSignalSequence{}
					individualLogBQItems = append(individualLogBQItems, createIndividualLogBQItems(faultLogs.RawLogMessages.LogFaultSignalSequence, deps.LogFaultSignalSequenceBQConverterConstructor(&httpHeader, &commonAttrs, msg, devices.ProcessLogFaultSignalSequence, &existingSeq, logUUID, deviceLogs.GetDeviceId()))...)
					individualLogBQItems = append(individualLogBQItems, bqItem{Data: existingSeq, Err: func() error {
						if existingSeq.OrganizationIdentifier == "" {
							return fmt.Errorf("organization identifier is empty")
						}
						return nil
					}()})
				case "Configuration":
					faultLogs.RawLogMessages.LogConfiguration = append(faultLogs.RawLogMessages.LogConfiguration, logEntry.GetMessage()...)
					individualLogBQItems = append(individualLogBQItems, createIndividualLogBQItems(faultLogs.RawLogMessages.LogConfiguration, deps.LogConfigurationBQConverterConstructor(&httpHeader, &commonAttrs, msg, devices.ProcessLogConfiguration, logUUID, deviceLogs.GetDeviceId()))...)
				default:
					logger.Warnf("Unknown log entry message type: %s", logEntry.GetLogType())
				}
			}
			// Add the top-level faultLogs to the batch
			individualLogBQItems = append(individualLogBQItems, bqItem{Data: faultLogs, Err: nil})

			// Process items in a single loop - collect errors and valid items
			var errorItems []error
			var validItems []any
			for _, item := range individualLogBQItems {
				if item.Err != nil {
					errorItems = append(errorItems, item.Err)
				} else {
					validItems = append(validItems, item.Data)
				}
			}

			// If there are errors, send to DLQ once and continue processing valid items
			if len(errorItems) > 0 {
				var errorMessages []string
				for i, err := range errorItems {
					errorMessages = append(errorMessages, fmt.Sprintf("Error %d: %v", i+1, err))
				}
				errMsg := fmt.Sprintf("Found %d error items: %s", len(errorItems), strings.Join(errorMessages, "; "))
				logger.Error(errMsg)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, errMsg)
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
			}

			// Upsert the device log
			if err = deps.UpsertDeviceLog(connections.Postgres, deviceLogs.GetDeviceId(), logUUID); err != nil {
				errMsg := fmt.Sprintf("Error upserting device log to postgres: %v", err)
				logger.Error(errMsg)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, errMsg)
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
			}

			// Add all valid items to batch
			for _, itemData := range validItems {
				if err = batch.Add(itemData); err != nil {
					errMsg := fmt.Sprintf("Error adding to batch: %v", err)
					logger.Error(errMsg)
					err = deps.SendToDLQ(ctx, connections.Pubsub, msg, errMsg)
					if err != nil {
						logger.Errorf("Error sending message to the DLQ topic %v", err)
						msg.Nack()
						return
					}
				}
			}

			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription %s: %v", sub.ID(), err)
		}
	}
}

// Handler is the production-ready Pub/Sub processor using real dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:                             connect.GetConnections,
	ParseAttributes:                       pubsubdata.ParseAttributes,
	SendToDLQ:                             etlShared.SendToDLQ,
	UnmarshalLogs:                         etlShared.UnmarshalDeviceLogs,
	GetBatch:                              bqbatch.GetBatch,
	LogMonitorResetBQConverterConstructor: edihelper.NewLogMonitorResetBQConverter,
	LogPreviousFailBQConverterConstructor: edihelper.NewLogPreviousFailBQConverter,
	LogACLineEventBQConverterConstructor:  edihelper.NewLogACLineEventBQConverter,
	LogFaultSignalSequenceBQConverterConstructor: edihelper.NewFaultSignalSequenceBQConverter,
	LogConfigurationBQConverterConstructor:       edihelper.NewConfigurationChangeLogBQConverter,
	UpsertDeviceLog:                              upsertDeviceLog,
})

// upsertDeviceLog inserts or updates a DeviceLog row in one statement.
func upsertDeviceLog(pg connect.DatabaseExecutor, deviceID string, logId string) error {
	const query = `
			INSERT INTO {{DeviceLog}} (
				DeviceId,
				DateUploaded,
				LogId
			)
			VALUES (
				$1, $2, $3
			)
			ON CONFLICT (DeviceId) DO UPDATE SET
				DateUploaded = EXCLUDED.DateUploaded,
				LogId        = EXCLUDED.LogId;
		`

	// Retry exec in case of deadlock
	return connect.WithDeadlockRetry(func() error {
		_, err := pg.Exec(query,
			deviceID,
			time.Now(),
			logId)
		return err
	})
}
