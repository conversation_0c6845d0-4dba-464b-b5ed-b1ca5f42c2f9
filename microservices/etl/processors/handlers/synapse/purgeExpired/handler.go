package purgeExpired

import (
	"context"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// handlerDeps is the dependencies for the handler
type handlerDeps struct {
	connector     func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	createService func(connections *connect.Connections) Service
}

// createHandler creates a handler with the given dependencies
func createHandler(deps handlerDeps) func(ctx context.Context, sub connect.PsSubscription) {
	return func(ctx context.Context, sub connect.PsSubscription) {
		connections, err := deps.connector(ctx)
		if err != nil {
			logger.Errorf("Error getting connections%v", err)
			return
		}
		svc := deps.createService(connections)
		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s with ID: %s, message: %+v", sub.ID(), string(msg.ID), msg)
			if err = svc.PurgeExpiredData(ctx); err != nil {
				logger.Errorf("Failed to purge expired data: %v", err)
				msg.Nack()
				return
			}
			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription %s: %v", sub.ID(), err)
		}
	}
}

// createService injects dependencies into the service
func createService(connections *connect.Connections) Service {
	return &service{
		persistence: &repository{
			db: connections.Postgres,
		},
		cache: &cacheRepository{
			redis: connections.Redis,
		},
		firestoreClient: connections.Firestore,
	}
}

// Handler is the handler with dependencies for production
var Handler = createHandler(handlerDeps{
	connector:     connect.GetConnections,
	createService: createService,
})
