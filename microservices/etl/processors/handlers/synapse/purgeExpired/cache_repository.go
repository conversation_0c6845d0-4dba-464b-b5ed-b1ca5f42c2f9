package purgeExpired

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/redis/go-redis/v9"
	apiShared "synapse-its.com/shared/api"
	"synapse-its.com/shared/logger"
)

const redisKeyPrefix = "GatewayRMSData"

type CacheRepository interface {
	GetGatewayRMSDataKeys(ctx context.Context) ([]string, error)
	GetAllGatewayRMSData(ctx context.Context, keys []string) ([]RedisGatewayRMSData, error)
	DeleteInvalidRMSData(ctx context.Context, keys []string) (int64, error)
}

type cacheRepository struct {
	redis *redis.Client
}

func (r *cacheRepository) GetGatewayRMSDataKeys(ctx context.Context) ([]string, error) {
	var cursor uint64
	var allKeys []string
	for {
		var err error
		match := fmt.Sprintf("%s:*", redisKeyPrefix)
		var keys []string

		keys, cursor, err = r.redis.Scan(ctx, cursor, match, 100).Result()
		if err != nil {
			panic(err)
		}

		allKeys = append(allKeys, keys...)

		if cursor == 0 {
			break
		}
	}
	return allKeys, nil
}

func (r *cacheRepository) GetAllGatewayRMSData(ctx context.Context, keys []string) ([]RedisGatewayRMSData, error) {
	if len(keys) == 0 {
		return []RedisGatewayRMSData{}, nil
	}
	raws, err := r.redis.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, fmt.Errorf("error getting gateway RMS raw data: %w", err)
	}

	var multiGatewayRMSData []RedisGatewayRMSData
	for idx, data := range raws {
		// no data for gateway
		if data == nil {
			logger.Warnf("no info for redis key: %s", keys[idx])
			multiGatewayRMSData = append(multiGatewayRMSData, nil)
			continue
		}
		// assert that it's a string
		str, ok := data.(string)
		if !ok {
			logger.Errorf("unable to convert redis value to string for key %s: %s got type %v", keys[idx], data, fmt.Sprintf("%T", data))
			multiGatewayRMSData = append(multiGatewayRMSData, nil)
			continue
		}
		var blob apiShared.RedisData
		err = json.Unmarshal([]byte(str), &blob)
		if err != nil {
			logger.Errorf("error unmarshalling redis value for key %s: %s got type %v", keys[idx], data, fmt.Sprintf("%T", data))
			multiGatewayRMSData = append(multiGatewayRMSData, nil)
			continue
		}
		multiGatewayRMSData = append(multiGatewayRMSData, &blob)
	}
	return multiGatewayRMSData, nil
}

func (r *cacheRepository) DeleteInvalidRMSData(ctx context.Context, keys []string) (int64, error) {
	if len(keys) == 0 {
		return 0, nil
	}
	return r.redis.Del(ctx, keys...).Result()
}
