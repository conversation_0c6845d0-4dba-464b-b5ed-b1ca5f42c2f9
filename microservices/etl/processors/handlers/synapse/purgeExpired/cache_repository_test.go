package purgeExpired

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	apiShared "synapse-its.com/shared/api"
)

func TestCacheRepository_GetGatewayRMSDataKeys(t *testing.T) {
	tests := []struct {
		name          string
		setupMock     func(redismock.ClientMock)
		expectedKeys  []string
		expectedError bool
	}{
		{
			name: "successful scan with multiple pages",
			setupMock: func(mock redismock.ClientMock) {
				// First call returns some keys and a cursor
				mock.ExpectScan(0, "GatewayRMSData:*", int64(100)).SetVal([]string{"GatewayRMSData:key1", "GatewayRMSData:key2"}, 123)
				// Second call returns remaining keys and cursor 0
				mock.ExpectScan(123, "GatewayRMSData:*", int64(100)).SetVal([]string{"GatewayRMSData:key3"}, 0)
			},
			expectedKeys:  []string{"GatewayRMSData:key1", "GatewayRMSData:key2", "GatewayRMSData:key3"},
			expectedError: false,
		},
		{
			name: "successful scan with single page",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectScan(0, "GatewayRMSData:*", int64(100)).SetVal([]string{"GatewayRMSData:key1"}, 0)
			},
			expectedKeys:  []string{"GatewayRMSData:key1"},
			expectedError: false,
		},
		{
			name: "scan error",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectScan(0, "GatewayRMSData:*", int64(100)).SetErr(errors.New("redis scan error"))
			},
			expectedKeys:  nil,
			expectedError: true, // This will panic, so we expect it to fail
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			redisClient, mock := redismock.NewClientMock()
			tt.setupMock(mock)

			repo := &cacheRepository{redis: redisClient}

			if tt.expectedError {
				assert.Panics(t, func() {
					repo.GetGatewayRMSDataKeys(context.Background())
				})
				return
			}

			keys, err := repo.GetGatewayRMSDataKeys(context.Background())
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedKeys, keys)
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestCacheRepository_GetAllGatewayRMSData(t *testing.T) {
	validRedisData := apiShared.RedisData{
		MsgVersion:      "1.0",
		MsgData:         "test-data",
		MsgTimestamp:    "2023-01-01T00:00:00Z",
		GatewayTimezone: "UTC",
	}
	validJSON, _ := json.Marshal(validRedisData)

	tests := []struct {
		name           string
		keys           []string
		setupMock      func(redismock.ClientMock)
		expectedResult []RedisGatewayRMSData
		expectedError  bool
	}{
		{
			name: "successful retrieval with valid data",
			keys: []string{"GatewayRMSData:key1", "GatewayRMSData:key2"},
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectMGet("GatewayRMSData:key1", "GatewayRMSData:key2").SetVal([]interface{}{string(validJSON), string(validJSON)})
			},
			expectedResult: []RedisGatewayRMSData{&validRedisData, &validRedisData},
			expectedError:  false,
		},
		{
			name: "mget error",
			keys: []string{"GatewayRMSData:key1"},
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectMGet("GatewayRMSData:key1").SetErr(errors.New("redis mget error"))
			},
			expectedResult: nil,
			expectedError:  true,
		},
		{
			name: "nil data for some keys",
			keys: []string{"GatewayRMSData:key1", "GatewayRMSData:key2", "GatewayRMSData:key3"},
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectMGet("GatewayRMSData:key1", "GatewayRMSData:key2", "GatewayRMSData:key3").SetVal([]interface{}{string(validJSON), nil, string(validJSON)})
			},
			expectedResult: []RedisGatewayRMSData{&validRedisData, nil, &validRedisData},
			expectedError:  false,
		},
		{
			name: "non-string data type",
			keys: []string{"GatewayRMSData:key1", "GatewayRMSData:key2"},
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectMGet("GatewayRMSData:key1", "GatewayRMSData:key2").SetVal([]interface{}{123, string(validJSON)})
			},
			expectedResult: []RedisGatewayRMSData{nil, &validRedisData},
			expectedError:  false,
		},
		{
			name: "invalid JSON data",
			keys: []string{"GatewayRMSData:key1"},
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectMGet("GatewayRMSData:key1").SetVal([]interface{}{"invalid-json"})
			},
			expectedResult: []RedisGatewayRMSData{nil},
			expectedError:  false,
		},
		{
			name: "empty keys slice",
			keys: []string{},
			setupMock: func(mock redismock.ClientMock) {
				// No expectations needed for empty keys
			},
			expectedResult: []RedisGatewayRMSData{},
			expectedError:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			redisClient, mock := redismock.NewClientMock()
			tt.setupMock(mock)

			repo := &cacheRepository{redis: redisClient}

			result, err := repo.GetAllGatewayRMSData(context.Background(), tt.keys)

			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, result)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, len(tt.expectedResult), len(result))

			for i, expected := range tt.expectedResult {
				if expected == nil {
					assert.Nil(t, result[i])
				} else {
					assert.NotNil(t, result[i])
					assert.Equal(t, expected.MsgVersion, result[i].MsgVersion)
					assert.Equal(t, expected.MsgData, result[i].MsgData)
					assert.Equal(t, expected.MsgTimestamp, result[i].MsgTimestamp)
					assert.Equal(t, expected.GatewayTimezone, result[i].GatewayTimezone)
				}
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestCacheRepository_DeleteInvalidRMSData(t *testing.T) {
	tests := []struct {
		name          string
		keys          []string
		setupMock     func(redismock.ClientMock)
		expectedCount int64
		expectedError bool
	}{
		{
			name: "successful deletion",
			keys: []string{"GatewayRMSData:key1", "GatewayRMSData:key2"},
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectDel("GatewayRMSData:key1", "GatewayRMSData:key2").SetVal(2)
			},
			expectedCount: 2,
			expectedError: false,
		},
		{
			name: "del error",
			keys: []string{"GatewayRMSData:key1"},
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectDel("GatewayRMSData:key1").SetErr(errors.New("redis del error"))
			},
			expectedCount: 0,
			expectedError: true,
		},
		{
			name: "empty keys slice",
			keys: []string{},
			setupMock: func(mock redismock.ClientMock) {
				// No expectations needed for empty keys
			},
			expectedCount: 0,
			expectedError: false,
		},
		{
			name: "partial deletion",
			keys: []string{"GatewayRMSData:key1", "GatewayRMSData:key2", "GatewayRMSData:key3"},
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectDel("GatewayRMSData:key1", "GatewayRMSData:key2", "GatewayRMSData:key3").SetVal(2) // Only 2 keys were actually deleted
			},
			expectedCount: 2,
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			redisClient, mock := redismock.NewClientMock()
			tt.setupMock(mock)

			repo := &cacheRepository{redis: redisClient}

			count, err := repo.DeleteInvalidRMSData(context.Background(), tt.keys)

			if tt.expectedError {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedCount, count)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, tt.expectedCount, count)
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// TestCacheRepository_Interface ensures the cacheRepository implements CacheRepository interface
func TestCacheRepository_Interface(t *testing.T) {
	var _ CacheRepository = (*cacheRepository)(nil)
}
