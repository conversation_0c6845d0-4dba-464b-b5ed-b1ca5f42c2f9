package purgeExpired

import (
	"context"
	"encoding/base64"
	"fmt"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/gateway/v1"
	"google.golang.org/api/iterator"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

const firestoreStateDocument = "_state"

type Service interface {
	PurgeExpiredData(ctx context.Context) error
}

type service struct {
	persistence     PersistenceRepository
	cache           CacheRepository
	firestoreClient connect.FirestoreClientInterface
}

func (s *service) PurgeExpiredData(ctx context.Context) error {
	logger.Infof("Purging expired data")

	// Delete unused devices on firestore
	if err := s.deleteUnusedDevicesOnFirestore(ctx); err != nil {
		logger.Errorf("Error deleting unused devices on firestore: %v", err)
		return err
	} else {
		logger.Infof("Unused devices deleted successfully")
	}

	// Remove expired tokens
	if removed, err := s.persistence.RemoveExpiredTokens(); err != nil {
		logger.Errorf("Error removing expired tokens: %v", err)
		return err
	} else {
		logger.Infof("Removed %d expired tokens", removed)
	}

	// Remove processed instructions
	if removed, err := s.persistence.RemoveProcessedInstructions(); err != nil {
		logger.Errorf("Error removing processed instructions: %v", err)
		return err
	} else {
		logger.Infof("Removed %d processed instructions", removed)
	}

	// Update non-reporting device states to error
	if updated, err := s.updateNonReportingDeviceStatesToError(ctx); err != nil {
		logger.Errorf("Error updating non-reporting device states to error: %v", err)
		return err
	} else {
		logger.Infof("Updated %d non-reporting device states to error", updated)
	}

	logger.Infof("Expired data purged successfully")
	return nil
}

func (s *service) updateNonReportingDeviceStatesToError(ctx context.Context) (int64, error) {
	// Get all gateway RMS data keys
	keys, err := s.cache.GetGatewayRMSDataKeys(ctx)
	if err != nil {
		return 0, fmt.Errorf("error getting gateway RMS data keys: %w", err)
	}

	// Get all gateway RMS data
	multiGatewayRMSData, err := s.cache.GetAllGatewayRMSData(ctx, keys)
	if err != nil {
		return 0, fmt.Errorf("error getting gateway RMS raw data: %w", err)
	}

	// Get invalid RMS data
	keysToDelete := []string{}
	for idx, blob := range multiGatewayRMSData {
		if blob == nil {
			logger.Warnf("no info for redis key: %s", keys[idx])
			continue
		}
		ingestedTimestamp, err := time.Parse(time.RFC3339, blob.MsgTimestamp)
		if err != nil {
			logger.Errorf("error parsing ingested timestamp for key %s: %v", keys[idx], err)
			continue
		}

		if ingestedTimestamp.Before(time.Now().UTC().Add(-time.Hour)) {
			keysToDelete = append(keysToDelete, keys[idx])
		}
	}

	// Delete invalid RMS data
	return s.cache.DeleteInvalidRMSData(ctx, keysToDelete)
}

func (s *service) deleteUnusedDevicesOnFirestore(ctx context.Context) error {
	// Get all devices and states for all organizations in a single query
	allOrganizationDevices, redisKeys, err := s.persistence.GetAllDevicesAndKeys()
	if err != nil {
		logger.Errorf("Error getting all devices and states: %v", err)
		return err
	}

	// Get reported devices from redis
	reportedDevices := make(DeviceSet)
	multiGatewayRMSData, err := s.cache.GetAllGatewayRMSData(ctx, redisKeys)
	if err != nil {
		return fmt.Errorf("error getting gateway RMS raw data: %w", err)
	}
	for idx, gatewayRMSData := range multiGatewayRMSData {
		if gatewayRMSData == nil {
			continue
		}
		base64DecodedData, err := base64.StdEncoding.DecodeString(gatewayRMSData.MsgData)
		if err != nil {
			logger.Errorf("error decoding base64 encoded data for key %s: %v", redisKeys[idx], err)
			continue
		}
		msg := &gatewayv1.DeviceData{}
		if err = proto.Unmarshal(base64DecodedData, msg); err != nil {
			logger.Errorf("%v: could not proto unmarshal redis message, %v", err, base64DecodedData)
			continue
		}
		for _, d := range msg.GetMessages() {
			reportedDevices[d.DeviceId] = nil
		}
	}

	// Process each organization
	for orgId, persistedDevices := range allOrganizationDevices {
		if err := s.updateDeviceStateOnFirestore(ctx, orgId, persistedDevices, reportedDevices); err != nil {
			logger.Errorf("Error syncing organization %s with firestore: %v", orgId, err)
			return fmt.Errorf("error syncing organization %s with firestore: %w", orgId, err)
		}
		if err := s.deleteNonExistentDevicesOnFirestore(ctx, orgId, persistedDevices); err != nil {
			logger.Errorf("Error deleting non-existent devices from firestore: %v", err)
			return fmt.Errorf("error deleting non-existent devices from firestore: %w", err)
		}
	}

	return nil
}

func (s *service) updateDeviceStateOnFirestore(ctx context.Context, orgId string, persistedDevices DeviceSet, reportedDevices DeviceSet) error {
	// Get state document reference from Firestore
	stateDocRef := s.firestoreClient.Collection(orgId).Doc(firestoreStateDocument)
	snapshot, err := stateDocRef.Get(ctx)
	if err != nil {
		return fmt.Errorf("error getting state document for organization %s: %w", orgId, err)
	}

	// Get current devices from Firestore
	var currentDevices DeviceSet
	if snapshot.Exists() {
		if err = snapshot.DataTo(&currentDevices); err != nil {
			return fmt.Errorf("error unmarshalling state document for organization %s: %w", orgId, err)
		}
		// Update device states
		modified := false
		for id, device := range currentDevices {
			_, persisted := persistedDevices[id]
			_, reported := reportedDevices[id]
			if persisted && !reported {
				device.Status = "error"
				modified = true
			} else if !persisted {
				delete(currentDevices, id)
				modified = true
			}
		}
		if modified {
			_, err = stateDocRef.Set(ctx, currentDevices)
			if err != nil {
				return fmt.Errorf("error setting state document for organization %s: %w", orgId, err)
			}
		}
	}
	return nil
}

func (s *service) deleteNonExistentDevicesOnFirestore(ctx context.Context, orgId string, persistedDevices DeviceSet) error {
	currentFirestoreDocList, err := s.getAllDocumentIDsInCollection(ctx, orgId)
	if err != nil {
		return fmt.Errorf("error getting realtime documents from firestore: %w", err)
	}

	batch := s.firestoreClient.BulkWriter(ctx)
	numberOfDevicesBatched := 0
	for _, doc := range currentFirestoreDocList {
		if doc != firestoreStateDocument {
			_, exists := persistedDevices[doc]
			if !exists {
				// the device was removed from the device list
				docRef := s.firestoreClient.Collection(orgId).Doc(doc)
				if err := batch.Delete(docRef); err != nil {
					return fmt.Errorf("error deleting device from firestore: %w", err)
				}
				numberOfDevicesBatched++
			}
			// flush the batch - limit 500 per batch
			if numberOfDevicesBatched > 498 {
				if err := batch.Flush(); err != nil {
					return fmt.Errorf("error flushing batch: %w", err)
				}
				numberOfDevicesBatched = 0
			}
		}
	}
	if numberOfDevicesBatched > 0 {
		if err := batch.Flush(); err != nil {
			return fmt.Errorf("error flushing final batch: %w", err)
		}
	}

	return nil
}

func (s *service) getAllDocumentIDsInCollection(ctx context.Context, collectionName string) ([]string, error) {
	var docIDs []string
	iter := s.firestoreClient.Collection(collectionName).Documents(ctx)
	defer iter.Stop()

	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break // Exit the loop when all documents have been processed
		}
		if err != nil {
			return nil, err // Return the error to handle it in the caller
		}
		docIDs = append(docIDs, doc.ID())
	}

	return docIDs, nil
}
