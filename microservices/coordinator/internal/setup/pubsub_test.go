package setup

import (
	"context"
	"errors"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
)

// TestPubsubClient extends the shared FakePubsubClient to support error injection
type TestPubsubClient struct {
	*mocks.FakePubsubClient
	CreateTopicError        error
	CreateSubscriptionError error
	TopicExists             bool
	SubscriptionExists      bool
	TopicExistsError        error
	SubscriptionExistsError error
}

func NewTestPubsubClient() *TestPubsubClient {
	return &TestPubsubClient{
		FakePubsubClient:   mocks.NewFakePubsubClient(),
		TopicExists:        true, // Default to true (topics exist)
		SubscriptionExists: true, // Default to true (subscriptions exist)
	}
}

// TestPubsubTopic implements connect.PsTopic with controllable Exists behavior
type TestPubsubTopic struct {
	exists      bool
	existsError error
}

func (tt *TestPubsubTopic) Exists(ctx context.Context) (bool, error) {
	if tt.existsError != nil {
		return false, tt.existsError
	}
	return tt.exists, nil
}

func (tt *TestPubsubTopic) Publish(ctx context.Context, msg *pubsub.Message) connect.PsPublishResult {
	return &TestPubsubPublishResult{}
}

type TestPubsubPublishResult struct{}

func (tr *TestPubsubPublishResult) Get(ctx context.Context) (string, error) {
	return "test-message-id", nil
}

// TestPubsubSubscription implements connect.PsSubscription with controllable Exists behavior
type TestPubsubSubscription struct {
	exists      bool
	existsError error
	name        string
}

func (ts *TestPubsubSubscription) Exists(ctx context.Context) (bool, error) {
	if ts.existsError != nil {
		return false, ts.existsError
	}
	return ts.exists, nil
}

func (ts *TestPubsubSubscription) Receive(ctx context.Context, f func(context.Context, *pubsub.Message)) error {
	return nil
}

func (ts *TestPubsubSubscription) Close() error {
	return nil
}

func (ts *TestPubsubSubscription) ID() string {
	return ts.name
}

func (ts *TestPubsubSubscription) SetReceiveSettings(settings *pubsub.ReceiveSettings) {
	// No-op for testing
}

func (tc *TestPubsubClient) Topic(name string) connect.PsTopic {
	return &TestPubsubTopic{
		exists:      tc.TopicExists,
		existsError: tc.TopicExistsError,
	}
}

func (tc *TestPubsubClient) Subscription(name string) connect.PsSubscription {
	return &TestPubsubSubscription{
		exists:      tc.SubscriptionExists,
		existsError: tc.SubscriptionExistsError,
		name:        name,
	}
}

func (tc *TestPubsubClient) CreateTopic(ctx context.Context, name string) (connect.PsTopic, error) {
	if tc.CreateTopicError != nil {
		return nil, tc.CreateTopicError
	}
	return &TestPubsubTopic{exists: true}, nil
}

func (tc *TestPubsubClient) CreateSubscription(ctx context.Context, name string, cfg connect.SubscriptionConfig) (connect.PsSubscription, error) {
	if tc.CreateSubscriptionError != nil {
		return nil, tc.CreateSubscriptionError
	}
	return &TestPubsubSubscription{exists: true, name: name}, nil
}

// TestPubSubComprehensiveCoverage tests all code paths in the PubSub function
func TestPubSubComprehensiveCoverage(t *testing.T) {
	// Save original pubsubdata values
	originalTopics := pubsubdata.Topics
	originalSubscriptions := pubsubdata.PubsubSubscriptions
	defer func() {
		pubsubdata.Topics = originalTopics
		pubsubdata.PubsubSubscriptions = originalSubscriptions
	}()

	// Set up test data
	pubsubdata.Topics = []string{"test-topic-1", "test-topic-2"}
	pubsubdata.PubsubSubscriptions = map[string]string{
		"test-sub-1": "test-topic-1",
		"test-sub-2": "test-topic-2",
	}

	tests := []struct {
		name          string
		setupClient   func() connect.PsClient
		expectError   bool
		errorContains string
	}{
		{
			name: "All topics and subscriptions exist",
			setupClient: func() connect.PsClient {
				return NewTestPubsubClient()
			},
			expectError: false,
		},
		{
			name: "Topics don't exist, need to create - success",
			setupClient: func() connect.PsClient {
				client := NewTestPubsubClient()
				client.TopicExists = false // Force topic creation path
				return client
			},
			expectError: false,
		},
		{
			name: "Subscriptions don't exist, need to create - success",
			setupClient: func() connect.PsClient {
				client := NewTestPubsubClient()
				client.SubscriptionExists = false // Force subscription creation path
				return client
			},
			expectError: false,
		},
		{
			name: "Neither topics nor subscriptions exist - success",
			setupClient: func() connect.PsClient {
				client := NewTestPubsubClient()
				client.TopicExists = false        // Force topic creation path
				client.SubscriptionExists = false // Force subscription creation path
				return client
			},
			expectError: false,
		},
		{
			name: "Create topic fails",
			setupClient: func() connect.PsClient {
				client := NewTestPubsubClient()
				client.TopicExists = false // Force topic creation path
				client.CreateTopicError = errors.New("failed to create topic")
				return client
			},
			expectError:   true,
			errorContains: "failed to create topic",
		},
		{
			name: "Create subscription fails",
			setupClient: func() connect.PsClient {
				client := NewTestPubsubClient()
				client.SubscriptionExists = false // Force subscription creation path
				client.CreateSubscriptionError = errors.New("failed to create subscription")
				return client
			},
			expectError:   true,
			errorContains: "failed to create subscription",
		},
		{
			name: "Topic exists check fails - should continue",
			setupClient: func() connect.PsClient {
				client := NewTestPubsubClient()
				client.TopicExistsError = errors.New("topic exists check failed")
				return client
			},
			expectError: false, // Function continues on topic exists errors
		},
		{
			name: "Subscription exists check fails - should continue",
			setupClient: func() connect.PsClient {
				client := NewTestPubsubClient()
				client.SubscriptionExistsError = errors.New("subscription exists check failed")
				return client
			},
			expectError: false, // Function continues on subscription exists errors
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := tt.setupClient()
			ctx := context.Background()
			err := PubSub(ctx, client)

			if tt.expectError {
				assert.Error(t, err, "Expected error for test case: %s", tt.name)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains,
						"Error should contain expected substring")
				}
			} else {
				assert.NoError(t, err, "Expected no error for test case: %s", tt.name)
			}
		})
	}
}

// TestPubSubEmptyTopicsAndSubscriptions tests with empty data
func TestPubSubEmptyTopicsAndSubscriptions(t *testing.T) {
	originalTopics := pubsubdata.Topics
	originalSubscriptions := pubsubdata.PubsubSubscriptions
	defer func() {
		pubsubdata.Topics = originalTopics
		pubsubdata.PubsubSubscriptions = originalSubscriptions
	}()

	// Set empty data
	pubsubdata.Topics = []string{}
	pubsubdata.PubsubSubscriptions = map[string]string{}

	client := NewTestPubsubClient()

	ctx := context.Background()
	err := PubSub(ctx, client)
	assert.NoError(t, err, "Expected no error with empty data")
}

// TestPubSubSingleTopicAndSubscription tests with minimal data
func TestPubSubSingleTopicAndSubscription(t *testing.T) {
	originalTopics := pubsubdata.Topics
	originalSubscriptions := pubsubdata.PubsubSubscriptions
	defer func() {
		pubsubdata.Topics = originalTopics
		pubsubdata.PubsubSubscriptions = originalSubscriptions
	}()

	// Set single topic and subscription
	pubsubdata.Topics = []string{"single-topic"}
	pubsubdata.PubsubSubscriptions = map[string]string{
		"single-sub": "single-topic",
	}

	client := NewTestPubsubClient()

	ctx := context.Background()
	err := PubSub(ctx, client)
	assert.NoError(t, err, "Expected no error with single topic/sub")
}

func TestPubSubValidation(t *testing.T) {
	// Test the basic validation and setup without complex mocking
	// This tests the function signature and basic logic

	ctx := context.Background()

	// Save original pubsubdata values
	originalTopics := pubsubdata.Topics
	originalSubscriptions := pubsubdata.PubsubSubscriptions
	defer func() {
		pubsubdata.Topics = originalTopics
		pubsubdata.PubsubSubscriptions = originalSubscriptions
	}()

	// Set up test data
	testTopics := []string{"topic1", "topic2"}
	testSubscriptions := map[string]string{
		"sub1": "topic1",
		"sub2": "topic2",
	}

	pubsubdata.Topics = testTopics
	pubsubdata.PubsubSubscriptions = testSubscriptions

	// Verify the test data is set correctly
	assert.Len(t, pubsubdata.Topics, 2, "Expected 2 topics")
	assert.Len(t, pubsubdata.PubsubSubscriptions, 2, "Expected 2 subscriptions")

	// Verify topic names
	for i, topic := range pubsubdata.Topics {
		expectedTopic := testTopics[i]
		assert.Equal(t, expectedTopic, topic, "Topic should match expected value")
	}

	// Verify subscription mappings
	for subName, topicName := range pubsubdata.PubsubSubscriptions {
		expectedTopic := testSubscriptions[subName]
		assert.Equal(t, expectedTopic, topicName,
			"Subscription %s should map to expected topic", subName)
	}

	// Test context is valid
	assert.NotNil(t, ctx, "Context should be valid")
}

func TestPubSubEmptyData(t *testing.T) {
	// Test with empty topics and subscriptions
	originalTopics := pubsubdata.Topics
	originalSubscriptions := pubsubdata.PubsubSubscriptions
	defer func() {
		pubsubdata.Topics = originalTopics
		pubsubdata.PubsubSubscriptions = originalSubscriptions
	}()

	pubsubdata.Topics = []string{}
	pubsubdata.PubsubSubscriptions = map[string]string{}

	// Verify empty data is handled correctly
	assert.Empty(t, pubsubdata.Topics, "Expected empty topics")
	assert.Empty(t, pubsubdata.PubsubSubscriptions, "Expected empty subscriptions")
}

func TestPubSubIntegration(t *testing.T) {
	// Use the FakePubsubClient to create a realistic test that exercises the PubSub function
	// Create a fake PubSub client that implements the PsClient interface
	fakeClient := mocks.NewFakePubsubClient()

	ctx := context.Background()
	err := PubSub(ctx, fakeClient)

	// This should work with the fake client and exercise the function
	assert.NoError(t, err, "PubSub function should work with fake client")
}

func TestPubSubWithTimeout(t *testing.T) {
	// Test with a context that has timeout
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	fakeClient := mocks.NewFakePubsubClient()
	err := PubSub(ctx, fakeClient)
	// Context timeout may or may not affect this depending on implementation
	if err != nil {
		assert.Contains(t, err.Error(), "context", "Timeout error should mention context")
	}
}

func TestPubSubWithCancelledContext(t *testing.T) {
	// Test with a cancelled context
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // Cancel immediately

	fakeClient := mocks.NewFakePubsubClient()
	err := PubSub(ctx, fakeClient)
	// Cancelled context may or may not affect this depending on implementation
	if err != nil {
		assert.Contains(t, err.Error(), "context", "Cancelled context error should mention context")
	}
}
