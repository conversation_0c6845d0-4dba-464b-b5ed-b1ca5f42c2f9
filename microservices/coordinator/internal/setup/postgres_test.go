package setup

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"synapse-its.com/shared/connect"
)

func TestPostgresNilExecutor(t *testing.T) {
	// Test with nil executor - this should return an error, not panic
	err := Postgres(nil, "1.0.0")

	// Assert that an error is returned
	assert.Error(t, err, "Expected error when calling Postgres with nil executor")
	assert.Contains(t, err.Error(), "cannot be nil", "Error should mention nil executor")
}

func TestPostgresWithValidExecutor(t *testing.T) {
	// Create a PostgresExecutor with proper config
	pg := &connect.PostgresExecutor{
		Config: connect.DatabaseConfig{
			DBName:      "test-db",
			Environment: "test",
			Namespace:   "test-ns",
		},
		Ctx: context.Background(),
		// DB is still nil, but we can test that the function attempts to call ApplyMigrations
	}

	// Test with a valid version - this should panic due to nil DB connection
	assert.Panics(t, func() {
		Postgres(pg, "1.0.0")
	}, "Expected panic due to nil DB connection")
}

func TestPostgresWithEmptyVersion(t *testing.T) {
	// Test with empty version string (should use latest schema)
	pg := &connect.PostgresExecutor{
		Config: connect.DatabaseConfig{
			DBName:      "test-db",
			Environment: "test",
			Namespace:   "test-ns",
		},
		Ctx: context.Background(),
	}

	// Test with empty version - this should also panic due to nil DB connection
	assert.Panics(t, func() {
		Postgres(pg, "")
	}, "Expected panic due to nil DB connection with empty version")
}

func TestPostgresExecutorCreation(t *testing.T) {
	// Test that we can create the required components correctly
	tests := []struct {
		name    string
		config  connect.DatabaseConfig
		version string
	}{
		{
			name: "Standard config",
			config: connect.DatabaseConfig{
				DBName:      "test-database",
				Environment: "test",
				Namespace:   "test-ns",
			},
			version: "1.0.0",
		},
		{
			name: "Empty version",
			config: connect.DatabaseConfig{
				DBName:      "prod-database",
				Environment: "production",
				Namespace:   "prod-ns",
			},
			version: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create PostgresExecutor
			pg := &connect.PostgresExecutor{
				Config: tt.config,
				Ctx:    context.Background(),
			}

			// Assert executor was created properly
			require.NotNil(t, pg, "PostgresExecutor should not be nil")

			// Assert config is set correctly
			assert.Equal(t, tt.config.DBName, pg.Config.DBName, "DBName should match")
			assert.Equal(t, tt.config.Environment, pg.Config.Environment, "Environment should match")
			assert.Equal(t, tt.config.Namespace, pg.Config.Namespace, "Namespace should match")

			// Assert context is set
			assert.NotNil(t, pg.Ctx, "Context should not be nil")
		})
	}
}

func TestPostgresInputValidation(t *testing.T) {
	// Test various input combinations to ensure proper validation
	tests := []struct {
		name           string
		executor       *connect.PostgresExecutor
		version        string
		expectError    bool
		expectPanic    bool
		errorSubstring string
	}{
		{
			name:           "Nil executor",
			executor:       nil,
			version:        "1.0.0",
			expectError:    true,
			expectPanic:    false, // Should return error, not panic
			errorSubstring: "cannot be nil",
		},
		{
			name: "Valid executor with version",
			executor: &connect.PostgresExecutor{
				Config: connect.DatabaseConfig{DBName: "test"},
				Ctx:    context.Background(),
			},
			version:     "1.0.0",
			expectError: false, // Will panic instead due to nil DB
			expectPanic: true,  // Will panic when trying to use nil DB
		},
		{
			name: "Valid executor with empty version",
			executor: &connect.PostgresExecutor{
				Config: connect.DatabaseConfig{DBName: "test"},
				Ctx:    context.Background(),
			},
			version:     "",
			expectError: false, // Will panic instead due to nil DB
			expectPanic: true,  // Will panic when trying to use nil DB
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.expectPanic {
				// Use testify's panic assertion
				assert.Panics(t, func() {
					Postgres(tt.executor, tt.version)
				}, "Expected panic for %s", tt.name)
			} else if tt.expectError {
				// Test error cases
				err := Postgres(tt.executor, tt.version)
				assert.Error(t, err, "Expected an error")
				if tt.errorSubstring != "" {
					assert.Contains(t, err.Error(), tt.errorSubstring, "Error should contain expected substring")
				}
			} else {
				// Test success cases (though none exist currently due to nil DB)
				err := Postgres(tt.executor, tt.version)
				assert.NoError(t, err, "Expected no error")
			}
		})
	}
}
