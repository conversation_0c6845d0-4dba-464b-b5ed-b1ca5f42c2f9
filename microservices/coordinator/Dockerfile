# Stage 1: Builder (Compiles the app)
FROM golang:1.24 AS builder
WORKDIR /app

# Accept build arguments for git branch and tag information
ARG GIT_BRANCH=unknown
ARG GIT_TAG=untagged

# Copy the microservice code to the WORKDIR folder
COPY microservices/coordinator .

# Copy the shared libraries folder
COPY shared /shared

# Copy the schemas folder
COPY schemas /schemas

# Insert a script that adds replace directives for all shared modules
# and runs `go mod tidy` and `go mod download` for each shared module,
# computes updated .sql file md5 values (if needed),
# then run `go mod tidy` and `go mod download` for the app itself.
RUN \
  chmod +x /shared/setup_shared.sh && \
  /shared/setup_shared.sh && \
  chmod +x /schemas/compute_md5.sh && \
  /schemas/compute_md5.sh && \
  go mod tidy && go mod download

# Generate an identifier file combining branch and tag information
RUN echo "${GIT_BRANCH}-${GIT_TAG}" > identifier.txt

# Build the application
RUN go build -o coordinator



# Stage 2: Production (Minimal Image for Running the App)
FROM gcr.io/distroless/base-debian12 AS prod

WORKDIR /app

# Use a non-root user (best practice)
USER nonroot:nonroot

# Copy the compiled binary and the identifier file from the builder stage
COPY --from=builder /app/coordinator .
COPY --from=builder /app/identifier.txt .

# Copy the schema directory
COPY schemas /schemas

CMD ["./coordinator"]



# Stage 3: Development
FROM golang:1.24 AS dev
WORKDIR /app

# Development mode expects a volume mount for the source code
# CMD that:
# 1. Adds replace directives for each shared module,
# 2. Runs go mod tidy and go mod download in each shared module,
# 3. Runs go mod tidy and go mod download for the microservice,
# 4. Finally compiles and runs the main application.
CMD sh -c "\
  /shared/setup_shared.sh && \
  go mod tidy && go mod download && \
  /schemas/compute_md5.sh && \
  go run ."
