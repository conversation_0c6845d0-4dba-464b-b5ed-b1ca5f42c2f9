package main

import (
	"context"
	"net/http"
	"os"

	"go.uber.org/zap"
	"synapse-its.com/coordinator/internal/setup"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/healthz"
	"synapse-its.com/shared/logger"
)

// Mockable dependencies for testing
var (
	healthzNewServer    = healthz.NewServer
	connectGetReleaseId = connect.GetReleaseIdentifier
	connectRedis        = connect.Redis
	connectPubSub       = connect.PubSub
	connectBigQuery     = connect.BigQuery
	connectPostgres     = connect.Postgres
	connectRedisSet     = connect.RedisSet
	setupPubSub         = setup.PubSub
	setupBigQuery       = setup.BigQuery
	setupPostgres       = setup.Postgres
	loggerFatalf        = logger.Fatalf
	loggerInfo          = logger.Info
	loggerDebug         = logger.Debug
	loggerInfof         = logger.Infof
	osGetenv            = os.Getenv
)

func main() {
	Run(context.Background())
}

// Run contains the main application logic with testable dependencies
func Run(ctx context.Context) {
	// Start healthz service
	healthzsrv := healthzNewServer(osGetenv("HEALTH_PORT"))
	if err := healthzsrv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		loggerFatalf("Health server failed: %v", err)
	}
	defer func() {
		if err := healthzsrv.Shutdown(ctx); err != nil {
			loggerDebug("Health server shutdown error", zap.Error(err))
		}
	}()

	loggerInfo("Starting coordinator...")
	healthzsrv.SetBootComplete()

	// Get the release identifier string.
	loggerDebug("Getting release identifier...")
	releaseIdentifier, err := connectGetReleaseId()
	if err != nil {
		loggerFatalf("Failed to get release identifier: %v", err)
	}
	loggerInfof("Release Identifier: %s", releaseIdentifier)

	// Get the Redis client.
	loggerDebug("Connecting to Redis...")
	redisClient, err := connectRedis(ctx)
	if err != nil {
		loggerFatalf("Failed to connect to Redis: %v", err)
	}
	defer redisClient.Close()

	// Get and setup the PubSub client.
	loggerDebug("Connecting to Pubsub..")
	pubsubClient, err := connectPubSub(ctx)
	if err != nil {
		loggerFatalf("Failed to connect to PubSub: %v", err)
	}
	defer pubsubClient.Close()

	// Get and setup Bigquery.
	loggerDebug("Connecting to BigQuery...")
	bq, err := connectBigQuery(ctx, nil, nil)
	if err != nil {
		loggerFatalf("Failed to connect to BigQuery: %v", err)
	}
	defer bq.Close()

	// Get and setup Postgres.
	loggerDebug("Connecting to Postgres...")
	pg, err := connectPostgres(ctx, nil)
	if err != nil {
		loggerFatalf("Failed to connect to Postgres: %v", err)
	}
	defer pg.Close()

	// All connections made
	healthzsrv.SetReady()

	// Setup all services
	if err := setupPubSub(ctx, pubsubClient); err != nil {
		loggerFatalf("Failed to setup PubSub: %v", err)
	}
	if err := connectRedisSet(ctx, redisClient, "ReleaseIdentifier:PubSub", releaseIdentifier); err != nil {
		loggerFatalf("Failed to set Redis key PubSub: %v", err)
	}

	if err = setupBigQuery(bq, ""); err != nil {
		loggerFatalf("Failed to setup BigQuery: %v", err)
	}
	if err := connectRedisSet(ctx, redisClient, "ReleaseIdentifier:BigQuery", releaseIdentifier); err != nil {
		loggerFatalf("Failed to set Redis key BigQuery: %v", err)
	}

	if err = setupPostgres(pg, ""); err != nil {
		loggerFatalf("Failed to setup Postgres: %v", err)
	}
	if err := connectRedisSet(ctx, redisClient, "ReleaseIdentifier:Postgres", releaseIdentifier); err != nil {
		loggerFatalf("Failed to set Redis key Postgres: %v", err)
	}

	// Finished all setups
	loggerInfo("Coordinator setup complete. Shutting down.")
}
