package main

import (
	"context"
	"errors"
	"testing"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/healthz"
	"synapse-its.com/shared/mocks"
)

// TestMainExists verifies that main function exists
func TestMainExists(t *testing.T) {
	assert.NotNil(t, main)
}

// TestMainFunction tests the actual main function by mocking Run
func TestMainFunction(t *testing.T) {
	// Save original dependencies
	origHealthzNewServer := healthzNewServer
	origLoggerFatalf := loggerFatalf
	origOsGetenv := osGetenv

	defer func() {
		healthzNewServer = origHealthzNewServer
		loggerFatalf = origLoggerFatalf
		osGetenv = origOsGetenv
	}()

	// Mock to exit quickly
	healthzNewServer = func(port string) healthz.HealthzServer {
		return &mockHealthzServer{listenErr: errors.New("quick exit")}
	}

	var fatalCalled bool
	loggerFatalf = func(format string, args ...interface{}) {
		fatalCalled = true
		panic("test exit")
	}

	osGetenv = func(key string) string {
		return "8080"
	}

	defer func() {
		recover() // Catch the panic
	}()

	// Call main() which will call Run() internally
	main()

	assert.True(t, fatalCalled, "Main should have triggered Run which should have called fatal")
}

// TestRunHealthServerError tests health server error path
func TestRunHealthServerError(t *testing.T) {
	origHealthzNewServer := healthzNewServer
	origLoggerFatalf := loggerFatalf
	origOsGetenv := osGetenv

	defer func() {
		healthzNewServer = origHealthzNewServer
		loggerFatalf = origLoggerFatalf
		osGetenv = origOsGetenv
	}()

	healthzNewServer = func(port string) healthz.HealthzServer {
		return &mockHealthzServer{listenErr: errors.New("mock error")}
	}

	var fatalCalled bool
	loggerFatalf = func(format string, args ...interface{}) {
		fatalCalled = true
		panic("test exit")
	}

	osGetenv = func(key string) string {
		return "8080"
	}

	defer func() {
		recover()
	}()

	Run(context.Background())

	assert.True(t, fatalCalled, "Fatal should have been called")
}

// TestRunReleaseIdError tests release identifier error
func TestRunReleaseIdError(t *testing.T) {
	origHealthzNewServer := healthzNewServer
	origConnectGetReleaseId := connectGetReleaseId
	origLoggerFatalf := loggerFatalf
	origLoggerInfo := loggerInfo
	origOsGetenv := osGetenv

	defer func() {
		healthzNewServer = origHealthzNewServer
		connectGetReleaseId = origConnectGetReleaseId
		loggerFatalf = origLoggerFatalf
		loggerInfo = origLoggerInfo
		osGetenv = origOsGetenv
	}()

	healthzNewServer = func(port string) healthz.HealthzServer {
		return &mockHealthzServer{}
	}

	connectGetReleaseId = func() (string, error) {
		return "", errors.New("release id error")
	}

	var fatalCalled bool
	loggerFatalf = func(format string, args ...interface{}) {
		fatalCalled = true
		panic("test exit")
	}

	loggerInfo = func(msg interface{}, fields ...zap.Field) {}

	osGetenv = func(key string) string {
		return "8080"
	}

	defer func() {
		recover()
	}()

	Run(context.Background())

	assert.True(t, fatalCalled, "Fatal should have been called")
}

// TestRunRedisError tests Redis connection error
func TestRunRedisError(t *testing.T) {
	origHealthzNewServer := healthzNewServer
	origConnectGetReleaseId := connectGetReleaseId
	origConnectRedis := connectRedis
	origLoggerFatalf := loggerFatalf
	origLoggerInfo := loggerInfo
	origLoggerDebug := loggerDebug
	origLoggerInfof := loggerInfof
	origOsGetenv := osGetenv

	defer func() {
		healthzNewServer = origHealthzNewServer
		connectGetReleaseId = origConnectGetReleaseId
		connectRedis = origConnectRedis
		loggerFatalf = origLoggerFatalf
		loggerInfo = origLoggerInfo
		loggerDebug = origLoggerDebug
		loggerInfof = origLoggerInfof
		osGetenv = origOsGetenv
	}()

	healthzNewServer = func(port string) healthz.HealthzServer {
		return &mockHealthzServer{}
	}

	connectGetReleaseId = func() (string, error) {
		return "test-release", nil
	}

	connectRedis = func(ctx context.Context) (*redis.Client, error) {
		return nil, errors.New("redis error")
	}

	var fatalCalled bool
	loggerFatalf = func(format string, args ...interface{}) {
		fatalCalled = true
		panic("test exit")
	}

	loggerInfo = func(msg interface{}, fields ...zap.Field) {}
	loggerDebug = func(msg interface{}, fields ...zap.Field) {}
	loggerInfof = func(format string, args ...interface{}) {}

	osGetenv = func(key string) string {
		return "8080"
	}

	defer func() {
		recover()
	}()

	Run(context.Background())

	assert.True(t, fatalCalled, "Fatal should have been called")
}

// TestRunPubSubError tests PubSub connection error
func TestRunPubSubError(t *testing.T) {
	origHealthzNewServer := healthzNewServer
	origConnectGetReleaseId := connectGetReleaseId
	origConnectRedis := connectRedis
	origConnectPubSub := connectPubSub
	origLoggerFatalf := loggerFatalf
	origLoggerInfo := loggerInfo
	origLoggerDebug := loggerDebug
	origLoggerInfof := loggerInfof
	origOsGetenv := osGetenv

	defer func() {
		healthzNewServer = origHealthzNewServer
		connectGetReleaseId = origConnectGetReleaseId
		connectRedis = origConnectRedis
		connectPubSub = origConnectPubSub
		loggerFatalf = origLoggerFatalf
		loggerInfo = origLoggerInfo
		loggerDebug = origLoggerDebug
		loggerInfof = origLoggerInfof
		osGetenv = origOsGetenv
	}()

	healthzNewServer = func(port string) healthz.HealthzServer {
		return &mockHealthzServer{}
	}

	connectGetReleaseId = func() (string, error) {
		return "test-release", nil
	}

	connectRedis = func(ctx context.Context) (*redis.Client, error) {
		return &redis.Client{}, nil
	}

	connectPubSub = func(ctx context.Context) (connect.PsClient, error) {
		return nil, errors.New("pubsub error")
	}

	var fatalCalled bool
	loggerFatalf = func(format string, args ...interface{}) {
		fatalCalled = true
		panic("test exit")
	}

	loggerInfo = func(msg interface{}, fields ...zap.Field) {}
	loggerDebug = func(msg interface{}, fields ...zap.Field) {}
	loggerInfof = func(format string, args ...interface{}) {}

	osGetenv = func(key string) string {
		return "8080"
	}

	defer func() {
		recover()
	}()

	Run(context.Background())

	assert.True(t, fatalCalled, "Fatal should have been called")
}

// TestRunBigQueryError tests BigQuery connection error
func TestRunBigQueryError(t *testing.T) {
	origHealthzNewServer := healthzNewServer
	origConnectGetReleaseId := connectGetReleaseId
	origConnectRedis := connectRedis
	origConnectPubSub := connectPubSub
	origConnectBigQuery := connectBigQuery
	origLoggerFatalf := loggerFatalf
	origLoggerInfo := loggerInfo
	origLoggerDebug := loggerDebug
	origLoggerInfof := loggerInfof
	origOsGetenv := osGetenv

	defer func() {
		healthzNewServer = origHealthzNewServer
		connectGetReleaseId = origConnectGetReleaseId
		connectRedis = origConnectRedis
		connectPubSub = origConnectPubSub
		connectBigQuery = origConnectBigQuery
		loggerFatalf = origLoggerFatalf
		loggerInfo = origLoggerInfo
		loggerDebug = origLoggerDebug
		loggerInfof = origLoggerInfof
		osGetenv = origOsGetenv
	}()

	healthzNewServer = func(port string) healthz.HealthzServer {
		return &mockHealthzServer{}
	}

	connectGetReleaseId = func() (string, error) {
		return "test-release", nil
	}

	connectRedis = func(ctx context.Context) (*redis.Client, error) {
		return &redis.Client{}, nil
	}

	connectPubSub = func(ctx context.Context) (connect.PsClient, error) {
		return &mocks.FakePubsubClient{}, nil
	}

	connectBigQuery = func(ctx context.Context, config *connect.DatabaseConfig, factory connect.BQClientFactory) (*connect.BigQueryExecutor, error) {
		return nil, errors.New("bigquery error")
	}

	var fatalCalled bool
	loggerFatalf = func(format string, args ...interface{}) {
		fatalCalled = true
		panic("test exit")
	}

	loggerInfo = func(msg interface{}, fields ...zap.Field) {}
	loggerDebug = func(msg interface{}, fields ...zap.Field) {}
	loggerInfof = func(format string, args ...interface{}) {}

	osGetenv = func(key string) string {
		return "8080"
	}

	defer func() {
		recover()
	}()

	Run(context.Background())

	assert.True(t, fatalCalled, "Fatal should have been called")
}

// TestRunPostgresError tests Postgres connection error
func TestRunPostgresError(t *testing.T) {
	origHealthzNewServer := healthzNewServer
	origConnectGetReleaseId := connectGetReleaseId
	origConnectRedis := connectRedis
	origConnectPubSub := connectPubSub
	origConnectBigQuery := connectBigQuery
	origConnectPostgres := connectPostgres
	origLoggerFatalf := loggerFatalf
	origLoggerInfo := loggerInfo
	origLoggerDebug := loggerDebug
	origLoggerInfof := loggerInfof
	origOsGetenv := osGetenv

	defer func() {
		healthzNewServer = origHealthzNewServer
		connectGetReleaseId = origConnectGetReleaseId
		connectRedis = origConnectRedis
		connectPubSub = origConnectPubSub
		connectBigQuery = origConnectBigQuery
		connectPostgres = origConnectPostgres
		loggerFatalf = origLoggerFatalf
		loggerInfo = origLoggerInfo
		loggerDebug = origLoggerDebug
		loggerInfof = origLoggerInfof
		osGetenv = origOsGetenv
	}()

	healthzNewServer = func(port string) healthz.HealthzServer {
		return &mockHealthzServer{}
	}

	connectGetReleaseId = func() (string, error) {
		return "test-release", nil
	}

	connectRedis = func(ctx context.Context) (*redis.Client, error) {
		return &redis.Client{}, nil
	}

	connectPubSub = func(ctx context.Context) (connect.PsClient, error) {
		return &mocks.FakePubsubClient{}, nil
	}

	connectBigQuery = func(ctx context.Context, config *connect.DatabaseConfig, factory connect.BQClientFactory) (*connect.BigQueryExecutor, error) {
		return &connect.BigQueryExecutor{}, nil
	}

	connectPostgres = func(ctx context.Context, config *connect.DatabaseConfig) (*connect.PostgresExecutor, error) {
		return nil, errors.New("postgres error")
	}

	var fatalCalled bool
	loggerFatalf = func(format string, args ...interface{}) {
		fatalCalled = true
		panic("test exit")
	}

	loggerInfo = func(msg interface{}, fields ...zap.Field) {}
	loggerDebug = func(msg interface{}, fields ...zap.Field) {}
	loggerInfof = func(format string, args ...interface{}) {}

	osGetenv = func(key string) string {
		return "8080"
	}

	defer func() {
		recover()
	}()

	Run(context.Background())

	assert.True(t, fatalCalled, "Fatal should have been called")
}

// TestRunSetupPubSubError tests PubSub setup error
func TestRunSetupPubSubError(t *testing.T) {
	origHealthzNewServer := healthzNewServer
	origConnectGetReleaseId := connectGetReleaseId
	origConnectRedis := connectRedis
	origConnectPubSub := connectPubSub
	origConnectBigQuery := connectBigQuery
	origConnectPostgres := connectPostgres
	origSetupPubSub := setupPubSub
	origLoggerFatalf := loggerFatalf
	origLoggerInfo := loggerInfo
	origLoggerDebug := loggerDebug
	origLoggerInfof := loggerInfof
	origOsGetenv := osGetenv

	defer func() {
		healthzNewServer = origHealthzNewServer
		connectGetReleaseId = origConnectGetReleaseId
		connectRedis = origConnectRedis
		connectPubSub = origConnectPubSub
		connectBigQuery = origConnectBigQuery
		connectPostgres = origConnectPostgres
		setupPubSub = origSetupPubSub
		loggerFatalf = origLoggerFatalf
		loggerInfo = origLoggerInfo
		loggerDebug = origLoggerDebug
		loggerInfof = origLoggerInfof
		osGetenv = origOsGetenv
	}()

	healthzNewServer = func(port string) healthz.HealthzServer {
		return &mockHealthzServer{}
	}

	connectGetReleaseId = func() (string, error) {
		return "test-release", nil
	}

	connectRedis = func(ctx context.Context) (*redis.Client, error) {
		return &redis.Client{}, nil
	}

	connectPubSub = func(ctx context.Context) (connect.PsClient, error) {
		return &mocks.FakePubsubClient{}, nil
	}

	connectBigQuery = func(ctx context.Context, config *connect.DatabaseConfig, factory connect.BQClientFactory) (*connect.BigQueryExecutor, error) {
		return &connect.BigQueryExecutor{}, nil
	}

	connectPostgres = func(ctx context.Context, config *connect.DatabaseConfig) (*connect.PostgresExecutor, error) {
		return &connect.PostgresExecutor{}, nil
	}

	setupPubSub = func(ctx context.Context, client connect.PsClient) error {
		return errors.New("pubsub setup error")
	}

	var fatalCalled bool
	loggerFatalf = func(format string, args ...interface{}) {
		fatalCalled = true
		panic("test exit")
	}

	loggerInfo = func(msg interface{}, fields ...zap.Field) {}
	loggerDebug = func(msg interface{}, fields ...zap.Field) {}
	loggerInfof = func(format string, args ...interface{}) {}

	osGetenv = func(key string) string {
		return "8080"
	}

	defer func() {
		recover()
	}()

	Run(context.Background())

	assert.True(t, fatalCalled, "Fatal should have been called")
}

// TestRunRedisSetError tests Redis set error
func TestRunRedisSetError(t *testing.T) {
	origHealthzNewServer := healthzNewServer
	origConnectGetReleaseId := connectGetReleaseId
	origConnectRedis := connectRedis
	origConnectPubSub := connectPubSub
	origConnectBigQuery := connectBigQuery
	origConnectPostgres := connectPostgres
	origConnectRedisSet := connectRedisSet
	origSetupPubSub := setupPubSub
	origLoggerFatalf := loggerFatalf
	origLoggerInfo := loggerInfo
	origLoggerDebug := loggerDebug
	origLoggerInfof := loggerInfof
	origOsGetenv := osGetenv

	defer func() {
		healthzNewServer = origHealthzNewServer
		connectGetReleaseId = origConnectGetReleaseId
		connectRedis = origConnectRedis
		connectPubSub = origConnectPubSub
		connectBigQuery = origConnectBigQuery
		connectPostgres = origConnectPostgres
		connectRedisSet = origConnectRedisSet
		setupPubSub = origSetupPubSub
		loggerFatalf = origLoggerFatalf
		loggerInfo = origLoggerInfo
		loggerDebug = origLoggerDebug
		loggerInfof = origLoggerInfof
		osGetenv = origOsGetenv
	}()

	healthzNewServer = func(port string) healthz.HealthzServer {
		return &mockHealthzServer{}
	}

	connectGetReleaseId = func() (string, error) {
		return "test-release", nil
	}

	connectRedis = func(ctx context.Context) (*redis.Client, error) {
		return &redis.Client{}, nil
	}

	connectPubSub = func(ctx context.Context) (connect.PsClient, error) {
		return &mocks.FakePubsubClient{}, nil
	}

	connectBigQuery = func(ctx context.Context, config *connect.DatabaseConfig, factory connect.BQClientFactory) (*connect.BigQueryExecutor, error) {
		return &connect.BigQueryExecutor{}, nil
	}

	connectPostgres = func(ctx context.Context, config *connect.DatabaseConfig) (*connect.PostgresExecutor, error) {
		return &connect.PostgresExecutor{}, nil
	}

	connectRedisSet = func(ctx context.Context, client *redis.Client, key, value string) error {
		return errors.New("redis set error")
	}

	setupPubSub = func(ctx context.Context, client connect.PsClient) error {
		return nil
	}

	var fatalCalled bool
	loggerFatalf = func(format string, args ...interface{}) {
		fatalCalled = true
		panic("test exit")
	}

	loggerInfo = func(msg interface{}, fields ...zap.Field) {}
	loggerDebug = func(msg interface{}, fields ...zap.Field) {}
	loggerInfof = func(format string, args ...interface{}) {}

	osGetenv = func(key string) string {
		return "8080"
	}

	defer func() {
		recover()
	}()

	Run(context.Background())

	assert.True(t, fatalCalled, "Fatal should have been called")
}

// TestRunSetupBigQueryError tests BigQuery setup error
func TestRunSetupBigQueryError(t *testing.T) {
	origHealthzNewServer := healthzNewServer
	origConnectGetReleaseId := connectGetReleaseId
	origConnectRedis := connectRedis
	origConnectPubSub := connectPubSub
	origConnectBigQuery := connectBigQuery
	origConnectPostgres := connectPostgres
	origConnectRedisSet := connectRedisSet
	origSetupPubSub := setupPubSub
	origSetupBigQuery := setupBigQuery
	origLoggerFatalf := loggerFatalf
	origLoggerInfo := loggerInfo
	origLoggerDebug := loggerDebug
	origLoggerInfof := loggerInfof
	origOsGetenv := osGetenv

	defer func() {
		healthzNewServer = origHealthzNewServer
		connectGetReleaseId = origConnectGetReleaseId
		connectRedis = origConnectRedis
		connectPubSub = origConnectPubSub
		connectBigQuery = origConnectBigQuery
		connectPostgres = origConnectPostgres
		connectRedisSet = origConnectRedisSet
		setupPubSub = origSetupPubSub
		setupBigQuery = origSetupBigQuery
		loggerFatalf = origLoggerFatalf
		loggerInfo = origLoggerInfo
		loggerDebug = origLoggerDebug
		loggerInfof = origLoggerInfof
		osGetenv = origOsGetenv
	}()

	healthzNewServer = func(port string) healthz.HealthzServer {
		return &mockHealthzServer{}
	}

	connectGetReleaseId = func() (string, error) {
		return "test-release", nil
	}

	connectRedis = func(ctx context.Context) (*redis.Client, error) {
		return &redis.Client{}, nil
	}

	connectPubSub = func(ctx context.Context) (connect.PsClient, error) {
		return &mocks.FakePubsubClient{}, nil
	}

	connectBigQuery = func(ctx context.Context, config *connect.DatabaseConfig, factory connect.BQClientFactory) (*connect.BigQueryExecutor, error) {
		return &connect.BigQueryExecutor{}, nil
	}

	connectPostgres = func(ctx context.Context, config *connect.DatabaseConfig) (*connect.PostgresExecutor, error) {
		return &connect.PostgresExecutor{}, nil
	}

	connectRedisSet = func(ctx context.Context, client *redis.Client, key, value string) error {
		return nil
	}

	setupPubSub = func(ctx context.Context, client connect.PsClient) error {
		return nil
	}

	setupBigQuery = func(bq *connect.BigQueryExecutor, query string) error {
		return errors.New("bigquery setup error")
	}

	var fatalCalled bool
	loggerFatalf = func(format string, args ...interface{}) {
		fatalCalled = true
		panic("test exit")
	}

	loggerInfo = func(msg interface{}, fields ...zap.Field) {}
	loggerDebug = func(msg interface{}, fields ...zap.Field) {}
	loggerInfof = func(format string, args ...interface{}) {}

	osGetenv = func(key string) string {
		return "8080"
	}

	defer func() {
		recover()
	}()

	Run(context.Background())

	assert.True(t, fatalCalled, "Fatal should have been called")
}

// TestRunRedisSetBigQueryError tests Redis set error after BigQuery setup
func TestRunRedisSetBigQueryError(t *testing.T) {
	origHealthzNewServer := healthzNewServer
	origConnectGetReleaseId := connectGetReleaseId
	origConnectRedis := connectRedis
	origConnectPubSub := connectPubSub
	origConnectBigQuery := connectBigQuery
	origConnectPostgres := connectPostgres
	origConnectRedisSet := connectRedisSet
	origSetupPubSub := setupPubSub
	origSetupBigQuery := setupBigQuery
	origLoggerFatalf := loggerFatalf
	origLoggerInfo := loggerInfo
	origLoggerDebug := loggerDebug
	origLoggerInfof := loggerInfof
	origOsGetenv := osGetenv

	defer func() {
		healthzNewServer = origHealthzNewServer
		connectGetReleaseId = origConnectGetReleaseId
		connectRedis = origConnectRedis
		connectPubSub = origConnectPubSub
		connectBigQuery = origConnectBigQuery
		connectPostgres = origConnectPostgres
		connectRedisSet = origConnectRedisSet
		setupPubSub = origSetupPubSub
		setupBigQuery = origSetupBigQuery
		loggerFatalf = origLoggerFatalf
		loggerInfo = origLoggerInfo
		loggerDebug = origLoggerDebug
		loggerInfof = origLoggerInfof
		osGetenv = origOsGetenv
	}()

	healthzNewServer = func(port string) healthz.HealthzServer {
		return &mockHealthzServer{}
	}

	connectGetReleaseId = func() (string, error) {
		return "test-release", nil
	}

	connectRedis = func(ctx context.Context) (*redis.Client, error) {
		return &redis.Client{}, nil
	}

	connectPubSub = func(ctx context.Context) (connect.PsClient, error) {
		return &mocks.FakePubsubClient{}, nil
	}

	connectBigQuery = func(ctx context.Context, config *connect.DatabaseConfig, factory connect.BQClientFactory) (*connect.BigQueryExecutor, error) {
		return &connect.BigQueryExecutor{}, nil
	}

	connectPostgres = func(ctx context.Context, config *connect.DatabaseConfig) (*connect.PostgresExecutor, error) {
		return &connect.PostgresExecutor{}, nil
	}

	callCount := 0
	connectRedisSet = func(ctx context.Context, client *redis.Client, key, value string) error {
		callCount++
		if callCount == 1 {
			return nil // First call (PubSub) succeeds
		}
		return errors.New("redis set bigquery error") // Second call (BigQuery) fails
	}

	setupPubSub = func(ctx context.Context, client connect.PsClient) error {
		return nil
	}

	setupBigQuery = func(bq *connect.BigQueryExecutor, query string) error {
		return nil
	}

	var fatalCalled bool
	loggerFatalf = func(format string, args ...interface{}) {
		fatalCalled = true
		panic("test exit")
	}

	loggerInfo = func(msg interface{}, fields ...zap.Field) {}
	loggerDebug = func(msg interface{}, fields ...zap.Field) {}
	loggerInfof = func(format string, args ...interface{}) {}

	osGetenv = func(key string) string {
		return "8080"
	}

	defer func() {
		recover()
	}()

	Run(context.Background())

	assert.True(t, fatalCalled, "Fatal should have been called")
}

// TestRunSetupPostgresError tests Postgres setup error
func TestRunSetupPostgresError(t *testing.T) {
	origHealthzNewServer := healthzNewServer
	origConnectGetReleaseId := connectGetReleaseId
	origConnectRedis := connectRedis
	origConnectPubSub := connectPubSub
	origConnectBigQuery := connectBigQuery
	origConnectPostgres := connectPostgres
	origConnectRedisSet := connectRedisSet
	origSetupPubSub := setupPubSub
	origSetupBigQuery := setupBigQuery
	origSetupPostgres := setupPostgres
	origLoggerFatalf := loggerFatalf
	origLoggerInfo := loggerInfo
	origLoggerDebug := loggerDebug
	origLoggerInfof := loggerInfof
	origOsGetenv := osGetenv

	defer func() {
		healthzNewServer = origHealthzNewServer
		connectGetReleaseId = origConnectGetReleaseId
		connectRedis = origConnectRedis
		connectPubSub = origConnectPubSub
		connectBigQuery = origConnectBigQuery
		connectPostgres = origConnectPostgres
		connectRedisSet = origConnectRedisSet
		setupPubSub = origSetupPubSub
		setupBigQuery = origSetupBigQuery
		setupPostgres = origSetupPostgres
		loggerFatalf = origLoggerFatalf
		loggerInfo = origLoggerInfo
		loggerDebug = origLoggerDebug
		loggerInfof = origLoggerInfof
		osGetenv = origOsGetenv
	}()

	healthzNewServer = func(port string) healthz.HealthzServer {
		return &mockHealthzServer{}
	}

	connectGetReleaseId = func() (string, error) {
		return "test-release", nil
	}

	connectRedis = func(ctx context.Context) (*redis.Client, error) {
		return &redis.Client{}, nil
	}

	connectPubSub = func(ctx context.Context) (connect.PsClient, error) {
		return &mocks.FakePubsubClient{}, nil
	}

	connectBigQuery = func(ctx context.Context, config *connect.DatabaseConfig, factory connect.BQClientFactory) (*connect.BigQueryExecutor, error) {
		return &connect.BigQueryExecutor{}, nil
	}

	connectPostgres = func(ctx context.Context, config *connect.DatabaseConfig) (*connect.PostgresExecutor, error) {
		return &connect.PostgresExecutor{}, nil
	}

	connectRedisSet = func(ctx context.Context, client *redis.Client, key, value string) error {
		return nil
	}

	setupPubSub = func(ctx context.Context, client connect.PsClient) error {
		return nil
	}

	setupBigQuery = func(bq *connect.BigQueryExecutor, query string) error {
		return nil
	}

	setupPostgres = func(pg *connect.PostgresExecutor, query string) error {
		return errors.New("postgres setup error")
	}

	var fatalCalled bool
	loggerFatalf = func(format string, args ...interface{}) {
		fatalCalled = true
		panic("test exit")
	}

	loggerInfo = func(msg interface{}, fields ...zap.Field) {}
	loggerDebug = func(msg interface{}, fields ...zap.Field) {}
	loggerInfof = func(format string, args ...interface{}) {}

	osGetenv = func(key string) string {
		return "8080"
	}

	defer func() {
		recover()
	}()

	Run(context.Background())

	assert.True(t, fatalCalled, "Fatal should have been called")
}

// TestRunRedisSetPostgresError tests Redis set error after Postgres setup
func TestRunRedisSetPostgresError(t *testing.T) {
	origHealthzNewServer := healthzNewServer
	origConnectGetReleaseId := connectGetReleaseId
	origConnectRedis := connectRedis
	origConnectPubSub := connectPubSub
	origConnectBigQuery := connectBigQuery
	origConnectPostgres := connectPostgres
	origConnectRedisSet := connectRedisSet
	origSetupPubSub := setupPubSub
	origSetupBigQuery := setupBigQuery
	origSetupPostgres := setupPostgres
	origLoggerFatalf := loggerFatalf
	origLoggerInfo := loggerInfo
	origLoggerDebug := loggerDebug
	origLoggerInfof := loggerInfof
	origOsGetenv := osGetenv

	defer func() {
		healthzNewServer = origHealthzNewServer
		connectGetReleaseId = origConnectGetReleaseId
		connectRedis = origConnectRedis
		connectPubSub = origConnectPubSub
		connectBigQuery = origConnectBigQuery
		connectPostgres = origConnectPostgres
		connectRedisSet = origConnectRedisSet
		setupPubSub = origSetupPubSub
		setupBigQuery = origSetupBigQuery
		setupPostgres = origSetupPostgres
		loggerFatalf = origLoggerFatalf
		loggerInfo = origLoggerInfo
		loggerDebug = origLoggerDebug
		loggerInfof = origLoggerInfof
		osGetenv = origOsGetenv
	}()

	healthzNewServer = func(port string) healthz.HealthzServer {
		return &mockHealthzServer{}
	}

	connectGetReleaseId = func() (string, error) {
		return "test-release", nil
	}

	connectRedis = func(ctx context.Context) (*redis.Client, error) {
		return &redis.Client{}, nil
	}

	connectPubSub = func(ctx context.Context) (connect.PsClient, error) {
		return &mocks.FakePubsubClient{}, nil
	}

	connectBigQuery = func(ctx context.Context, config *connect.DatabaseConfig, factory connect.BQClientFactory) (*connect.BigQueryExecutor, error) {
		return &connect.BigQueryExecutor{}, nil
	}

	connectPostgres = func(ctx context.Context, config *connect.DatabaseConfig) (*connect.PostgresExecutor, error) {
		return &connect.PostgresExecutor{}, nil
	}

	callCount := 0
	connectRedisSet = func(ctx context.Context, client *redis.Client, key, value string) error {
		callCount++
		if callCount <= 2 {
			return nil // First two calls (PubSub, BigQuery) succeed
		}
		return errors.New("redis set postgres error") // Third call (Postgres) fails
	}

	setupPubSub = func(ctx context.Context, client connect.PsClient) error {
		return nil
	}

	setupBigQuery = func(bq *connect.BigQueryExecutor, query string) error {
		return nil
	}

	setupPostgres = func(pg *connect.PostgresExecutor, query string) error {
		return nil
	}

	var fatalCalled bool
	loggerFatalf = func(format string, args ...interface{}) {
		fatalCalled = true
		panic("test exit")
	}

	loggerInfo = func(msg interface{}, fields ...zap.Field) {}
	loggerDebug = func(msg interface{}, fields ...zap.Field) {}
	loggerInfof = func(format string, args ...interface{}) {}

	osGetenv = func(key string) string {
		return "8080"
	}

	defer func() {
		recover()
	}()

	Run(context.Background())

	assert.True(t, fatalCalled, "Fatal should have been called")
}

// TestHealthServerShutdownError tests health server shutdown error handling
func TestHealthServerShutdownError(t *testing.T) {
	origHealthzNewServer := healthzNewServer
	origConnectGetReleaseId := connectGetReleaseId
	origLoggerFatalf := loggerFatalf
	origLoggerInfo := loggerInfo
	origLoggerDebug := loggerDebug
	origOsGetenv := osGetenv

	defer func() {
		healthzNewServer = origHealthzNewServer
		connectGetReleaseId = origConnectGetReleaseId
		loggerFatalf = origLoggerFatalf
		loggerInfo = origLoggerInfo
		loggerDebug = origLoggerDebug
		osGetenv = origOsGetenv
	}()

	healthzNewServer = func(port string) healthz.HealthzServer {
		return &mockHealthzServerWithShutdownError{}
	}

	connectGetReleaseId = func() (string, error) {
		return "", errors.New("quick exit") // Trigger early exit
	}

	var fatalCalled bool
	loggerFatalf = func(format string, args ...interface{}) {
		fatalCalled = true
		panic("test exit")
	}

	var debugMessages []string
	loggerInfo = func(msg interface{}, fields ...zap.Field) {}
	loggerDebug = func(msg interface{}, fields ...zap.Field) {
		if len(fields) > 0 {
			debugMessages = append(debugMessages, "Health server shutdown error")
		}
	}

	osGetenv = func(key string) string {
		return "8080"
	}

	defer func() {
		recover()
	}()

	Run(context.Background())

	assert.True(t, fatalCalled, "Fatal should have been called")
	assert.Contains(t, debugMessages, "Health server shutdown error")
}

// mockHealthzServer implements healthz.HealthzServer for testing
type mockHealthzServer struct {
	listenErr error
}

func (m *mockHealthzServer) ListenAndServe() error {
	return m.listenErr
}

func (m *mockHealthzServer) Shutdown(ctx context.Context) error {
	return nil
}

func (m *mockHealthzServer) SetBootComplete()                        {}
func (m *mockHealthzServer) SetReady()                               {}
func (m *mockHealthzServer) SetNotReady()                            {}
func (m *mockHealthzServer) SetCustomReadinessCheck(fn func() error) {}

// mockHealthzServerWithShutdownError implements healthz.HealthzServer with shutdown error
type mockHealthzServerWithShutdownError struct {
	mockHealthzServer
}

func (m *mockHealthzServerWithShutdownError) Shutdown(ctx context.Context) error {
	return errors.New("shutdown error")
}
