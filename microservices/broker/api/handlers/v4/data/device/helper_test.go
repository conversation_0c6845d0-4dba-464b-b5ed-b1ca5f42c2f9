package device

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
)

// TestGetEDIInitialState tests the getEDIInitialState function
func TestGetEDIInitialState(t *testing.T) {
	tests := []struct {
		name     string
		device   pgDeviceInfo
		expected DeviceState
	}{
		{
			name: "disabled device returns nevercomm",
			device: pgDeviceInfo{
				IsEnabled: false,
			},
			expected: DeviceStateNeverComm,
		},
		{
			name: "enabled device returns error",
			device: pgDeviceInfo{
				IsEnabled: true,
			},
			expected: DeviceStateError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getEDIInitialState(tt.device)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestGetEDIStateFromRMS tests the getEDIStateFromRMS function
func TestGetEDIStateFromRMS(t *testing.T) {
	tests := []struct {
		name      string
		isFaulted bool
		expected  DeviceState
	}{
		{
			name:      "faulted device returns faulted state",
			isFaulted: true,
			expected:  DeviceStateFaulted,
		},
		{
			name:      "non-faulted device returns no fault state",
			isFaulted: false,
			expected:  DeviceStateNoFault,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getEDIStateFromRMS(tt.isFaulted)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestGetDeviceFlag tests the getDeviceFlag function
func TestGetDeviceFlag(t *testing.T) {
	validFlag := "test-flag"
	emptyFlag := ""

	tests := []struct {
		name     string
		dbFlag   *string
		expected string
	}{
		{
			name:     "nil pointer returns empty string",
			dbFlag:   nil,
			expected: "",
		},
		{
			name:     "empty string returns empty string",
			dbFlag:   &emptyFlag,
			expected: "",
		},
		{
			name:     "valid string returns the string",
			dbFlag:   &validFlag,
			expected: "test-flag",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getDeviceFlag(tt.dbFlag)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestConvertPgDeviceInfos tests the convertPgDeviceInfos function
func TestConvertPgDeviceInfos(t *testing.T) {
	now := time.Now()
	deviceFlag := "test-flag"

	tests := []struct {
		name     string
		dbInfo   *[]pgDeviceInfo
		expected *[]dataPayload
	}{
		{
			name:     "nil input returns nil",
			dbInfo:   nil,
			expected: nil,
		},
		{
			name:     "empty slice returns empty slice",
			dbInfo:   &[]pgDeviceInfo{},
			expected: &[]dataPayload{},
		},
		{
			name: "EDI_LEGACY device type",
			dbInfo: &[]pgDeviceInfo{
				{
					ID:                  1,
					DeviceID:            "device-1",
					MonitorID:           123,
					MonitorName:         "Test Monitor",
					DeviceType:          "EDI_LEGACY",
					IPAddress:           "***********",
					Port:                "8080",
					DateUploadedUTC:     now,
					Fault:               "Test Fault",
					MonitorTime:         now,
					EngineVersion:       "Engine 1.0",
					EngineRevision:      "Rev 1",
					ChannelRedStatus:    []bool{true, false},
					ChannelYellowStatus: []bool{true},
					ChannelGreenStatus:  []bool{false, true, false},
					GatewayVersion:      "Gateway 1.0",
					Latitude:            "40.7128",
					Longitude:           "-74.0060",
					DeviceFlag:          &deviceFlag,
					IsEnabled:           true, // Device is enabled
				},
			},
			expected: &[]dataPayload{
				{
					DeviceIdentifier:   "device-1",
					ApplicationVersion: "Gateway 1.0",
					Location: location{
						Latitude:  "40.7128",
						Longitude: "-74.0060",
					},
					Status: deviceStatus{
						State:                DeviceStateError, // Initial state for enabled device
						HeartbeatReceivedUTC: "",
					},
					DeviceInfo: deviceInfo{
						Manufacturer: "EDI",
						DeviceType:   "EDI_LEGACY",
						DeviceFlag:   "test-flag",
						Brand: brandInfo{
							EDI: &ediInfo{
								IPAddress: "***********",
								Port:      "8080",
								Model:     "",
								Legacy: &ediLegacy{
									DeviceID:                 1,
									UserAssignedDeviceID:     "123",
									UserAssignedDeviceName:   "Test Monitor",
									FirmwareType:             "",
									FirmwareVersion:          "",
									LogUploadedUTC:           now.Format(time.RFC3339),
									LastFaultReason:          "Test Fault",
									LastFaultUploadedUTC:     now.Format(time.RFC3339),
									CommVersion:              "",
									RmsEngineFirmwareType:    "Engine 1.0",
									RmsEngineFirmwareVersion: "Rev 1",
									FaultedChannelStatus: channelStatus{
										ChannelRed:    []bool{true, false},
										ChannelYellow: []bool{true},
										ChannelGreen:  []bool{false, true, false},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name: "EDI_NEXT_GEN device type",
			dbInfo: &[]pgDeviceInfo{
				{
					ID:           2,
					DeviceID:     "device-2",
					MonitorName:  "Next Gen Monitor",
					MonitorID:    123,
					DeviceType:   "EDI_NEXT_GEN",
					IPAddress:    "***********",
					Port:         "8081",
					SerialNumber: "SN123456",
					DeviceFlag:   nil,  // Test nil device flag
					IsEnabled:    true, // Device is enabled
				},
			},
			expected: &[]dataPayload{
				{
					DeviceIdentifier: "device-2",
					Status: deviceStatus{
						State: DeviceStateError,
					},
					DeviceInfo: deviceInfo{
						Manufacturer: "EDI",
						DeviceType:   "EDI_NEXT_GEN",
						DeviceFlag:   "", // Empty string for nil flag
						Brand: brandInfo{
							EDI: &ediInfo{
								IPAddress: "***********",
								Port:      "8081",
								Model:     "Next Gen MMU",
								NextGen: &ediNextGen{
									SerialNumber: "SN123456",
									MonitorID:    "Next Gen Monitor",
									UserID:       "123",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "disabled EDI_LEGACY device",
			dbInfo: &[]pgDeviceInfo{
				{
					ID:         3,
					DeviceID:   "device-3",
					DeviceType: "EDI_LEGACY",
					DeviceFlag: nil,
					IsEnabled:  false, // Device is disabled
				},
			},
			expected: &[]dataPayload{
				{
					DeviceIdentifier: "device-3",
					Status: deviceStatus{
						State: DeviceStateNeverComm, // Disabled device should be nevercomm
					},
					DeviceInfo: deviceInfo{
						Manufacturer: "EDI",
						DeviceType:   "EDI_LEGACY",
						DeviceFlag:   "",
						Brand: brandInfo{
							EDI: &ediInfo{
								Legacy: &ediLegacy{
									DeviceID:                 3,
									UserAssignedDeviceID:     "0",
									UserAssignedDeviceName:   "",
									LogUploadedUTC:           time.Time{}.Format(time.RFC3339),
									LastFaultReason:          "",
									LastFaultUploadedUTC:     time.Time{}.Format(time.RFC3339),
									RmsEngineFirmwareType:    "",
									RmsEngineFirmwareVersion: "",
									FaultedChannelStatus: channelStatus{
										ChannelRed:    []bool{},
										ChannelYellow: []bool{},
										ChannelGreen:  []bool{},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name: "unknown device type",
			dbInfo: &[]pgDeviceInfo{
				{
					ID:         4,
					DeviceID:   "device-4",
					DeviceType: "UNKNOWN_TYPE",
					DeviceFlag: nil,
					IsEnabled:  true, // Enabled but unknown type
				},
			},
			expected: &[]dataPayload{
				{
					DeviceIdentifier: "device-4",
					Status: deviceStatus{
						State: DeviceStateError,
					},
					DeviceInfo: deviceInfo{
						Manufacturer: "Unknown",
						DeviceType:   "UNKNOWN_TYPE",
						DeviceFlag:   "",
						Brand:        brandInfo{},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertPgDeviceInfos(tt.dbInfo)

			if tt.expected == nil {
				assert.Nil(t, result)
				return
			}

			assert.NotNil(t, result)
			assert.Equal(t, len(*tt.expected), len(*result))

			for i, expectedPayload := range *tt.expected {
				actualPayload := (*result)[i]

				assert.Equal(t, expectedPayload.DeviceIdentifier, actualPayload.DeviceIdentifier)
				assert.Equal(t, expectedPayload.DeviceInfo.Manufacturer, actualPayload.DeviceInfo.Manufacturer)
				assert.Equal(t, expectedPayload.DeviceInfo.DeviceType, actualPayload.DeviceInfo.DeviceType)
				assert.Equal(t, expectedPayload.DeviceInfo.DeviceFlag, actualPayload.DeviceInfo.DeviceFlag)
				assert.Equal(t, expectedPayload.Status.State, actualPayload.Status.State)

				// Test EDI-specific fields if present
				if expectedPayload.DeviceInfo.Brand.EDI != nil {
					assert.NotNil(t, actualPayload.DeviceInfo.Brand.EDI)
					assert.Equal(t, expectedPayload.DeviceInfo.Brand.EDI.IPAddress, actualPayload.DeviceInfo.Brand.EDI.IPAddress)
					assert.Equal(t, expectedPayload.DeviceInfo.Brand.EDI.Port, actualPayload.DeviceInfo.Brand.EDI.Port)
					assert.Equal(t, expectedPayload.DeviceInfo.Brand.EDI.Model, actualPayload.DeviceInfo.Brand.EDI.Model)

					if expectedPayload.DeviceInfo.Brand.EDI.Legacy != nil {
						assert.NotNil(t, actualPayload.DeviceInfo.Brand.EDI.Legacy)
						assert.Equal(t, expectedPayload.DeviceInfo.Brand.EDI.Legacy.DeviceID, actualPayload.DeviceInfo.Brand.EDI.Legacy.DeviceID)
						assert.Equal(t, expectedPayload.DeviceInfo.Brand.EDI.Legacy.UserAssignedDeviceID, actualPayload.DeviceInfo.Brand.EDI.Legacy.UserAssignedDeviceID)
						assert.Equal(t, expectedPayload.DeviceInfo.Brand.EDI.Legacy.UserAssignedDeviceName, actualPayload.DeviceInfo.Brand.EDI.Legacy.UserAssignedDeviceName)
					}

					if expectedPayload.DeviceInfo.Brand.EDI.NextGen != nil {
						assert.NotNil(t, actualPayload.DeviceInfo.Brand.EDI.NextGen)
						assert.Equal(t, expectedPayload.DeviceInfo.Brand.EDI.NextGen.SerialNumber, actualPayload.DeviceInfo.Brand.EDI.NextGen.SerialNumber)
						assert.Equal(t, expectedPayload.DeviceInfo.Brand.EDI.NextGen.MonitorID, actualPayload.DeviceInfo.Brand.EDI.NextGen.MonitorID)
						assert.Equal(t, expectedPayload.DeviceInfo.Brand.EDI.NextGen.UserID, actualPayload.DeviceInfo.Brand.EDI.NextGen.UserID)
					}
				}
			}
		})
	}
}

// TestAddRedisToPayload tests the addRedisToPayload function
func TestAddRedisToPayload(t *testing.T) {
	now := time.Now()

	tests := []struct {
		name          string
		payload       *dataPayload
		header        *helper.HeaderRecord
		status        *helper.RmsStatusRecord
		expectedState DeviceState
		expectedModel string
	}{
		{
			name: "EDI Legacy device with faulted status",
			payload: &dataPayload{
				DeviceIdentifier: "device-1",
				Status: deviceStatus{
					State: DeviceStateError,
				},
				DeviceInfo: deviceInfo{
					DeviceType: "EDI_LEGACY",
					Brand: brandInfo{
						EDI: &ediInfo{
							Legacy: &ediLegacy{
								FirmwareType:    "",
								FirmwareVersion: "",
								CommVersion:     "",
							},
						},
					},
				},
			},
			header: &helper.HeaderRecord{
				FirmwareVersion:  "FW1.0",
				FirmwareRevision: "Rev1.0",
				CommVersion:      "Comm1.0",
			},
			status: &helper.RmsStatusRecord{
				MonitorTime: now,
				IsFaulted:   true,
				DeviceModel: "Test Model",
			},
			expectedState: DeviceStateFaulted,
			expectedModel: "Test Model",
		},
		{
			name: "EDI Legacy device with no fault status",
			payload: &dataPayload{
				DeviceIdentifier: "device-2",
				Status: deviceStatus{
					State: DeviceStateError,
				},
				DeviceInfo: deviceInfo{
					DeviceType: "EDI_LEGACY",
					Brand: brandInfo{
						EDI: &ediInfo{
							Legacy: &ediLegacy{
								FirmwareType:    "",
								FirmwareVersion: "",
								CommVersion:     "",
							},
						},
					},
				},
			},
			header: &helper.HeaderRecord{
				FirmwareVersion:  "FW2.0",
				FirmwareRevision: "Rev2.0",
				CommVersion:      "Comm2.0",
			},
			status: &helper.RmsStatusRecord{
				MonitorTime: now,
				IsFaulted:   false,
				DeviceModel: "Test Model 2",
			},
			expectedState: DeviceStateNoFault,
			expectedModel: "Test Model 2",
		},
		{
			name: "EDI Next Gen device",
			payload: &dataPayload{
				DeviceIdentifier: "device-3",
				Status: deviceStatus{
					State: DeviceStateError,
				},
				DeviceInfo: deviceInfo{
					DeviceType: "EDI_NEXT_GEN",
					Brand: brandInfo{
						EDI: &ediInfo{
							NextGen: &ediNextGen{
								SerialNumber: "SN123",
							},
						},
					},
				},
			},
			header: &helper.HeaderRecord{
				FirmwareVersion:  "FW3.0",
				FirmwareRevision: "Rev3.0",
				CommVersion:      "Comm3.0",
			},
			status: &helper.RmsStatusRecord{
				MonitorTime: now,
				IsFaulted:   false,
				DeviceModel: "Next Gen Model",
			},
			expectedState: DeviceStateNoFault,
			expectedModel: "Next Gen Model",
		},
		{
			name: "device with nil EDI brand",
			payload: &dataPayload{
				DeviceIdentifier: "device-4",
				Status: deviceStatus{
					State: DeviceStateError,
				},
				DeviceInfo: deviceInfo{
					DeviceType: "UNKNOWN",
					Brand:      brandInfo{},
				},
			},
			header: &helper.HeaderRecord{
				FirmwareVersion:  "FW4.0",
				FirmwareRevision: "Rev4.0",
				CommVersion:      "Comm4.0",
			},
			status: &helper.RmsStatusRecord{
				MonitorTime: now,
				IsFaulted:   false,
				DeviceModel: "Unknown Model",
			},
			expectedState: DeviceStateNoFault,
			expectedModel: "", // Model won't be set since EDI is nil
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Make a copy of the original payload to compare changes
			originalPayload := *tt.payload

			addRedisToPayload(tt.payload, tt.header, tt.status)

			// Verify status updates
			assert.Equal(t, tt.expectedState, tt.payload.Status.State)
			assert.Equal(t, now.UTC().Format(time.RFC3339), tt.payload.Status.HeartbeatReceivedUTC)

			// Verify EDI brand info updates
			if tt.payload.DeviceInfo.Brand.EDI != nil {
				assert.Equal(t, tt.expectedModel, tt.payload.DeviceInfo.Brand.EDI.Model)

				// Check Legacy-specific updates
				if tt.payload.DeviceInfo.DeviceType == "EDI_LEGACY" && tt.payload.DeviceInfo.Brand.EDI.Legacy != nil {
					assert.Equal(t, tt.header.FirmwareVersion, tt.payload.DeviceInfo.Brand.EDI.Legacy.FirmwareType)
					assert.Equal(t, tt.header.FirmwareRevision, tt.payload.DeviceInfo.Brand.EDI.Legacy.FirmwareVersion)
					assert.Equal(t, tt.header.CommVersion, tt.payload.DeviceInfo.Brand.EDI.Legacy.CommVersion)
				}
			} else {
				// If EDI brand is nil, model should remain unchanged
				assert.Equal(t, originalPayload.DeviceInfo.Brand, tt.payload.DeviceInfo.Brand)
			}
		})
	}
}

// TestDeviceStateString tests the String method of DeviceState
func TestDeviceStateString(t *testing.T) {
	tests := []struct {
		name     string
		state    DeviceState
		expected string
	}{
		{
			name:     "no fault state",
			state:    DeviceStateNoFault,
			expected: "nofault",
		},
		{
			name:     "faulted state",
			state:    DeviceStateFaulted,
			expected: "faulted",
		},
		{
			name:     "error state",
			state:    DeviceStateError,
			expected: "error",
		},
		{
			name:     "never comm state",
			state:    DeviceStateNeverComm,
			expected: "nevercomm",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.state.String()
			assert.Equal(t, tt.expected, result)
		})
	}
}
