package device

import (
	"fmt"
	"time"

	"synapse-its.com/shared/devices/edi/helper"
)

// getEDIInitialState determines the initial state of an EDI device based on database information
func getEDIInitialState(d pgDeviceInfo) DeviceState {
	// If device is disabled, it's in nevercomm state
	if !d.IsEnabled {
		return DeviceStateNeverComm
	}

	// If device is enabled but we don't have real-time data yet, default to error
	// This will be overridden by Redis data if the gateway is sending data
	return DeviceStateError
}

// getEDIStateFromRMS determines the EDI device state based on RMS fault status
func getEDIStateFromRMS(isFaulted bool) DeviceState {
	if isFaulted {
		return DeviceStateFaulted
	}
	return DeviceStateNoFault
}

// getDeviceFlag safely extracts device flag from nullable database field
func getDeviceFlag(dbFlag *string) string {
	if dbFlag != nil && *dbFlag != "" {
		return *dbFlag
	}
	return "" // Return empty string for null/empty values (will be omitted from JSON)
}

// Convert a slice of pgDeviceInfo to a slice of dataPayload.
func convertPgDeviceInfos(dbInfo *[]pgDeviceInfo) *[]dataPayload {
	// If the input pointer is nil, return nil
	if dbInfo == nil {
		return nil
	}
	infos := *dbInfo
	out := make([]dataPayload, 0, len(infos))
	for _, d := range infos {
		// Create device payload based on device type
		var devInfo deviceInfo
		var status deviceStatus

		switch d.DeviceType {
		case "EDI_LEGACY":
			devInfo = deviceInfo{
				Manufacturer: "EDI",
				DeviceType:   d.DeviceType,
				DeviceFlag:   getDeviceFlag(d.DeviceFlag), // Get device flag from database
				Brand: brandInfo{
					EDI: &ediInfo{
						IPAddress: d.IPAddress,
						Port:      d.Port,
						Model:     "", // Will be populated from Redis if available
						Legacy: &ediLegacy{
							DeviceID:                 d.ID,
							UserAssignedDeviceID:     fmt.Sprint(d.MonitorID),
							UserAssignedDeviceName:   d.MonitorName,
							FirmwareType:             "", // Will be populated from Redis
							FirmwareVersion:          "", // Will be populated from Redis
							LogUploadedUTC:           d.DateUploadedUTC.Format(time.RFC3339),
							LastFaultReason:          d.Fault,
							LastFaultUploadedUTC:     d.MonitorTime.Format(time.RFC3339),
							CommVersion:              "", // Will be populated from Redis
							RmsEngineFirmwareType:    d.EngineVersion,
							RmsEngineFirmwareVersion: d.EngineRevision,
							FaultedChannelStatus: channelStatus{
								ChannelRed:    d.ChannelRedStatus,
								ChannelYellow: d.ChannelYellowStatus,
								ChannelGreen:  d.ChannelGreenStatus,
							},
						},
						// NextGen left as nil - will not appear in JSON due to omitempty
					},
					// Polara and Carmanah left as nil - will not appear in JSON due to omitempty
				},
			}
			// EDI Legacy device status - initial state based on database info
			status = deviceStatus{
				State:                getEDIInitialState(d),
				HeartbeatReceivedUTC: "", // Will be populated from Redis
			}
		case "EDI_NEXT_GEN":
			devInfo = deviceInfo{
				Manufacturer: "EDI",
				DeviceType:   d.DeviceType,
				DeviceFlag:   getDeviceFlag(d.DeviceFlag), // Get device flag from database
				Brand: brandInfo{
					EDI: &ediInfo{
						IPAddress: d.IPAddress,
						Port:      d.Port,
						Model:     "Next Gen MMU", // TODO: Stubbed as there is no model number yet
						// Legacy left as nil - will not appear in JSON due to omitempty
						NextGen: &ediNextGen{
							SerialNumber: d.SerialNumber,
							MonitorID:    d.MonitorName,           // string: User defined string ID from device itself
							UserID:       fmt.Sprint(d.MonitorID), // int: User defined integer ID from device itself
						},
					},
					// Polara and Carmanah left as nil - will not appear in JSON due to omitempty
				},
			}
			// EDI Next-Gen device status - initial state based on database info
			status = deviceStatus{
				State:                getEDIInitialState(d),
				HeartbeatReceivedUTC: "", // Will be populated from Redis
			}
		default: // TODO: Add other device types here when defined.
			devInfo = deviceInfo{
				Manufacturer: "Unknown",
				DeviceType:   d.DeviceType,
				DeviceFlag:   getDeviceFlag(d.DeviceFlag), // Get device flag from database
				Brand:        brandInfo{
					// All brands omitted for unknown device types - will not appear in JSON due to omitempty
				},
			}
			// Unknown device types - default to error state
			status = deviceStatus{
				State:                DeviceStateError, // Unknown device types default to error
				HeartbeatReceivedUTC: "",               // Will be populated from Redis if available
			}
		}

		dp := dataPayload{
			DeviceIdentifier:   d.DeviceID,
			ApplicationVersion: d.GatewayVersion, // Gateway version device is connected to
			Location: location{
				Latitude:  d.Latitude,
				Longitude: d.Longitude,
			},
			Status:     status,
			DeviceInfo: devInfo,
		}
		out = append(out, dp)
	}
	return &out
}

func addRedisToPayload(dp *dataPayload, header *helper.HeaderRecord, status *helper.RmsStatusRecord) {
	// Update status information with real-time data from Redis
	dp.Status.HeartbeatReceivedUTC = status.MonitorTime.UTC().Format(time.RFC3339)

	// Update EDI device state based on RMS fault status
	dp.Status.State = getEDIStateFromRMS(status.IsFaulted)

	// Update EDI brand info if it exists
	if dp.DeviceInfo.Brand.EDI != nil {
		dp.DeviceInfo.Brand.EDI.Model = status.DeviceModel

		// Update device-type specific information
		switch dp.DeviceInfo.DeviceType {
		case "EDI_LEGACY":
			dp.DeviceInfo.Brand.EDI.Legacy.FirmwareType = header.FirmwareVersion
			dp.DeviceInfo.Brand.EDI.Legacy.FirmwareVersion = header.FirmwareRevision
			dp.DeviceInfo.Brand.EDI.Legacy.CommVersion = header.CommVersion
		}
		// For next-gen devices, we don't need to update firmware info as it's not part of the next-gen structure
	}
}
