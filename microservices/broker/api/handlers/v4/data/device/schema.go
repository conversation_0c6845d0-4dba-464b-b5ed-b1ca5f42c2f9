package device

import (
	"time"
)

// DeviceState represents the state of a device using enum-style pattern
type DeviceState string

// Device state enum values
const (
	// DeviceStateNoFault indicates the device is operating normally without any faults
	DeviceStateNoFault DeviceState = "nofault"

	// DeviceStateFaulted indicates the device has detected a fault condition
	DeviceStateFaulted DeviceState = "faulted"

	// DeviceStateError indicates there's an error communicating with the device or processing its data
	DeviceStateError DeviceState = "error"

	// DeviceStateNeverComm indicates the device has never communicated or is disabled
	DeviceStateNeverComm DeviceState = "nevercomm"
)

// String implements the Stringer interface for DeviceState
func (ds DeviceState) String() string {
	return string(ds)
}

type pgDeviceInfo struct {
	MonitorTime               time.Time `db:"monitortime"`
	Fault                     string    `db:"fault"`
	ChannelGreenStatus        []bool    `db:"channelgreenstatus"`
	ChannelYellowStatus       []bool    `db:"channelyellowstatus"`
	ChannelRedStatus          []bool    `db:"channelredstatus"`
	ID                        int       `db:"id"`
	DeviceID                  string    `db:"deviceid"`
	DeviceType                string    `db:"devicetype"`
	Latitude                  string    `db:"latitude"`
	Longitude                 string    `db:"longitude"`
	IPAddress                 string    `db:"ipaddress"`
	Port                      string    `db:"port"`
	MonitorID                 int       `db:"monitorid"`
	MonitorName               string    `db:"monitorname"`
	EngineVersion             string    `db:"engineversion"`
	EngineRevision            string    `db:"enginerevision"`
	DateUploadedUTC           time.Time `db:"dateuploadedutc"`
	SoftwareGatewayIdentifier string    `db:"softwaregatewayidentifier"`
	GatewayVersion            string    `db:"gatewayversion"`
	IsEnabled                 bool      `db:"isenabled"`
	SerialNumber              string    `db:"serialnumber"`
	DeviceFlag                *string   `db:"deviceflag"` // Nullable field from database
}

// dataPayload represents the complete device information response
type dataPayload struct {
	DeviceIdentifier   string       `json:"device_identifier" example:"device-uuid-123"` // Device UUID or identifier string
	ApplicationVersion string       `json:"application_version" example:"1.0.0"`         // Application version
	Location           location     `json:"location"`                                    // Geographic location information
	Status             deviceStatus `json:"status"`                                      // Current device status and fault information
	DeviceInfo         deviceInfo   `json:"device_info"`                                 // Device hardware and software information
}

// location represents the geographic coordinates of the device
type location struct {
	Latitude  string `json:"latitude" example:"40.7128"`   // Device latitude coordinate
	Longitude string `json:"longitude" example:"-74.0060"` // Device longitude coordinate
}

// deviceInfo contains hardware and software information about the device
type deviceInfo struct {
	Manufacturer string    `json:"manufacturer" example:"EDI"`            // Device manufacturer name
	DeviceType   string    `json:"device_type" example:"EDI_LEGACY"`      // Type classification of the device, this is the device type from the database
	DeviceFlag   string    `json:"device_flag,omitempty" example:"ALPHA"` // Device flag used for frontend behavior (omitted when empty)
	Brand        brandInfo `json:"brand"`                                 // Brand-specific device information
}

// brandInfo contains brand-specific device information
type brandInfo struct {
	EDI      *ediInfo     `json:"edi,omitempty"`      // EDI-specific device information
	Polara   *polaraInfo  `json:"polara,omitempty"`   // Polara-specific device information
	Carmanah *carmanaInfo `json:"carmanah,omitempty"` // Carmanah-specific device information
}

// ediInfo contains EDI-specific device information
type ediInfo struct {
	IPAddress string      `json:"ip_address" example:"*************"` // Device IP address
	Port      string      `json:"port" example:"8080"`                // Device communication port
	Model     string      `json:"model" example:"Next Gen MMU"`       // Device model identifier
	Legacy    *ediLegacy  `json:"legacy,omitempty"`                   // Legacy EDI device information
	NextGen   *ediNextGen `json:"next_gen,omitempty"`                 // Next generation EDI device information
}

// ediLegacy contains legacy EDI device-specific information
type ediLegacy struct {
	DeviceID                 int           `json:"device_id" example:"7006"`                               // Legacy device ID
	UserAssignedDeviceID     string        `json:"user_assigned_device_id" example:"1600"`                 // User-defined device identifier
	UserAssignedDeviceName   string        `json:"user_assigned_device_name" example:"Parker Rd"`          // User-friendly device name
	FirmwareType             string        `json:"firmware_type" example:"01"`                             // Firmware type classification
	FirmwareVersion          string        `json:"firmware_version" example:"7.5"`                         // Firmware version number
	LogUploadedUTC           string        `json:"log_uploaded_utc" example:"1970-01-01T00:00:00Z"`        // Last log upload timestamp
	LastFaultReason          string        `json:"last_fault_reason" example:""`                           // Last fault reason
	LastFaultUploadedUTC     string        `json:"last_fault_uploaded_utc" example:"1970-01-01T00:00:00Z"` // Last fault upload timestamp
	CommVersion              string        `json:"comm_version" example:"3.8"`                             // Communication protocol version
	RmsEngineFirmwareType    string        `json:"rms_engine_firmware_type" example:"01"`                  // RMS engine firmware type
	RmsEngineFirmwareVersion string        `json:"rms_engine_firmware_version" example:"3.1"`              // RMS engine firmware version
	FaultedChannelStatus     channelStatus `json:"faulted_channel_status"`                                 // Channel status during last fault
}

// ediNextGen contains next generation EDI device-specific information
type ediNextGen struct {
	SerialNumber string `json:"serial_number" example:"100011"`    // Device serial number
	MonitorID    string `json:"monitor_id" example:"4th and Main"` // Monitor ID/name
	UserID       string `json:"user_id" example:"11111111"`        // User ID
}

// polaraInfo contains Polara-specific device information
type polaraInfo struct {
	// This has not been defined yet
}

// carmanaInfo contains Carmanah-specific device information
type carmanaInfo struct {
	// This has not been defined yet
}

// deviceStatus contains current status and heartbeat information
type deviceStatus struct {
	State                DeviceState `json:"state" example:"nofault"`                               // Current device state: nofault (normal operation), faulted (device fault detected), error (communication/processing error), nevercomm (disabled or never communicated)
	HeartbeatReceivedUTC string      `json:"heartbeat_received_utc" example:"2024-01-15T10:30:00Z"` // Timestamp of last heartbeat received
}

// channelStatus represents the last faulted status of device channels
type channelStatus struct {
	ChannelRed    []bool `json:"red"`    // Red channel status array (true = on)
	ChannelYellow []bool `json:"yellow"` // Yellow channel status array (true = on)
	ChannelGreen  []bool `json:"green"`  // Green channel status array (true = on)
}
