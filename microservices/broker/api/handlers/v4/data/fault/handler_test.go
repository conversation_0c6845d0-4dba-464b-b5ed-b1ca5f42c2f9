package fault

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gorilla/mux"
	"synapse-its.com/broker/api/brokerShared"
	authorizer "synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	onramphelper "synapse-its.com/shared/rest/onramp/helper"
	"synapse-its.com/shared/schemas"

	"github.com/stretchr/testify/assert"
)

// Test UUIDs for organization scoping tests
const (
	testOrgID  = "550e8400-e29b-41d4-a716-************"
	testOrgID2 = "550e8400-e29b-41d4-a716-************"
)

// backup & restore our package-level overrides
var (
	origUserPermissionsFromContext = userPermissionsFromContext
	origGetPGDeviceDetail          = getPGDeviceDetail
	origGetBQLogs                  = getBQLogs
	origJSONMarshalIndent          = jsonMarshalIndentFn
	origJSONClean                  = jsonCleanFn
)

func teardown() {
	userPermissionsFromContext = origUserPermissionsFromContext
	getPGDeviceDetail = origGetPGDeviceDetail
	getBQLogs = origGetBQLogs
	jsonMarshalIndentFn = origJSONMarshalIndent
	jsonCleanFn = origJSONClean
}

// helper to create request with mux vars for organizationId
func newRequestWithOrgId(method, url, orgId string) *http.Request {
	req := httptest.NewRequest(method, url, nil)
	req = mux.SetURLVars(req, map[string]string{onramphelper.OrganizationIDParam: orgId})
	return req
}

// a tiny helper to grab HTTP status codes
func doRequest(t *testing.T, req *http.Request) *httptest.ResponseRecorder {
	w := httptest.NewRecorder()
	Handler(w, req)
	return w
}

func TestHandler_UserInfoMissing(t *testing.T) {
	defer teardown()
	// simulate no JWT info
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return nil, false
	}

	req := newRequestWithOrgId("GET", "/?deviceid=42", testOrgID)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_OrgIdMissing(t *testing.T) {
	defer teardown()
	// stub valid JWT
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{UserID: "1"}, true
	}

	// Create request WITHOUT organizationId in path
	req := httptest.NewRequest("GET", "/?deviceid=42", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestHandler_DeviceIdMissing(t *testing.T) {
	defer teardown()
	// stub valid JWT
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{UserID: "1"}, true
	}

	// Create request WITHOUT deviceid query param
	req := newRequestWithOrgId("GET", "/", testOrgID)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestHandler_DeviceIdInvalid(t *testing.T) {
	defer teardown()
	// stub valid JWT
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{UserID: "1"}, true
	}

	// Create request with invalid deviceid (not a valid UUID or integer)
	req := newRequestWithOrgId("GET", "/?deviceid=notavaliduuid", testOrgID)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestHandler_GetConnectionsError(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{UserID: "2"}, true
	}

	origGetConns := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return nil, errors.New("fail connect")
	}
	defer func() { connect.GetConnections = origGetConns }()

	req := newRequestWithOrgId("GET", "/?deviceid=550e8400-e29b-41d4-a716-************", testOrgID)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_CanAccessDeviceError(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{UserID: "3"}, true
	}

	// Create a fake database executor that will fail for CanAccessDevice
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			// Fail for CanAccessDevice query (getDeviceInfo)
			return errors.New("access error")
		},
	}

	// make GetConnections succeed with our fake DB
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	req := newRequestWithOrgId("GET", "/?deviceid=550e8400-e29b-41d4-a716-446655440777", testOrgID)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_CanAccessDeviceForbidden(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{UserID: "4"}, true
	}

	// Create a fake database executor that returns no device (empty DeviceID)
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			// For CanAccessDevice query (getDeviceInfo), return no error but don't populate
			// This will result in empty DeviceID which means no access
			return nil
		},
	}

	// make GetConnections succeed with our fake DB
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	req := newRequestWithOrgId("GET", "/?deviceid=550e8400-e29b-41d4-a716-446655440888", testOrgID)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusForbidden, w.Code)
}

func TestHandler_PGDeviceDetailError(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "5",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        testOrgID,
					OrganizationID: testOrgID,
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}

	callCount := 0
	// Create a fake database executor that succeeds for CanAccessDevice but fails for getPGDeviceDetail
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			callCount++
			if callCount == 1 {
				// First call is CanAccessDevice (getDeviceInfo) - populate with device info
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "550e8400-e29b-41d4-a716-446655440999"
					deviceInfo.OrganizationID = testOrgID
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// Second call is getPGDeviceDetail - return an error
			return errors.New("db error")
		},
	}

	// make GetConnections succeed with our fake DB
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	req := newRequestWithOrgId("GET", "/?deviceid=550e8400-e29b-41d4-a716-446655440999", testOrgID)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_BQLogsError(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "4",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        testOrgID,
					OrganizationID: testOrgID,
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}

	callCount := 0
	// Create a fake database executor that succeeds for CanAccessDevice and getPGDeviceDetail
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			callCount++
			if callCount == 1 {
				// First call is CanAccessDevice (getDeviceInfo)
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "550e8400-e29b-41d4-a716-446655440888"
					deviceInfo.OrganizationID = testOrgID
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// Second call is getPGDeviceDetail - succeed
			if pgDetail, ok := dest.(*brokerShared.PGDeviceDetail); ok {
				pgDetail.DeviceID = "dev"
				pgDetail.SoftWareGateWayID = "gw"
				pgDetail.OrganizationID = "org"
			}
			return nil
		},
	}

	// make GetConnections & PG succeed
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	// stub BQ to error
	getBQLogs = func(bq connect.BigQueryExecutorInterface, deviceID, orgID string, filterDays ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{}, errors.New("bq error")
	}

	req := newRequestWithOrgId("GET", "/?deviceid=550e8400-e29b-41d4-a716-446655440888", testOrgID)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_PGDeviceDetail_WrongOrganization(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "5",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        testOrgID2,
					OrganizationID: testOrgID2,
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}

	callCount := 0
	// Create a fake database that allows access but device belongs to different org
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			callCount++
			if callCount == 1 {
				// First call is CanAccessDevice - allow access
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "550e8400-e29b-41d4-a716-446655440123"
					deviceInfo.OrganizationID = testOrgID2
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// Second call is getPGDeviceDetail with org filter - no rows found because org doesn't match
			return errors.New("no rows found")
		},
	}

	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	req := newRequestWithOrgId("GET", "/?deviceid=550e8400-e29b-41d4-a716-446655440123", testOrgID2)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_Success_DefaultSchema(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "5",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        testOrgID,
					OrganizationID: testOrgID,
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}

	callCount := 0
	// Create a fake database executor that succeeds for CanAccessDevice and getPGDeviceDetail
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			callCount++
			if callCount == 1 {
				// First call is CanAccessDevice
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "550e8400-e29b-41d4-a716-446655440999"
					deviceInfo.OrganizationID = testOrgID
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// Second call is getPGDeviceDetail - succeed
			if pgDetail, ok := dest.(*brokerShared.PGDeviceDetail); ok {
				pgDetail.DeviceID = "dev"
				pgDetail.SoftWareGateWayID = "gw"
				pgDetail.OrganizationID = "org"
				pgDetail.MonitorID = 11
				pgDetail.MonitorName = "X"
				pgDetail.RMSEngineFirmwareType = "1"
				pgDetail.RMSEngineFirmwareVersion = "2"
			}
			return nil
		},
	}

	// stub connect & PG
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	// stub BQ to return zero-valued AllLogs with a Model that maps via default case
	getBQLogs = func(
		bq connect.BigQueryExecutorInterface,
		deviceID, orgID string,
		filterDays ...int,
	) (schemas.AllLogs, error) {
		return schemas.AllLogs{
			FaultLogs: schemas.FaultLogsWithModel{
				Model:              int64(99999), // hits default -> 16leip
				DeviceModel:        "",
				FirmwareReVision:   "0",
				FirmwareVersion:    "0",
				MonitorCommVersion: "0",
				PubsubTimestamp:    time.Time{}, // zero-value
				RawLogMessages:     schemas.RawLogMessages{},
			},
		}, nil
	}

	req := newRequestWithOrgId("GET", "/?deviceid=550e8400-e29b-41d4-a716-446655440999", testOrgID)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusOK, w.Code)
}

func TestHandler_Success_ECL2010FPlusSchema(t *testing.T) {
	defer teardown()

	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "100",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "orgID",
					OrganizationID: "orgID",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}

	callCount := 0
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			callCount++
			if callCount == 1 {
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "550e8400-e29b-41d4-a716-446655440555"
					deviceInfo.OrganizationID = "orgID"
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			if pgDetail, ok := dest.(*brokerShared.PGDeviceDetail); ok {
				pgDetail.DeviceID = "devID"
				pgDetail.SoftWareGateWayID = "gwID"
				pgDetail.OrganizationID = "orgID"
				pgDetail.MonitorID = 7
				pgDetail.MonitorName = "MyMon"
				pgDetail.RMSEngineFirmwareType = "9"
				pgDetail.RMSEngineFirmwareVersion = "10"
			}
			return nil
		},
	}

	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	getBQLogs = func(bq connect.BigQueryExecutorInterface, deviceID, orgID string, filterDays ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{
			FaultLogs: schemas.FaultLogsWithModel{
				Model:              int64(3), // ECL2010 model (edihelper.Ecl2010 = 3)
				DeviceModel:        "MyModel",
				FirmwareReVision:   "2",
				FirmwareVersion:    "3",
				MonitorCommVersion: "4",
				PubsubTimestamp:    time.Now(),
				RawLogMessages:     schemas.RawLogMessages{},
			},
		}, nil
	}

	req := newRequestWithOrgId("GET", "/?deviceid=550e8400-e29b-41d4-a716-446655440555", testOrgID)
	w := doRequest(t, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), `"model":"MyModel"`)
	assert.Contains(t, w.Body.String(), `"schema":"fault_ecl2010_fplus.proto"`)
}

func TestHandler_Success_CMU2212BaseSchema(t *testing.T) {
	defer teardown()

	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "101",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "orgID2",
					OrganizationID: "orgID2",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}

	callCount := 0
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			callCount++
			if callCount == 1 {
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "550e8400-e29b-41d4-a716-446655440777"
					deviceInfo.OrganizationID = "orgID2"
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			if pgDetail, ok := dest.(*brokerShared.PGDeviceDetail); ok {
				pgDetail.DeviceID = "devID2"
				pgDetail.SoftWareGateWayID = "gwID2"
				pgDetail.OrganizationID = "orgID2"
				pgDetail.MonitorID = 8
				pgDetail.MonitorName = "YourMon"
				pgDetail.RMSEngineFirmwareType = "11"
				pgDetail.RMSEngineFirmwareVersion = "12"
			}
			return nil
		},
	}

	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	getBQLogs = func(bq connect.BigQueryExecutorInterface, deviceID, orgID string, filterDays ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{
			FaultLogs: schemas.FaultLogsWithModel{
				Model:              int64(51), // CMUip2212_hv model (edihelper.CMUip2212_hv = 51)
				DeviceModel:        "YourModel",
				FirmwareReVision:   "5",
				FirmwareVersion:    "6",
				MonitorCommVersion: "7",
				PubsubTimestamp:    time.Now(),
				RawLogMessages:     schemas.RawLogMessages{},
			},
		}, nil
	}

	req := newRequestWithOrgId("GET", "/?deviceid=550e8400-e29b-41d4-a716-446655440777", testOrgID2)
	w := doRequest(t, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), `"model":"YourModel"`)
	assert.Contains(t, w.Body.String(), `"schema":"fault_cmu2212_base.proto"`)
}

func TestHandler_JSONMarshalError(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "200",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "org-123",
					OrganizationID: "org-123",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}

	callCount := 0
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			callCount++
			if callCount == 1 {
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "550e8400-e29b-41d4-a716-************"
					deviceInfo.OrganizationID = "org-123"
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			if pgDetail, ok := dest.(*brokerShared.PGDeviceDetail); ok {
				pgDetail.DeviceID = "d"
				pgDetail.SoftWareGateWayID = "g"
				pgDetail.MonitorID = 0
				pgDetail.MonitorName = ""
				pgDetail.RMSEngineFirmwareType = "0"
				pgDetail.RMSEngineFirmwareVersion = "0"
				pgDetail.OrganizationID = "org-123"
			}
			return nil
		},
	}

	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	getBQLogs = func(bq connect.BigQueryExecutorInterface, did, oid string, f ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{FaultLogs: schemas.FaultLogsWithModel{Model: int64(11)}}, nil // Mmu16le = 11
	}

	// force JSON-marshal error
	jsonMarshalIndentFn = func(v interface{}, prefix, indent string) ([]byte, error) {
		return nil, errors.New("marshal fail")
	}

	w := doRequest(t, newRequestWithOrgId("GET", "/?deviceid=550e8400-e29b-41d4-a716-************", testOrgID))
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_JSONCleanError(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "201",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "org-123",
					OrganizationID: "org-123",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}

	callCount := 0
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			callCount++
			if callCount == 1 {
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "550e8400-e29b-41d4-a716-446655440002"
					deviceInfo.OrganizationID = "org-123"
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			if pgDetail, ok := dest.(*brokerShared.PGDeviceDetail); ok {
				pgDetail.DeviceID = "d"
				pgDetail.SoftWareGateWayID = "g"
				pgDetail.MonitorID = 0
				pgDetail.MonitorName = ""
				pgDetail.RMSEngineFirmwareType = "0"
				pgDetail.RMSEngineFirmwareVersion = "0"
				pgDetail.OrganizationID = "org-123"
			}
			return nil
		},
	}

	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	getBQLogs = func(bq connect.BigQueryExecutorInterface, did, oid string, f ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{FaultLogs: schemas.FaultLogsWithModel{Model: int64(11)}}, nil // Mmu16le = 11
	}

	// JSON-marshal works normally
	jsonMarshalIndentFn = origJSONMarshalIndent
	// force JSON-clean error
	jsonCleanFn = func(raw []byte) ([]byte, error) {
		return nil, errors.New("clean fail")
	}

	w := doRequest(t, newRequestWithOrgId("GET", "/?deviceid=550e8400-e29b-41d4-a716-446655440002", testOrgID))
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_BQLogsInternalError(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "6",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        testOrgID2,
					OrganizationID: testOrgID2,
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}

	callCount := 0
	// Create a fake database executor that succeeds for CanAccessDevice and getPGDeviceDetail
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			callCount++
			if callCount == 1 {
				// First call is CanAccessDevice
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "550e8400-e29b-41d4-a716-446655440778"
					deviceInfo.OrganizationID = testOrgID2
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// Second call is getPGDeviceDetail - succeed
			if pgDetail, ok := dest.(*brokerShared.PGDeviceDetail); ok {
				pgDetail.DeviceID = "dev-777"
				pgDetail.SoftWareGateWayID = "gw-777"
				pgDetail.OrganizationID = testOrgID2
			}
			return nil
		},
	}

	// Create a fake BigQuery executor that returns an error
	fakeBQ := &mocks.FakeBigQueryExecutor{}
	fakeBQ.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
		return errors.New("bigquery internal error")
	}

	// make GetConnections succeed with our fake DB and BQ
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	mockConnections.Bigquery = fakeBQ
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	// DON'T override getBQLogs - we want to test the real function's error handling

	req := newRequestWithOrgId("GET", "/?deviceid=550e8400-e29b-41d4-a716-446655440778", testOrgID2)
	w := doRequest(t, req)

	// The getBQLogs function returns the error, causing the handler to return 500
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_BQLogsWithCustomDays(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "6",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "org-123",
					OrganizationID: "org-123",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}

	callCount := 0
	// Create a fake database executor that succeeds for CanAccessDevice and getPGDeviceDetail
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			callCount++
			if callCount == 1 {
				// First call is CanAccessDevice
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "550e8400-e29b-41d4-a716-446655440779"
					deviceInfo.OrganizationID = "org-123"
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// Second call is getPGDeviceDetail - succeed
			if pgDetail, ok := dest.(*brokerShared.PGDeviceDetail); ok {
				pgDetail.DeviceID = "dev-777"
				pgDetail.SoftWareGateWayID = "gw-777"
				pgDetail.OrganizationID = "org-777"
			}
			return nil
		},
	}

	// Create a fake BigQuery executor that succeeds and verifies the custom days parameter
	fakeBQ := &mocks.FakeBigQueryExecutor{}
	fakeBQ.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
		// Verify that the first argument (days) is our custom value
		if len(args) > 0 {
			if days, ok := args[0].(int); ok && days == 7 {
				// This confirms the filterDays parameter was used
				t.Logf("Custom days parameter %d was used correctly", days)
			}
		}
		// Populate with some dummy data
		if allLogs, ok := dest.(*schemas.AllLogs); ok {
			allLogs.FaultLogs.Model = 3 // ECL2010 model
		}
		return nil
	}

	// Override getBQLogs to use custom days parameter
	origGetBQLogs := getBQLogs
	getBQLogs = func(bq connect.BigQueryExecutorInterface, deviceID string, orgID string, filterDays ...int) (schemas.AllLogs, error) {
		// Call the original function with custom days parameter (7 days instead of default 90)
		return origGetBQLogs(bq, deviceID, orgID, 7)
	}
	defer func() { getBQLogs = origGetBQLogs }()

	// make GetConnections succeed with our fake DB and BQ
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	mockConnections.Bigquery = fakeBQ
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	req := newRequestWithOrgId("GET", "/?deviceid=550e8400-e29b-41d4-a716-446655440779", testOrgID2)
	w := doRequest(t, req)

	// Should succeed with ECL2010 schema
	assert.Equal(t, http.StatusOK, w.Code)
}
