package instruction

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
	onramphelper "synapse-its.com/shared/rest/onramp/helper"
)

// Test UUIDs for organization scoping tests
const (
	testOrgID    = "550e8400-e29b-41d4-a716-************"
	testOrgID2   = "550e8400-e29b-41d4-a716-************"
	testDeviceID = "550e8400-e29b-41d4-a716-************"
)

// FakeResult implements sql.Result for testing
type FakeResult struct {
	AffectedRows    int64
	RowsAffectedErr error
}

func (r *FakeResult) LastInsertId() (int64, error) {
	return 0, nil
}

func (r *FakeResult) RowsAffected() (int64, error) {
	if r.RowsAffectedErr != nil {
		return 0, r.RowsAffectedErr
	}
	return r.AffectedRows, nil
}

// UserPermissionsInterface defines the interface for user permissions to allow mocking
type UserPermissionsInterface interface {
	CanAccessDevice(db connect.DatabaseExecutor, deviceID string, requiredPermissions ...string) (bool, error)
}

// MockUserPermissions wraps UserPermissions to allow mocking CanAccessDevice
type MockUserPermissions struct {
	*authorizer.UserPermissions
	MockCanAccessDevice func(db connect.DatabaseExecutor, deviceID string, requiredPermissions ...string) (bool, error)
}

func (m *MockUserPermissions) CanAccessDevice(db connect.DatabaseExecutor, deviceID string, requiredPermissions ...string) (bool, error) {
	if m.MockCanAccessDevice != nil {
		return m.MockCanAccessDevice(db, deviceID, requiredPermissions...)
	}
	// Default behavior if no mock is set
	return m.UserPermissions.CanAccessDevice(db, deviceID, requiredPermissions...)
}

// Helper to create request with organization ID in path
func newRequestWithOrgId(method, path, orgId, body string) *http.Request {
	var reqBody *strings.Reader
	if body != "" {
		reqBody = strings.NewReader(body)
	}
	req := httptest.NewRequest(method, path, reqBody)
	req = mux.SetURLVars(req, map[string]string{onramphelper.OrganizationIDParam: orgId})
	return req
}

func Test_HandlerWithDeps(t *testing.T) {
	validRequestBody := fmt.Sprintf(`{"instruction": "get_device_logs", "device_id": "%s"}`, testDeviceID)
	invalidInstructionBody := fmt.Sprintf(`{"instruction": "invalid_instruction", "device_id": "%s"}`, testDeviceID)
	invalidJSONBody := `{"instruction": "get_device_logs", "device_id": "`
	invalidDeviceIDBody := `{"instruction": "get_device_logs", "device_id": {"invalid": "object"}}`

	t.Parallel()

	tests := []struct {
		name                    string
		setupMocks              func(*dbexecutor.FakeDBExecutor)
		setupUserPermissions    func() *MockUserPermissions
		requestBody             string
		orgId                   string
		mockGetConnectionsError bool
		expectedStatus          int
	}{
		{
			name: "successful_instruction_insertion",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// Mock for CanAccessDevice's getDeviceInfo query
				mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
						deviceInfo.DeviceID = testDeviceID
						deviceInfo.OrganizationID = testOrgID
						deviceInfo.DeviceGroupIDs = []string{}
						deviceInfo.LocationGroupIDs = []string{}
					}
					return nil
				}
				// Mock for insertInstruction
				mdb.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &FakeResult{AffectedRows: 1}, nil
				}
			},
			setupUserPermissions: func() *MockUserPermissions {
				return &MockUserPermissions{
					UserPermissions: &authorizer.UserPermissions{
						UserID: "test-user-id",
						Permissions: []authorizer.Permission{
							{
								Scope:          "org",
								ScopeID:        testOrgID,
								OrganizationID: testOrgID,
								Permissions:    []string{"org_manage_devices"},
							},
						},
					},
				}
			},
			requestBody:    validRequestBody,
			orgId:          testOrgID,
			expectedStatus: http.StatusOK,
		},
		{
			name: "user_permissions_not_found",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// No setup needed
			},
			setupUserPermissions: func() *MockUserPermissions {
				return nil // Simulate user permissions not found
			},
			requestBody:    validRequestBody,
			orgId:          testOrgID,
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "missing_organization_id",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// No setup needed
			},
			setupUserPermissions: func() *MockUserPermissions {
				return &MockUserPermissions{
					UserPermissions: &authorizer.UserPermissions{UserID: "test-user-id"},
				}
			},
			requestBody:    validRequestBody,
			orgId:          "", // Empty orgId to simulate missing
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "invalid_instruction",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// No setup needed
			},
			setupUserPermissions: func() *MockUserPermissions {
				return &MockUserPermissions{
					UserPermissions: &authorizer.UserPermissions{UserID: "test-user-id"},
				}
			},
			requestBody:    invalidInstructionBody,
			orgId:          testOrgID,
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "invalid_json_body",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// No setup needed
			},
			setupUserPermissions: func() *MockUserPermissions {
				return &MockUserPermissions{
					UserPermissions: &authorizer.UserPermissions{UserID: "test-user-id"},
				}
			},
			requestBody:    invalidJSONBody,
			orgId:          testOrgID,
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "invalid_device_id_parsing",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// No setup needed
			},
			setupUserPermissions: func() *MockUserPermissions {
				return &MockUserPermissions{
					UserPermissions: &authorizer.UserPermissions{UserID: "test-user-id"},
				}
			},
			requestBody:    invalidDeviceIDBody,
			orgId:          testOrgID,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "connection_error",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// No setup needed
			},
			setupUserPermissions: func() *MockUserPermissions {
				return &MockUserPermissions{
					UserPermissions: &authorizer.UserPermissions{UserID: "test-user-id"},
				}
			},
			requestBody:             validRequestBody,
			orgId:                   testOrgID,
			mockGetConnectionsError: true,
			expectedStatus:          http.StatusInternalServerError,
		},
		{
			name: "can_access_device_error",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// Mock database error for CanAccessDevice's getDeviceInfo query
				mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database error")
				}
			},
			setupUserPermissions: func() *MockUserPermissions {
				return &MockUserPermissions{
					UserPermissions: &authorizer.UserPermissions{
						UserID: "test-user-id",
						Permissions: []authorizer.Permission{
							{
								Scope:          "org",
								ScopeID:        testOrgID,
								OrganizationID: testOrgID,
								Permissions:    []string{"org_manage_devices"},
							},
						},
					},
				}
			},
			requestBody:    validRequestBody,
			orgId:          testOrgID,
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "can_access_device_forbidden",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// Mock device from different org (no access)
				mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
						deviceInfo.DeviceID = testDeviceID
						deviceInfo.OrganizationID = testOrgID2 // Different org
						deviceInfo.DeviceGroupIDs = []string{}
						deviceInfo.LocationGroupIDs = []string{}
					}
					return nil
				}
			},
			setupUserPermissions: func() *MockUserPermissions {
				return &MockUserPermissions{
					UserPermissions: &authorizer.UserPermissions{
						UserID: "test-user-id",
						Permissions: []authorizer.Permission{
							{
								Scope:          "org",
								ScopeID:        testOrgID, // Different from device's org
								OrganizationID: testOrgID,
								Permissions:    []string{"org_manage_devices"},
							},
						},
					},
				}
			},
			requestBody:    validRequestBody,
			orgId:          testOrgID,
			expectedStatus: http.StatusForbidden,
		},
		{
			name: "insert_instruction_db_error",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// Mock for CanAccessDevice's getDeviceInfo query
				mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
						deviceInfo.DeviceID = testDeviceID
						deviceInfo.OrganizationID = testOrgID
						deviceInfo.DeviceGroupIDs = []string{}
						deviceInfo.LocationGroupIDs = []string{}
					}
					return nil
				}
				// Mock database error for insertInstruction
				mdb.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database error")
				}
			},
			setupUserPermissions: func() *MockUserPermissions {
				return &MockUserPermissions{
					UserPermissions: &authorizer.UserPermissions{
						UserID: "test-user-id",
						Permissions: []authorizer.Permission{
							{
								Scope:          "org",
								ScopeID:        testOrgID,
								OrganizationID: testOrgID,
								Permissions:    []string{"org_manage_devices"},
							},
						},
					},
				}
			},
			requestBody:    validRequestBody,
			orgId:          testOrgID,
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "device_not_in_organization",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// Mock for CanAccessDevice's getDeviceInfo query
				mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
						deviceInfo.DeviceID = testDeviceID
						deviceInfo.OrganizationID = testOrgID
						deviceInfo.DeviceGroupIDs = []string{}
						deviceInfo.LocationGroupIDs = []string{}
					}
					return nil
				}
				// Mock no rows affected (device not in organization)
				mdb.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &FakeResult{AffectedRows: 0}, nil
				}
			},
			setupUserPermissions: func() *MockUserPermissions {
				return &MockUserPermissions{
					UserPermissions: &authorizer.UserPermissions{
						UserID: "test-user-id",
						Permissions: []authorizer.Permission{
							{
								Scope:          "org",
								ScopeID:        testOrgID,
								OrganizationID: testOrgID,
								Permissions:    []string{"org_manage_devices"},
							},
						},
					},
				}
			},
			requestBody:    validRequestBody,
			orgId:          testOrgID,
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB := new(dbexecutor.FakeDBExecutor)
			tt.setupMocks(mockDB)

			mockConnections := &connect.Connections{
				Postgres: mockDB,
			}

			userPerms := tt.setupUserPermissions()

			deps := HandlerDeps{
				UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
					if userPerms == nil {
						return nil, false
					}
					return userPerms.UserPermissions, true
				},
				GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					if tt.mockGetConnectionsError {
						return nil, errors.New("connection error")
					}
					return mockConnections, nil
				},
				ParseRequest:      parseRequest,
				InsertInstruction: insertInstruction,
			}

			var req *http.Request
			if tt.orgId == "" {
				req = httptest.NewRequest(http.MethodPost, "/", strings.NewReader(tt.requestBody))
			} else {
				req = newRequestWithOrgId(http.MethodPost, "/", tt.orgId, tt.requestBody)
			}

			w := httptest.NewRecorder()

			handler := HandlerWithDeps(deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code, "Test %s: Expected status code %d, got %d", tt.name, tt.expectedStatus, w.Code)
		})
	}
}

func Test_parseRequest(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		requestBody   string
		expectedError error
	}{
		{
			name:          "valid_request",
			requestBody:   `{"instruction": "get_device_logs", "device_id": 123}`,
			expectedError: nil,
		},
		{
			name:          "invalid_instruction",
			requestBody:   `{"instruction": "invalid_instruction", "device_id": 123}`,
			expectedError: ErrInvalidInstructionRequest,
		},
		{
			name:          "invalid_json",
			requestBody:   `{"instruction": "get_device_logs", "device_id": 123`,
			expectedError: ErrDecodeRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(http.MethodPost, "/instruction", strings.NewReader(tt.requestBody))
			result, err := parseRequest(req)

			if tt.expectedError != nil {
				assert.ErrorIs(t, err, tt.expectedError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "get_device_logs", result.Instruction)
				assert.Equal(t, json.RawMessage("123"), result.DeviceID)
			}
		})
	}
}

func Test_insertInstruction(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		setupMock   func(*dbexecutor.FakeDBExecutor)
		deviceUUID  string
		instruction string
		orgId       string
		wantErr     bool
		errContains string
	}{
		{
			name: "successful_insertion",
			setupMock: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					assert.Equal(t, 5, len(args), "Should have 5 arguments")
					assert.Equal(t, "org-123", args[4], "Last argument should be orgId")
					return &FakeResult{AffectedRows: 1}, nil
				}
			},
			deviceUUID:  "device-uuid-123",
			instruction: "get_device_logs",
			orgId:       "org-123",
			wantErr:     false,
		},
		{
			name: "no_rows_affected",
			setupMock: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &FakeResult{AffectedRows: 0}, nil
				}
			},
			deviceUUID:  "device-uuid-999",
			instruction: "get_device_logs",
			orgId:       "org-123",
			wantErr:     true,
			errContains: "no instruction was inserted",
		},
		{
			name: "database_error",
			setupMock: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database error")
				}
			},
			deviceUUID:  "device-uuid-123",
			instruction: "get_device_logs",
			orgId:       "org-123",
			wantErr:     true,
			errContains: "failed to insert instruction",
		},
		{
			name: "rows_affected_error",
			setupMock: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &FakeResult{
						AffectedRows:    1,
						RowsAffectedErr: errors.New("rows affected error"),
					}, nil
				}
			},
			deviceUUID:  "device-uuid-123",
			instruction: "get_device_logs",
			orgId:       "org-123",
			wantErr:     true,
			errContains: "failed to get rows affected",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB := new(dbexecutor.FakeDBExecutor)
			tt.setupMock(mockDB)

			userPerms := &authorizer.UserPermissions{UserID: "user-123"}
			err := insertInstruction(mockDB, userPerms, tt.deviceUUID, tt.instruction, tt.orgId)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
