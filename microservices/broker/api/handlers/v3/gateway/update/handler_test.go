package update

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"strconv"
	"strings"
	"testing"

	softwareGateway "synapse-its.com/shared/api/softwaregateway"
	connect "synapse-its.com/shared/connect"
	mocks "synapse-its.com/shared/mocks"
)

// --- fakeResult implements sql.Result for testing ---

type fakeResult struct{}

func (r fakeResult) LastInsertId() (int64, error) { return 0, nil }
func (r fakeResult) RowsAffected() (int64, error) { return 1, nil }

// --- Store Original Functions to Enable Override ---
var (
	origParseRequest                   = parseRequest
	origGetGatewayInfo                 = getGatewayInfo
	origGetInstructions                = getInstructions
	origMarkDeviceInstructionsReceived = markDeviceInstructionsReceived
	origResetLastChecked               = resetLastChecked
)

// restoreOverrides resets all package‑level function variables after each test.
func restoreOverrides() {
	parseRequest = origParseRequest
	getGatewayInfo = origGetGatewayInfo
	getInstructions = origGetInstructions
	markDeviceInstructionsReceived = origMarkDeviceInstructionsReceived
	resetLastChecked = origResetLastChecked
}

// --- Tests for Handler ---

// ------------------------
// Test Handler: parseRequest error
// ------------------------
func TestHandler_ParseRequestError(t *testing.T) {
	defer restoreOverrides()

	parseRequest = func(r *http.Request) (string, string, error) {
		return "", "", errors.New("parse error")
	}

	// Missing required header "gateway-device-id" so parseRequest returns an error.
	bodyData := []byte(`{"token":"abc"}`)
	req, _ := http.NewRequest("POST", "/dummy", bytes.NewBuffer(bodyData))
	// Set only one header.
	req.Header.Set("message-type", "config")

	rr := httptest.NewRecorder()

	// Attach dummy connections in context.
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	Handler(rr, req)

	if rr.Code != http.StatusUnauthorized {
		t.Errorf("Expected HTTP %d Unauthorized, got %d", http.StatusUnauthorized, rr.Code)
	}
}

// ------------------------
// Test Handler: GetConnections error
// ------------------------
func TestHandler_GetConnectionsError(t *testing.T) {
	defer restoreOverrides()

	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-missing", "abc", nil
	}

	// Create a valid request.
	bodyData := []byte(`{"token":"abc"}`)
	req, _ := http.NewRequest("POST", "/dummy", bytes.NewBuffer(bodyData))
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "config")

	rr := httptest.NewRecorder()

	// Do not add connections to the context.

	Handler(rr, req)

	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to connection error, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// ------------------------
// Test Handler: Nil Postgres connection
// ------------------------
func TestHandler_NilPostgres(t *testing.T) {
	defer restoreOverrides()

	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "wrong", nil
	}

	// Create a valid request.
	bodyData := []byte(`{"token":"abc"}`)
	req, _ := http.NewRequest("POST", "/dummy", bytes.NewBuffer(bodyData))
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "config")

	rr := httptest.NewRecorder()

	// Create a connections object with Postgres set to nil.
	conns := &connect.Connections{
		Postgres: nil,
	}
	req = req.WithContext(connect.WithConnections(context.Background(), conns))

	Handler(rr, req)

	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to nil Postgres, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// ------------------------
// Test Handler: getGatewayInfo error (simulate no rows)
// ------------------------
func TestHandler_GetGatewayInfoError(t *testing.T) {
	defer restoreOverrides()

	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "abc", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGateway, error) {
		return nil, errors.New("no rows")
	}

	bodyData := []byte(`{"token":"abc"}`)
	req, _ := http.NewRequest("POST", "/dummy", bytes.NewBuffer(bodyData))
	req.Header.Set("gateway-device-id", "gw-missing")
	req.Header.Set("message-type", "config")

	rr := httptest.NewRecorder()

	// Attach dummy connections in context.
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	Handler(rr, req)

	// Expect unauthorized when gateway info cannot be found.
	if rr.Code != http.StatusUnauthorized {
		t.Errorf("Expected HTTP %d Unauthorized due to missing gateway info, got %d", http.StatusUnauthorized, rr.Code)
	}
}

// ------------------------
// Test Handler: getInstructions error
// ------------------------
func TestHandler_GetInstructionsError(t *testing.T) {
	defer restoreOverrides()

	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "abc", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGateway, error) {
		return &dbGateway{
			Token:                 "abc",
			PushConfigOnNextCheck: true,
		}, nil
	}
	getInstructions = func(pg connect.DatabaseExecutor, gatewayId string) (*[]softwareGateway.OnDemandPayload, error) {
		return nil, errors.New("instruction query failed")
	}

	bodyData := []byte(`{"token":"abc"}`)
	req, _ := http.NewRequest("POST", "/dummy", bytes.NewBuffer(bodyData))
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "config")

	rr := httptest.NewRecorder()

	// Attach dummy connections in context.
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	Handler(rr, req)

	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to getInstructions error, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// ------------------------
// Test Handler: markDeviceInstructionsReceived error
// ------------------------
func TestHandler_MarkDeviceInstructionsReceivedError(t *testing.T) {
	defer restoreOverrides()

	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "abc", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGateway, error) {
		return &dbGateway{
			Token:                 "abc",
			PushConfigOnNextCheck: true,
		}, nil
	}
	getInstructions = func(pg connect.DatabaseExecutor, gatewayId string) (*[]softwareGateway.OnDemandPayload, error) {
		return &[]softwareGateway.OnDemandPayload{
			{DeviceID: "dev1", Instruction: "inst1"},
		}, nil
	}
	markDeviceInstructionsReceived = func(pg connect.DatabaseExecutor, instructions *[]softwareGateway.OnDemandPayload) error {
		return errors.New("failed to mark instructions")
	}

	bodyData := []byte(`{"token":"abc"}`)
	req, _ := http.NewRequest("POST", "/dummy", bytes.NewBuffer(bodyData))
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "config")

	rr := httptest.NewRecorder()

	// Attach dummy connections in context.
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	Handler(rr, req)

	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to markDeviceInstructionsReceived error, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// ------------------------
// Test Handler: resetLastChecked error does not affect response
// ------------------------
func TestHandler_ResetLastCheckedError(t *testing.T) {
	defer restoreOverrides()

	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "abc", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGateway, error) {
		return &dbGateway{
			Token:                 "abc",
			PushConfigOnNextCheck: true,
		}, nil
	}
	getInstructions = func(pg connect.DatabaseExecutor, gatewayId string) (*[]softwareGateway.OnDemandPayload, error) {
		return &[]softwareGateway.OnDemandPayload{
			{DeviceID: "dev1", Instruction: "inst1"},
		}, nil
	}
	markDeviceInstructionsReceived = func(pg connect.DatabaseExecutor, instructions *[]softwareGateway.OnDemandPayload) error {
		return nil
	}
	// Simulate error in resetLastChecked, but note that response is sent before this call.
	resetLastChecked = func(pg connect.DatabaseExecutor, gatewayId string) error {
		return errors.New("reset failed")
	}

	bodyData := []byte(`{"token":"abc"}`)
	req, _ := http.NewRequest("POST", "/dummy", bytes.NewBuffer(bodyData))
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "config")

	rr := httptest.NewRecorder()

	// Attach dummy connections in context.
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	Handler(rr, req)

	// Even though resetLastChecked fails, the response should have already been sent as success.
	if rr.Code != http.StatusOK {
		t.Errorf("Expected HTTP %d OK despite resetLastChecked failure, got %d", http.StatusOK, rr.Code)
	}
}

// ------------------------
// Test Handler: Success scenario
// ------------------------
func TestHandler_Success(t *testing.T) {
	defer restoreOverrides()

	// All dependencies simulate successful behavior.
	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "abc", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGateway, error) {
		return &dbGateway{
			Token:                 "abc",
			PushConfigOnNextCheck: true,
		}, nil
	}
	getInstructions = func(pg connect.DatabaseExecutor, gatewayId string) (*[]softwareGateway.OnDemandPayload, error) {
		return &[]softwareGateway.OnDemandPayload{
			{DeviceID: "dev1", Instruction: "inst1"},
			{DeviceID: "dev2", Instruction: "inst2"},
		}, nil
	}
	markDeviceInstructionsReceived = func(pg connect.DatabaseExecutor, instructions *[]softwareGateway.OnDemandPayload) error {
		return nil
	}
	resetLastChecked = func(pg connect.DatabaseExecutor, gatewayId string) error {
		return nil
	}

	bodyData := []byte(`{"token":"abc"}`)
	req, _ := http.NewRequest("POST", "/dummy", bytes.NewBuffer(bodyData))
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "config")

	rr := httptest.NewRecorder()

	// Attach dummy connections in context.
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	Handler(rr, req)

	if rr.Code != http.StatusOK {
		t.Errorf("Expected HTTP %d OK for success case, got %d", http.StatusOK, rr.Code)
	}

	type SuccessResponse struct {
		Status  string                                        `json:"status"`
		Data    softwareGateway.SoftwareGatewayUpdateResponse `json:"data"`
		Message string                                        `json:"message"`
		Code    int                                           `json:"code"`
	}

	// Unmarshal and verify the response body.
	var resp SuccessResponse
	if err := json.Unmarshal(rr.Body.Bytes(), &resp); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	// Verify the actual data (SoftwareGatewayUpdateResponse) contained in the envelope.
	data := resp.Data
	if data.ConfigUpdateAvailable != strconv.FormatBool(true) {
		t.Errorf("Expected ConfigUpdateAvailable %s, got %s", strconv.FormatBool(true), data.ConfigUpdateAvailable)
	}

	if len(data.Instructions) != 2 {
		t.Errorf("Expected 2 instructions in response, got %d", len(data.Instructions))
	}
}

// --- Tests for parseRequest ---

func TestParseRequest_Valid(t *testing.T) {
	bodyData := []byte(`{"token":"mytoken"}`)
	req, _ := http.NewRequest("POST", "/dummy", bytes.NewBuffer(bodyData))
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "config")

	gwID, token, err := parseRequest(req)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if gwID != "gw-123" {
		t.Errorf("Expected gateway-device-id 'gw-123', got '%s'", gwID)
	}
	if token != "mytoken" {
		t.Errorf("Expected token 'mytoken', got '%s'", token)
	}
}

func TestParseRequest_MissingGatewayHeader(t *testing.T) {
	bodyData := []byte(`{"token":"mytoken"}`)
	req, _ := http.NewRequest("POST", "/dummy", bytes.NewBuffer(bodyData))
	req.Header.Set("message-type", "config") // Missing gateway-device-id

	_, _, err := parseRequest(req)
	if err == nil || !strings.Contains(err.Error(), "missing gateway-device-id") {
		t.Errorf("Expected error for missing gateway-device-id header, got: %v", err)
	}
}

func TestParseRequest_InvalidMessageType(t *testing.T) {
	bodyData := []byte(`{"token":"mytoken"}`)
	req, _ := http.NewRequest("POST", "/dummy", bytes.NewBuffer(bodyData))
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "invalid") // Not "config"

	_, _, err := parseRequest(req)
	if err == nil || !strings.Contains(err.Error(), "invalid message-type") {
		t.Errorf("Expected error for invalid message-type header, got: %v", err)
	}
}

func TestParseRequest_InvalidJSON(t *testing.T) {
	bodyData := []byte(`not a json`) // Not a json
	req, _ := http.NewRequest("POST", "/dummy", bytes.NewBuffer(bodyData))
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "config")

	_, _, err := parseRequest(req)
	if err == nil || !strings.Contains(err.Error(), "invalid character") {
		t.Errorf("Expected error for invalid character, got: %v", err)
	}
}

func TestParseRequest_MissingToken(t *testing.T) {
	bodyData := []byte(`{"token":""}`)
	req, _ := http.NewRequest("POST", "/dummy", bytes.NewBuffer(bodyData))
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "config")

	_, _, err := parseRequest(req)
	if err == nil || !strings.Contains(err.Error(), "token does not exist") {
		t.Errorf("Expected error for missing token in body, got: %v", err)
	}
}

// --- Tests for resetLastChecked  ---

func TestResetLastChecked_Success(t *testing.T) {
	var capturedQuery string
	var capturedArgs []interface{}

	// Create a FakeDBExecutor with an ExecFunc override.
	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			capturedQuery = query
			capturedArgs = args
			return fakeResult{}, nil
		},
	}

	gwID := "gw-123"
	err := resetLastChecked(fakeDB, gwID)
	if err != nil {
		t.Fatalf("Expected no error from resetLastChecked, got: %v", err)
	}
	if !strings.Contains(capturedQuery, "UPDATE") {
		t.Errorf("Expected update query in resetLastChecked, got: %s", capturedQuery)
	}
	if len(capturedArgs) != 2 {
		t.Errorf("Expected 2 parameters in query, got: %d", len(capturedArgs))
	}
	if capturedArgs[1] != gwID {
		t.Errorf("Expected gateway id %s, got: %v", gwID, capturedArgs[1])
	}

	// Check that the timestamp parameter is a non-empty string.
	ts, ok := capturedArgs[0].(string)
	if !ok || ts == "" {
		t.Errorf("Expected a non-empty timestamp string, got: %v", capturedArgs[0])
	}
}

func TestResetLastChecked_GenericQueryError(t *testing.T) {
	// Create a FakeDBExecutor with an ExecFunc override.
	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			// Simulate General Error error.
			return fakeResult{}, errors.New("Generic error")
		},
	}

	gwID := "gw-123"
	err := resetLastChecked(fakeDB, gwID)
	if err == nil || !strings.Contains(err.Error(), "Generic error") {
		t.Errorf("Expected error for Generic error, got: %v", err)
	}
}

// --- Tests for getGatewayInfo ---

func TestGetGatewayInfo_Success(t *testing.T) {
	expectedGateway := &dbGateway{
		Token:                 "abc",
		PushConfigOnNextCheck: true,
	}
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			db, ok := dest.(*dbGateway)
			if !ok {
				return errors.New("dest must be of type *dbGateway")
			}
			*db = *expectedGateway
			return nil
		},
	}

	gateway, err := getGatewayInfo(fakeDB, "gw-123", "abc")
	if err != nil {
		t.Fatalf("Expected success, got error: %v", err)
	}
	if gateway.Token != "abc" {
		t.Errorf("Expected token 'abc', got '%s'", gateway.Token)
	}
	if gateway.PushConfigOnNextCheck != true {
		t.Errorf("Expected PushConfigOnNextCheck to be true, got: %v", gateway.PushConfigOnNextCheck)
	}
}

func TestGetGatewayInfo_NoRows(t *testing.T) {
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			// Simulate no rows.
			return sql.ErrNoRows
		},
	}

	_, err := getGatewayInfo(fakeDB, "gw-missing", "abc")
	if err == nil || !strings.Contains(err.Error(), "was not found") {
		t.Errorf("Expected error for missing gateway, got: %v", err)
	}
}

func TestGetGatewayInfo_GenericQueryError(t *testing.T) {
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			// Simulate not sql.ErrNoRows error.
			return errors.New("Generic error")
		},
	}

	_, err := getGatewayInfo(fakeDB, "gw-missing", "abc")
	if err == nil || !strings.Contains(err.Error(), "Generic error") {
		t.Errorf("Expected error for Generic error, got: %v", err)
	}
}

// --- Tests for getInstructions ---

func TestGetInstructions_Success(t *testing.T) {
	mockRows := &[]softwareGateway.OnDemandPayload{
		{
			DeviceID:    "dev1",
			Instruction: "inst1",
		},
		{
			DeviceID:    "dev2",
			Instruction: "inst2",
		},
	}

	fakeDB := &mocks.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			instrSlice, ok := dest.(*[]softwareGateway.OnDemandPayload)
			if !ok {
				return errors.New("dest is not of type *[]softwareGateway.OnDemandPayload")
			}
			*instrSlice = *mockRows
			return nil
		},
	}

	instructionsPtr, err := getInstructions(fakeDB, "gw-123")

	// Check that the pointer isn't nil
	if instructionsPtr == nil {
		t.Fatalf("Expected non-nil instructions pointer")
	}

	instructions := *instructionsPtr
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if len(instructions) != 2 {
		t.Fatalf("Expected 2 instructions, got: %d", len(instructions))
	}
	if instructions[0].DeviceID != "dev1" || instructions[0].Instruction != "inst1" {
		t.Errorf("Unexpected first instruction: %+v", instructions[0])
	}
}

func TestGetInstructions_Error(t *testing.T) {
	fakeDB := &mocks.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			return errors.New("query failed")
		},
	}
	_, err := getInstructions(fakeDB, "gw-123")
	if err == nil || !strings.Contains(err.Error(), "query failed") {
		t.Errorf("Expected query failure error, got: %v", err)
	}
}

// --- Tests for markDeviceInstructionsReceived ---

func TestMarkDeviceInstructionsReceived_Success(t *testing.T) {
	var capturedQuery string
	var capturedArgs []interface{}

	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			capturedQuery = query
			capturedArgs = args
			return fakeResult{}, nil
		},
	}

	// Sample instructions.
	instructions := []softwareGateway.OnDemandPayload{
		{DeviceID: "dev1", Instruction: "inst1"},
		{DeviceID: "dev2", Instruction: "inst2"},
	}

	err := markDeviceInstructionsReceived(fakeDB, &instructions)
	if err != nil {
		t.Fatalf("Expected success from markDeviceInstructionsReceived, got: %v", err)
	}
	if !strings.Contains(capturedQuery, "WITH payload") {
		t.Errorf("Expected query to contain 'WITH payload', got: %s", capturedQuery)
	}
	// There should be a timestamp plus two parameters per instruction.
	expectedParams := 1 + len(instructions)*2
	if len(capturedArgs) != expectedParams {
		t.Errorf("Expected %d parameters, got: %d", expectedParams, len(capturedArgs))
	}
	// Verify that each DeviceID appears in the parameters.
	for _, inst := range instructions {
		found := false
		for _, arg := range capturedArgs {
			if arg == inst.DeviceID {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected DeviceID %s in parameters", inst.DeviceID)
		}
	}
}

func TestMarkDeviceInstructionsReceived_NoInstructions(t *testing.T) {
	called := false
	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			called = true
			return fakeResult{}, nil
		},
	}

	err := markDeviceInstructionsReceived(fakeDB, &[]softwareGateway.OnDemandPayload{})
	if err != nil {
		t.Errorf("Expected nil error when no instructions, got: %v", err)
	}
	if called {
		t.Error("Exec should not be called when there are no instructions")
	}
}

func TestMarkDeviceInstructionsReceived_NilInstructions(t *testing.T) {
	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return fakeResult{}, nil
		},
	}

	err := markDeviceInstructionsReceived(fakeDB, nil)
	if err == nil {
		t.Fatal("expected error for nil instructions pointer, got nil")
	}
	if err.Error() != "instructions nil pointer" {
		t.Errorf("unexpected error message: %q", err.Error())
	}
}

func TestMarkDeviceInstructionsReceived_GenericQueryError(t *testing.T) {
	// Create a FakeDBExecutor with an ExecFunc override.
	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			// Simulate General Error error.
			return fakeResult{}, errors.New("Generic error")
		},
	}

	// Sample instructions.
	instructions := []softwareGateway.OnDemandPayload{
		{DeviceID: "dev1", Instruction: "inst1"},
		{DeviceID: "dev2", Instruction: "inst2"},
	}

	err := markDeviceInstructionsReceived(fakeDB, &instructions)
	if err == nil || !strings.Contains(err.Error(), "Generic error") {
		t.Errorf("Expected error for Generic error, got: %v", err)
	}
}
