package update

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	response "synapse-its.com/shared/api/response"
	softwareGateway "synapse-its.com/shared/api/softwaregateway"
	connect "synapse-its.com/shared/connect"
	logger "synapse-its.com/shared/logger"
)

// <PERSON><PERSON> serves as the handler for the main function for the gateway update endpoint.
//
// @Summary      Get gateway updates and instructions
// @Description  Retrieves pending instructions and configuration updates for a software gateway. This endpoint is used by gateways to check for new device instructions and configuration changes. Instructions are marked as received when fetched and will not be returned again.
// @Tags         env:dev, env:qa, gateway
// @Accept       json
// @Produce      json
// @Param        gateway-device-id  header    string                                            true   "Gateway device identifier (MachineKey)"
// @Param        message-type       header    string                                            true   "Message type (must be 'config')"
// @Param        body               body      update.requestBody                                true   "Update request body with authentication token"
// @Success      200                {object}  softwareGateway.SoftwareGatewayUpdateResponse     "Updates and instructions retrieved successfully"
// @Failure      401                {object}  shared.UnauthorizedResponse                       "Unauthorized"
// @Failure      500                {object}  shared.InternalServerErrorResponse                "Internal Server Error"
// @Router       /v3/gateway/update [post]
func Handler(w http.ResponseWriter, r *http.Request) {
	gatewayId, rqToken, err := parseRequest(r)
	if err != nil {
		logger.Infof("Unable to parse request: %v", err)
		response.CreateUnauthorizedResponse(w)
		return
	}

	// Get the postgres connection.
	connections, err := connect.GetConnections(r.Context())
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}
	pg := connections.Postgres

	// Query database for token and flag to push configs
	dbGateway, err := getGatewayInfo(pg, gatewayId, rqToken)
	if err != nil {
		logger.Infof("Error getting gateway info: %v", err)
		response.CreateUnauthorizedResponse(w)
		return
	}

	// Query the database for pending instructions
	instructions, err := getInstructions(pg, gatewayId)
	if err != nil {
		logger.Infof("Error fetching instructions: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	// Before returning results, mark instructions as 'received'. No continuing if it fails
	if err := markDeviceInstructionsReceived(pg, instructions); err != nil {
		logger.Errorf("Error clearing instructions: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	// Send instruction set immediately after marking as received to prevent concurrency issues
	sg := softwareGateway.SoftwareGatewayUpdateResponse{}
	sg.Instructions = *instructions
	sg.ConfigUpdateAvailable = strconv.FormatBool(dbGateway.PushConfigOnNextCheck)

	response.CreateSuccessResponse(sg, w)

	// Update DateLastCheckedIn and reset PushConfigOnNextCheck after success response
	// If this query fails, it should still result in a safe state as instructions will not be repeated
	if err := resetLastChecked(pg, gatewayId); err != nil {
		logger.Errorf("Error updating SoftwareGateway last checked: %v", err)
		return
	}
}

// Parses the HTTP request to validate it is correctly formed and returns gatewayId from header
// and Token from body
var parseRequest = func(r *http.Request) (string, string, error) {
	gatewayId := r.Header.Get("gateway-device-id")
	if gatewayId == "" {
		return "", "", errors.New("missing gateway-device-id header; no value present")
	}

	messageType := r.Header.Get("message-type")
	if messageType != "config" {
		return "", "", errors.New("invalid message-type header; expected 'config'")
	}

	type requestBody struct {
		Token string `json:"token"`
	}

	var body requestBody
	err := json.NewDecoder(r.Body).Decode(&body)
	if err != nil {
		return "", "", err
	}

	if body.Token == "" {
		return "", "", errors.New("token does not exist in the body")
	}
	return gatewayId, body.Token, nil
}

// Gets gateway info to check against from the PG db
var getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGateway, error) {
	query := `		
		SELECT 
			s.Token,
			CASE 
				WHEN s.Token = '' THEN true 
				ELSE s.PushConfigOnNextCheck 
			END as PushConfigOnNextCheck
		FROM {{SoftwareGateway}} s
		WHERE s.IsEnabled AND s.MachineKey = $1 AND (s.Token = $2 OR s.Token = '')
	`
	gateway := &dbGateway{}
	err := pg.QueryRowStruct(gateway, query, gatewayId, rqToken)
	// Query returns no rows
	if errors.Is(err, sql.ErrNoRows) {
		return nil, fmt.Errorf("GatewayId (%s) was not found. Either the gatewayId does not exist, the gatewayId is disabled, etc", gatewayId)
	}
	if err != nil {
		return nil, err
	}
	return gateway, nil
}

// Returns a *[]softwareGateway.OnDemandPayload for pending instructions
var getInstructions = func(pg connect.DatabaseExecutor, gatewayId string) (*[]softwareGateway.OnDemandPayload, error) {
	query := `
		SELECT
			DISTINCT i.DeviceId,
			i.Instruction
		FROM {{SoftwareGatewayInstruction}} i
		JOIN {{Device}} d
			ON i.DeviceId = d.Id
		JOIN {{SoftwareGateway}} s
			ON d.SoftwareGatewayId = s.Id
		WHERE 
			i.Status = 'queued'
			AND s.MachineKey = $1
			AND s.IsEnabled
	`
	onDemandPayloadResult := &[]softwareGateway.OnDemandPayload{}
	err := pg.QueryGenericSlice(onDemandPayloadResult, query, gatewayId)
	if err != nil {
		return nil, err
	}
	return onDemandPayloadResult, nil
}

// Marks instructions as received from a []softwareGateway.OnDemandPayload
var markDeviceInstructionsReceived = func(pg connect.DatabaseExecutor, instructions *[]softwareGateway.OnDemandPayload) error {
	if instructions == nil {
		return errors.New("instructions nil pointer")
	}

	// If instructions is empty stop processing
	if len(*instructions) == 0 {
		return nil
	}

	// Initialize params with $1 representing the current timestamp
	params := []interface{}{time.Now().UTC().Format(time.DateTime)}

	// Set param index to the next $X available, in this case $2
	paramIndex := 2

	// Initialize a placeholder array
	var valuePlaceholders []string

	// Build the dynamic VALUES clause of the CTE
	for _, payload := range *instructions {
		// Each onDemandPayload contains two values: deviceID and instruction
		// valuePlaceholders will generate a placeholder like "($2, $3)", "($4, $5)",...
		valuePlaceholders = append(valuePlaceholders, fmt.Sprintf("($%d, $%d)", paramIndex, paramIndex+1))
		// params will have each deviceID and instruction pair appended to it
		params = append(params, payload.DeviceID, payload.Instruction)
		// paramIndex incremented for each loop
		paramIndex += 2
	}

	// Combine the row placeholders into a comma-seperated list
	valuesClause := strings.Join(valuePlaceholders, ", ")

	query := `
		WITH payload (DeviceId, Instruction) AS (
			VALUES %s
		)
		UPDATE {{SoftwareGatewayInstruction}} i
		SET 
			DateReceived = $1,
			Status = 'received'
		FROM payload p 
		WHERE
			i.DeviceId = p.DeviceId::uuid
			AND i.Instruction = p.Instruction
			AND i.Status = 'queued'
	`
	// Construct the final SQL query
	query = fmt.Sprintf(query, valuesClause)

	// Execute with all parameters
	_, err := pg.Exec(query, params...)
	return err
}

// Resets the last checked DateLastCheckedIn and PushConfigOnNextCheck in {{SoftwareGateway}} PG
var resetLastChecked = func(pg connect.DatabaseExecutor, gatewayId string) error {
	query := `
		UPDATE {{SoftwareGateway}} s
		SET
			DateLastCheckedIn = $1,
			PushConfigOnNextCheck = false
		WHERE s.IsEnabled AND s.MachineKey = $2
	`
	_, err := pg.Exec(query, time.Now().UTC().Format(time.DateTime), gatewayId)
	return err
}
