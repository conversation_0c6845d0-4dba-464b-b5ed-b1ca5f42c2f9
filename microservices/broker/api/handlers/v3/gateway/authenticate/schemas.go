package authenticate

// Used by get gateway info
type dbGatewayInfo struct {
	Id             string `db:"id"`
	OrganizationId string `db:"organizationid"`
	APIKey         string `db:"apikey"`
	Config         string `db:"config"`
	Token          string `db:"token"`
}

type dbConfig struct {
	Value string `db:"value"`
}

// requestBody represents the authentication request payload
type requestBody struct {
	Token *string `json:"token" example:"abc123token" binding:"required"` // Authentication token (can be empty string for initial setup)
}
