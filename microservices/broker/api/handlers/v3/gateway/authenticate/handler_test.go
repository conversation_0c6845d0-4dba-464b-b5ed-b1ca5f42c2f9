package authenticate

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	security "synapse-its.com/shared/api/security"
	softwareGateway "synapse-its.com/shared/api/softwaregateway"
	connect "synapse-its.com/shared/connect"
	mocks "synapse-its.com/shared/mocks"
	gatewayConfig "synapse-its.com/shared/rest/onramp/softwaregateway/config"
)

// fakeResult implements sql.Result for testing.
type fakeResult struct{}

func (r fakeResult) LastInsertId() (int64, error) { return 0, nil }
func (r fakeResult) RowsAffected() (int64, error) { return 1, nil }

// --- Store Original Functions to Enable Override ---
var (
	origParseRequest                                = parseRequest
	origGetGatewayInfo                              = getGatewayInfo
	origGetGatewayConfigWithTemplates               = getGatewayConfigWithTemplates
	origGetSoftwareGatewayByIdentifierFunc          = getSoftwareGatewayByIdentifierFunc
	origConvertSettingsToGatewaySettings            = convertSettingsToGatewaySettings
	origConvertSettingsToGatewaySettingsWithVersion = convertSettingsToGatewaySettingsWithVersion
	origGetDeviceSettings                           = getDeviceSettings
	origSetNewGatewayToken                          = setNewGatewayToken
	origGetJWTAsymmetric                            = security.GetJWTAsymmetric
)

// restoreOverrides resets all package‑level function variables after each test.
func restoreOverrides() {
	parseRequest = origParseRequest
	getGatewayInfo = origGetGatewayInfo
	getGatewayConfigWithTemplates = origGetGatewayConfigWithTemplates
	getSoftwareGatewayByIdentifierFunc = origGetSoftwareGatewayByIdentifierFunc
	convertSettingsToGatewaySettings = origConvertSettingsToGatewaySettings
	convertSettingsToGatewaySettingsWithVersion = origConvertSettingsToGatewaySettingsWithVersion
	getDeviceSettings = origGetDeviceSettings
	setNewGatewayToken = origSetNewGatewayToken
	security.GetJWTAsymmetric = origGetJWTAsymmetric
}

// --- Tests for Handler ---

func TestHandler_ParseRequestError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "", "", "", errors.New("parse error")
	}

	req, err := http.NewRequest("POST", "/api/v3/gateway/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusUnauthorized {
		t.Errorf("Expected HTTP %d Unauthorized due to parse error, got %d", http.StatusUnauthorized, rr.Code)
	}
}

// Test when getGatewayInfo returns an error.
func TestHandler_GetGatewayInfoError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-missing", "abc", "", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, error) {
		return nil, errors.New("gateway info not found")
	}

	req, err := http.NewRequest("POST", "/api/v3/gateway/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-missing")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusUnauthorized {
		t.Errorf("Expected HTTP %d Unauthorized when getGatewayInfo fails, got %d", http.StatusUnauthorized, rr.Code)
	}
}

// Test when GetJWTAsymmetric returns an error.
func TestHandler_GetJWTError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			Id: "123", OrganizationId: "org1", APIKey: "key", Token: "abc", Config: "{}",
		}, nil
	}
	// Override GetJWTAsymmetric to return an error.
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "", "", errors.New("JWT error")
	}

	req, err := http.NewRequest("POST", "/api/v3/gateway/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to GetJWTAsymmetric error, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when getDeviceSettings returns an error.
func TestHandler_GetDeviceSettingsError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			Id: "123", OrganizationId: "org1", APIKey: "key", Token: "abc", Config: "{}",
		}, nil
	}
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "private", "public", nil
	}
	getDeviceSettings = func(pg connect.DatabaseExecutor, dbSoftwareGatewayId string) (*[]softwareGateway.DeviceSettings, error) {
		return nil, errors.New("device settings error")
	}

	req, err := http.NewRequest("POST", "/api/v3/gateway/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to device settings error, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when setNewGatewayToken returns an error.
func TestHandler_SetNewGatewayTokenError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			Id: "123", OrganizationId: "org1", APIKey: "key", Token: "abc", Config: "{}",
		}, nil
	}
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "private", "public", nil
	}
	getDeviceSettings = func(pg connect.DatabaseExecutor, dbSoftwareGatewayId string) (*[]softwareGateway.DeviceSettings, error) {
		return &[]softwareGateway.DeviceSettings{
			{Device_ID: "dev1"},
		}, nil
	}
	setNewGatewayToken = func(pg connect.DatabaseExecutor, gatewayId string, gatewayVersion string) (*softwareGateway.CloudSettings, error) {
		return nil, errors.New("set token error")
	}

	req, err := http.NewRequest("POST", "/api/v3/gateway/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to setNewGatewayToken error, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when the Postgres connection is nil.
func TestHandler_GetConnectionsError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}

	req, err := http.NewRequest("POST", "/api/v3/gateway/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")

	// Don't set connections

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to nil Postgres, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when the Postgres connection is nil.
func TestHandler_NilPostgres(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}

	req, err := http.NewRequest("POST", "/api/v3/gateway/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")

	// Create a connections object with Postgres set to nil.
	conns := &connect.Connections{
		Postgres: nil,
	}
	req = req.WithContext(connect.WithConnections(context.Background(), conns))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to nil Postgres, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test the full successful flow.
func TestHandler_Success(t *testing.T) {
	defer restoreOverrides()
	// Override all dependencies to simulate a successful authentication.
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			Id: "123", OrganizationId: "org1", APIKey: "key", Token: "abc", Config: `{"application_version": "1.0.0"}`,
		}, nil
	}
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "private", "public", nil
	}
	getDeviceSettings = func(pg connect.DatabaseExecutor, dbSoftwareGatewayId string) (*[]softwareGateway.DeviceSettings, error) {
		return &[]softwareGateway.DeviceSettings{
			{Device_ID: "dev1"},
		}, nil
	}
	setNewGatewayToken = func(pg connect.DatabaseExecutor, gatewayId string, gatewayVersion string) (*softwareGateway.CloudSettings, error) {
		return &softwareGateway.CloudSettings{Token: "new-token"}, nil
	}

	req, err := http.NewRequest("POST", "/api/v3/gateway/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusOK {
		t.Errorf("Expected HTTP %d OK on success, got %d", http.StatusOK, rr.Code)
	}

	// The response envelope is created using CreateSuccessResponse.
	// Define a response type matching that envelope.
	type SuccessResponse struct {
		Status  string                         `json:"status"`
		Data    softwareGateway.GlobalSettings `json:"data"`
		Message string                         `json:"message"`
		Code    int                            `json:"code"`
	}

	var resp SuccessResponse
	if err := json.Unmarshal(rr.Body.Bytes(), &resp); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}
	if resp.Status != "success" {
		t.Errorf("Expected status 'success', got %s", resp.Status)
	}
	if resp.Message != "Request Succeeded" {
		t.Errorf("Expected message 'Request Succeeded', got %s", resp.Message)
	}
	if resp.Code != http.StatusOK {
		t.Errorf("Expected code %d, got %d", http.StatusOK, resp.Code)
	}
	// Verify the global settings in the response.
	gs := resp.Data
	if gs.OrganizationId != "org1" {
		t.Errorf("Expected OrganizationId 'org1', got %s", gs.OrganizationId)
	}
	if gs.PublicKey != "public" {
		t.Errorf("Expected PublicKey 'public', got %s", gs.PublicKey)
	}
	if gs.AWS.Token != "new-token" {
		t.Errorf("Expected CloudSettings Token 'new-token', got %s", gs.AWS.Token)
	}
	if len(gs.Devices) != 1 {
		t.Errorf("Expected 1 device setting, got %d", len(gs.Devices))
	}
}

// --- Tests for parseRequest ---

func TestParseRequest_MissingGatewayDeviceID(t *testing.T) {
	body := `{"token": "abc123"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("message-type", "authenticate")
	_, _, _, err := parseRequest(req)
	if err == nil || err.Error() != "header gateway-device-id is not present" {
		t.Errorf("Expected error for missing gateway-device-id, got: %v", err)
	}
}

func TestParseRequest_MissingMessageType(t *testing.T) {
	body := `{"token": "abc123"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	_, _, _, err := parseRequest(req)
	if err == nil || err.Error() != "header message-type is not present" {
		t.Errorf("Expected error for missing message-type, got: %v", err)
	}
}

func TestParseRequest_InvalidMessageType(t *testing.T) {
	body := `{"token": "abc123"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	req.Header.Set("message-type", "not-authenticate")
	_, _, _, err := parseRequest(req)
	if err == nil || err.Error() != "header message-type != authenticate" {
		t.Errorf("Expected error for invalid message-type, got: %v", err)
	}
}

func TestParseRequest_InvalidJSONBody(t *testing.T) {
	body := `not a json`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	req.Header.Set("message-type", "authenticate")
	_, _, _, err := parseRequest(req)
	if err == nil {
		t.Error("Expected error for invalid JSON body, got nil")
	}
}

func TestParseRequest_MissingTokenInBody(t *testing.T) {
	body := `{"notToken": "abc"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	req.Header.Set("message-type", "authenticate")
	_, _, _, err := parseRequest(req)
	if err == nil || err.Error() != "token does not exist in the body" {
		t.Errorf("Expected error for missing token in body, got: %v", err)
	}
}

func TestParseRequest_Success(t *testing.T) {
	const tokenVal = "abc123"
	body := `{"token": "` + tokenVal + `"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	req.Header.Set("message-type", "authenticate")
	gatewayID, token, _, err := parseRequest(req)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if gatewayID != "gateway-001" {
		t.Errorf("Expected gateway-device-id %q, got %q", "gateway-001", gatewayID)
	}
	if token != tokenVal {
		t.Errorf("Expected token %q, got %q", tokenVal, token)
	}
}

// --- Tests for getGatewayInfo ---

func TestGetGatewayInfo_Success(t *testing.T) {
	gatewayID := "gateway-001"
	rqToken := "request-token"
	queryResults := &dbGatewayInfo{
		Id:             "123",
		OrganizationId: "org-001",
		APIKey:         "api-key-value",
		Config:         `{"setting": "value"}`,
		Token:          "dbtoken",
	}
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			db, ok := dest.(*dbGatewayInfo)
			if !ok {
				return errors.New("dest must be of type *dbGatewayInfo")
			}
			*db = *queryResults
			return nil
		},
	}

	gatewayInfo, err := getGatewayInfo(fakeDB, gatewayID, rqToken)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if gatewayInfo.Id != "123" {
		t.Errorf("Expected id '123', got %v", gatewayInfo.Id)
	}
	if gatewayInfo.OrganizationId != "org-001" {
		t.Errorf("Expected org 'org-001', got %v", gatewayInfo.OrganizationId)
	}
	if gatewayInfo.APIKey != "api-key-value" {
		t.Errorf("Expected apiKey 'api-key-value', got %v", gatewayInfo.APIKey)
	}
	if gatewayInfo.Token != "dbtoken" {
		t.Errorf("Expected dbToken 'dbtoken', got %v", gatewayInfo.Token)
	}
}

func TestGetGatewayInfo_NoRows(t *testing.T) {
	gatewayID := "unknown-gateway"
	rqToken := "request-token"
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			return sql.ErrNoRows
		},
	}

	_, err := getGatewayInfo(fakeDB, gatewayID, rqToken)
	if err == nil || !strings.Contains(err.Error(), "was not found") {
		t.Errorf("Expected error for missing gateway, got: %v", err)
	}
}

func TestGetGatewayInfo_GenericQueryError(t *testing.T) {
	gatewayID := "unknown-gateway"
	rqToken := "request-token"
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			return errors.New("Generic error non-sql.norows error")
		},
	}

	_, err := getGatewayInfo(fakeDB, gatewayID, rqToken)
	if err == nil || !strings.Contains(err.Error(), "Generic error") {
		t.Errorf("Expected error for Generic error, got: %v", err)
	}
}

// ---------- Tests for getDeviceSettings ----------

func TestGetDeviceSettings_Success(t *testing.T) {
	// Use keys matching the DeviceSettings JSON tags.
	dbDeviceSettings := []softwareGateway.DeviceSettings{
		{
			Device_ID:          "dev-001",
			Latitude:           "12.34",
			Longitude:          "56.78",
			IP_Address:         "***********",
			Port:               "8080",
			FlushConnection_MS: "100",
			EnableRealtime:     "true",
		},
	}
	fakeDB := &mocks.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			instrSlice, ok := dest.(*[]softwareGateway.DeviceSettings)
			if !ok {
				return errors.New("dest is not of type *[]softwareGateway.DeviceSettings")
			}
			*instrSlice = dbDeviceSettings
			return nil
		},
	}

	deviceSettings, err := getDeviceSettings(fakeDB, "test-gateway-id")
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if len(*deviceSettings) != 1 {
		t.Errorf("Expected 1 device configuration, got %d", len(*deviceSettings))
	}
	ds := (*deviceSettings)[0]
	if ds.Device_ID != "dev-001" {
		t.Errorf("Expected Device_ID 'dev-001', got %q", ds.Device_ID)
	}
	if ds.Latitude != "12.34" {
		t.Errorf("Expected Latitude '12.34', got %q", ds.Latitude)
	}
	if ds.Longitude != "56.78" {
		t.Errorf("Expected Longitude '56.78', got %q", ds.Longitude)
	}
	if ds.IP_Address != "***********" {
		t.Errorf("Expected IP_Address '***********', got %q", ds.IP_Address)
	}
	if ds.Port != "8080" {
		t.Errorf("Expected Port '8080', got %q", ds.Port)
	}
	if ds.FlushConnection_MS != "100" {
		t.Errorf("Expected FlushConnection_MS '100', got %q", ds.FlushConnection_MS)
	}
	if ds.EnableRealtime != "true" {
		t.Errorf("Expected EnableRealtime 'true', got %q", ds.EnableRealtime)
	}
}

func TestGetDeviceSettings_GenericQueryError(t *testing.T) {
	fakeDB := &mocks.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			return errors.New("Generic error non-sql.norows error")
		},
	}

	_, err := getDeviceSettings(fakeDB, "test-gateway-id")
	if err == nil || !strings.Contains(err.Error(), "Generic error") {
		t.Errorf("Expected 'Generic error', got: %v", err)
	}
}

// ---------- Tests for setNewGatewayToken ----------

func TestSetNewGatewayToken_Success(t *testing.T) {
	gatewayID := "gateway-001"
	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			if len(args) != 4 {
				return nil, errors.New("wrong number of args")
			}
			tokenArg, ok := args[0].(string)
			if !ok || tokenArg == "" {
				return nil, errors.New("token is empty")
			}
			// Validate the timestamp format.
			if _, err := time.Parse(time.DateTime, args[1].(string)); err != nil {
				return nil, errors.New("invalid timestamp")
			}
			if args[3] != gatewayID {
				return nil, errors.New("gateway identifier mismatch")
			}
			return fakeResult{}, nil
		},
	}

	cloudSettings, err := setNewGatewayToken(fakeDB, gatewayID, "1.0.0")
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if cloudSettings.Token == "" {
		t.Errorf("Expected a non-empty new token, got: %v", cloudSettings.Token)
	}
}

func TestSetNewGatewayToken_ExecError(t *testing.T) {
	gatewayID := "gateway-001"
	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return nil, errors.New("exec error")
		},
	}

	cloudSettings, err := setNewGatewayToken(fakeDB, gatewayID, "1.0.0")
	if err == nil {
		t.Error("Expected error due to Exec failure, got nil")
	}
	if cloudSettings != nil {
		if cloudSettings.Token != "" {
			t.Errorf("Expected empty token on error, got: %q", cloudSettings.Token)
		}
	}
}

func TestSetNewGatewayToken_GenTokenHexError(t *testing.T) {
	origRandRead := randRead
	defer func() { randRead = origRandRead }()
	readfail := errors.New("read fail")
	randRead = func(b []byte) (int, error) {
		return 0, readfail
	}
	gatewayID := "gateway-001"
	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return nil, nil
		},
	}

	_, err := setNewGatewayToken(fakeDB, gatewayID, "1.0.0")
	if !errors.Is(err, readfail) {
		t.Error("Expected error due to randRead failure, got nil")
	}
}

// Test template settings conversion success
func TestHandler_TemplateConfigSuccess(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}
	getGatewayConfigWithTemplates = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, []gatewayConfig.ResolvedGatewayConfigSetting, error) {
		templateSettings := []gatewayConfig.ResolvedGatewayConfigSetting{
			{Setting: "ws_port", Value: "8080", Format: `{"type":"integer","versions":["v3"]}`},
			{Setting: "ws_active", Value: "true", Format: `{"type":"boolean","versions":["v3"]}`},
			{Setting: "log_level", Value: "info", Format: `{"type":"string","versions":["v3"]}`},
		}
		return &dbGatewayInfo{
			Id: "123e4567-e89b-12d3-a456-************", OrganizationId: "org1", APIKey: "key", Token: "abc",
		}, templateSettings, nil
	}
	getDeviceSettings = func(pg connect.DatabaseExecutor, dbSoftwareGatewayId string) (*[]softwareGateway.DeviceSettings, error) {
		return &[]softwareGateway.DeviceSettings{}, nil
	}
	setNewGatewayToken = func(pg connect.DatabaseExecutor, gatewayId string, gatewayVersion string) (*softwareGateway.CloudSettings, error) {
		return &softwareGateway.CloudSettings{Token: "new-token"}, nil
	}
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "fake-private-key", "fake-public-key", nil
	}

	req, err := http.NewRequest("POST", "/api/v3/gateway/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusOK {
		t.Errorf("Expected HTTP %d success with template config, got %d", http.StatusOK, rr.Code)
	}

	// Verify response contains dynamic gateway settings
	var response struct {
		Data softwareGateway.GlobalSettings `json:"data"`
	}
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	// Check that gateway config map contains our template settings with correct type conversion
	gateway := response.Data.Gateway
	// JSON unmarshaling converts numbers to float64
	if gateway["ws_port"] != float64(8080) {
		t.Errorf("Expected ws_port=8080 (float64), got %v (type: %T)", gateway["ws_port"], gateway["ws_port"])
	}
	if gateway["ws_active"] != true {
		t.Errorf("Expected ws_active=true (bool), got %v (type: %T)", gateway["ws_active"], gateway["ws_active"])
	}
	if gateway["log_level"] != "info" {
		t.Errorf("Expected log_level=info (string), got %v (type: %T)", gateway["log_level"], gateway["log_level"])
	}
}

// Test template settings conversion failure
func TestHandler_TemplateConfigConversionError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}
	getGatewayConfigWithTemplates = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, []gatewayConfig.ResolvedGatewayConfigSetting, error) {
		templateSettings := []gatewayConfig.ResolvedGatewayConfigSetting{
			{Setting: "test_setting", Value: "test_value", Format: `{"type":"string","versions":["v3"]}`},
		}
		return &dbGatewayInfo{
			Id: "123e4567-e89b-12d3-a456-************", OrganizationId: "org1", APIKey: "key", Token: "abc",
		}, templateSettings, nil
	}
	convertSettingsToGatewaySettingsWithVersion = func(settings []gatewayConfig.ResolvedGatewayConfigSetting, apiVersion string) (map[string]interface{}, error) {
		return nil, errors.New("conversion failed")
	}

	req, err := http.NewRequest("POST", "/api/v3/gateway/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d error due to template conversion failure, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test with no template settings (empty config path)
func TestHandler_NoTemplateSettings(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}
	getGatewayConfigWithTemplates = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, []gatewayConfig.ResolvedGatewayConfigSetting, error) {
		// Return gateway info successfully, but no template settings (simulating the actual behavior)
		return &dbGatewayInfo{
			Id: "123e4567-e89b-12d3-a456-************", OrganizationId: "org1", APIKey: "key", Token: "abc",
		}, nil, nil
	}
	getDeviceSettings = func(pg connect.DatabaseExecutor, dbSoftwareGatewayId string) (*[]softwareGateway.DeviceSettings, error) {
		return &[]softwareGateway.DeviceSettings{}, nil
	}
	setNewGatewayToken = func(pg connect.DatabaseExecutor, gatewayId string, gatewayVersion string) (*softwareGateway.CloudSettings, error) {
		return &softwareGateway.CloudSettings{Token: "new-token"}, nil
	}
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "fake-private-key", "fake-public-key", nil
	}

	req, err := http.NewRequest("POST", "/api/v3/gateway/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusOK {
		t.Errorf("Expected HTTP %d success when no template settings (should use empty config), got %d", http.StatusOK, rr.Code)
	}

	// Verify response contains empty gateway settings
	var response struct {
		Data softwareGateway.GlobalSettings `json:"data"`
	}
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	// Should have empty gateway config
	gateway := response.Data.Gateway
	if len(gateway) != 0 {
		t.Errorf("Expected empty gateway config when no template settings, got %v", gateway)
	}
}

// Test nil gatewayConfigMap check
func TestHandler_NilGatewayConfigMap(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}
	getGatewayConfigWithTemplates = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, []gatewayConfig.ResolvedGatewayConfigSetting, error) {
		templateSettings := []gatewayConfig.ResolvedGatewayConfigSetting{
			{Setting: "test_setting", Value: "test_value", Format: ""},
		}
		return &dbGatewayInfo{
			Id: "123e4567-e89b-12d3-a456-************", OrganizationId: "org1", APIKey: "key", Token: "abc",
		}, templateSettings, nil
	}
	convertSettingsToGatewaySettingsWithVersion = func(settings []gatewayConfig.ResolvedGatewayConfigSetting, apiVersion string) (map[string]interface{}, error) {
		// Return nil map to trigger the nil check
		return nil, nil
	}
	getDeviceSettings = func(pg connect.DatabaseExecutor, dbSoftwareGatewayId string) (*[]softwareGateway.DeviceSettings, error) {
		return &[]softwareGateway.DeviceSettings{}, nil
	}
	setNewGatewayToken = func(pg connect.DatabaseExecutor, gatewayId string, gatewayVersion string) (*softwareGateway.CloudSettings, error) {
		return &softwareGateway.CloudSettings{Token: "new-token"}, nil
	}
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "fake-private-key", "fake-public-key", nil
	}

	req, err := http.NewRequest("POST", "/api/v3/gateway/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusOK {
		t.Errorf("Expected HTTP %d success when gatewayConfigMap is nil (should initialize empty map), got %d", http.StatusOK, rr.Code)
	}

	// Verify response contains empty gateway settings (since we returned nil from conversion)
	var response struct {
		Data softwareGateway.GlobalSettings `json:"data"`
	}
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	// Should have empty gateway config (initialized by nil check)
	gateway := response.Data.Gateway
	if len(gateway) != 0 {
		t.Errorf("Expected empty gateway config when conversion returns nil, got %v", gateway)
	}
}

// Test getGatewayConfigWithTemplates with valid UUID but no template found
func TestGetGatewayConfigWithTemplates_ValidUUIDNoTemplate(t *testing.T) {
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
			// Return a valid UUID that won't have template config
			return map[string]interface{}{
				"Id":             "123e4567-e89b-12d3-a456-************",
				"OrganizationId": "org1",
				"APIKey":         "key1",
				"Token":          "token1",
			}, nil
		},
	}

	// This should hit the GetSoftwareGatewayByIdentifier call but likely return not found
	gatewayInfo, templateSettings, err := getGatewayConfigWithTemplates(fakeDB, "test-gateway", "test-token")

	// Should succeed but may not have template settings
	if err == nil {
		t.Logf("Test passed: Valid UUID handled, gatewayInfo=%+v, templateSettings=%v", gatewayInfo, templateSettings)
	} else {
		t.Logf("Got error (expected for missing template): %v", err)
	}
}

// Test getGatewayConfigWithTemplates with invalid UUID
func TestGetGatewayConfigWithTemplates_InvalidUUID(t *testing.T) {
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
			return map[string]interface{}{
				"Id":             "invalid-uuid", // This will trigger UUID parse error
				"OrganizationId": "org1",
				"APIKey":         "key1",
				"Token":          "token1",
			}, nil
		},
	}

	// This should hit the invalid UUID path
	gatewayInfo, templateSettings, err := getGatewayConfigWithTemplates(fakeDB, "test-gateway", "test-token")

	// Should succeed but with no template settings for invalid UUID
	if err == nil && templateSettings == nil {
		t.Logf("Test passed: invalid UUID gracefully handled, gatewayInfo=%+v", gatewayInfo)
	} else {
		t.Errorf("Expected success with no template settings for invalid UUID, got err=%v", err)
	}
}

// Test getGatewayConfigWithTemplates function directly to cover the real implementation
// This tests the actual GetSoftwareGatewayByIdentifier call paths that are missing coverage

func TestGetGatewayConfigWithTemplates_GetSoftwareGatewayByIdentifier_Success(t *testing.T) {
	defer restoreOverrides()

	// Don't override getGatewayConfigWithTemplates - test the real implementation
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			gatewayInfo, ok := dest.(*dbGatewayInfo)
			if !ok {
				return errors.New("dest must be of type *dbGatewayInfo")
			}
			*gatewayInfo = dbGatewayInfo{
				Id:             "123e4567-e89b-12d3-a456-************",
				OrganizationId: "org1",
				APIKey:         "key1",
				Token:          "token1",
			}
			return nil
		},
	}

	// Override getSoftwareGatewayByIdentifierFunc to simulate success
	getSoftwareGatewayByIdentifierFunc = func(pg connect.DatabaseExecutor, gatewayUUID uuid.UUID) (*gatewayConfig.SoftwareGateway, []gatewayConfig.ResolvedGatewayConfigSetting, error) {
		return &gatewayConfig.SoftwareGateway{}, []gatewayConfig.ResolvedGatewayConfigSetting{
			{Setting: "test_setting", Value: "test_value"},
		}, nil
	}

	gatewayInfo, templateSettings, err := getGatewayConfigWithTemplates(fakeDB, "test-gateway", "test-token")
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if gatewayInfo == nil {
		t.Fatal("Expected gatewayInfo to be returned")
	}
	if templateSettings == nil {
		t.Fatal("Expected templateSettings to be returned")
	}
	if len(templateSettings) != 1 {
		t.Errorf("Expected 1 template setting, got %d", len(templateSettings))
	}
}

func TestGetGatewayConfigWithTemplates_GetSoftwareGatewayByIdentifier_NotFound(t *testing.T) {
	defer restoreOverrides()

	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			gatewayInfo, ok := dest.(*dbGatewayInfo)
			if !ok {
				return errors.New("dest must be of type *dbGatewayInfo")
			}
			*gatewayInfo = dbGatewayInfo{
				Id:             "123e4567-e89b-12d3-a456-************",
				OrganizationId: "org1",
				APIKey:         "key1",
				Token:          "token1",
			}
			return nil
		},
	}

	// Override getSoftwareGatewayByIdentifierFunc to simulate not found
	getSoftwareGatewayByIdentifierFunc = func(pg connect.DatabaseExecutor, gatewayUUID uuid.UUID) (*gatewayConfig.SoftwareGateway, []gatewayConfig.ResolvedGatewayConfigSetting, error) {
		return nil, nil, gatewayConfig.ErrSoftwareGatewayConfigNotFound
	}

	gatewayInfo, templateSettings, err := getGatewayConfigWithTemplates(fakeDB, "test-gateway", "test-token")
	if err != nil {
		t.Fatalf("Expected no error (should handle not found gracefully), got: %v", err)
	}
	if gatewayInfo == nil {
		t.Fatal("Expected gatewayInfo to be returned")
	}
	if templateSettings != nil {
		t.Errorf("Expected templateSettings to be nil when not found, got %v", templateSettings)
	}
}

func TestGetGatewayConfigWithTemplates_GetSoftwareGatewayByIdentifier_OtherError(t *testing.T) {
	defer restoreOverrides()

	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			gatewayInfo, ok := dest.(*dbGatewayInfo)
			if !ok {
				return errors.New("dest must be of type *dbGatewayInfo")
			}
			*gatewayInfo = dbGatewayInfo{
				Id:             "123e4567-e89b-12d3-a456-************",
				OrganizationId: "org1",
				APIKey:         "key1",
				Token:          "token1",
			}
			return nil
		},
	}

	// Override getSoftwareGatewayByIdentifierFunc to simulate other error
	getSoftwareGatewayByIdentifierFunc = func(pg connect.DatabaseExecutor, gatewayUUID uuid.UUID) (*gatewayConfig.SoftwareGateway, []gatewayConfig.ResolvedGatewayConfigSetting, error) {
		return nil, nil, errors.New("database connection failed")
	}

	gatewayInfo, templateSettings, err := getGatewayConfigWithTemplates(fakeDB, "test-gateway", "test-token")
	if err != nil {
		t.Fatalf("Expected no error (should handle other errors gracefully), got: %v", err)
	}
	if gatewayInfo == nil {
		t.Fatal("Expected gatewayInfo to be returned")
	}
	if templateSettings != nil {
		t.Errorf("Expected templateSettings to be nil when error occurs, got %v", templateSettings)
	}
}

// Test convertValueUsingFormat function directly
func TestConvertValueUsingFormat(t *testing.T) {
	tests := []struct {
		name      string
		setting   string
		value     string
		format    string
		expected  interface{}
		expectErr bool
	}{
		{
			name:     "integer conversion",
			setting:  "ws_port",
			value:    "8080",
			format:   `{"type":"integer","versions":["v3"]}`,
			expected: 8080,
		},
		{
			name:     "boolean true conversion",
			setting:  "ws_active",
			value:    "true",
			format:   `{"type":"boolean","versions":["v3"]}`,
			expected: true,
		},
		{
			name:     "boolean false conversion",
			setting:  "send_logs",
			value:    "false",
			format:   `{"type":"boolean","versions":["v3"]}`,
			expected: false,
		},
		{
			name:     "string conversion",
			setting:  "log_level",
			value:    "info",
			format:   `{"type":"string","versions":["v3"]}`,
			expected: "info",
		},
		{
			name:     "array conversion with empty array",
			setting:  "debug_level_device_ids",
			value:    "[]",
			format:   `{"type":"array","versions":["v3"]}`,
			expected: []interface{}{},
		},
		{
			name:     "array conversion with values",
			setting:  "debug_level_device_ids",
			value:    `["device1","device2"]`,
			format:   `{"type":"array","versions":["v3"]}`,
			expected: []interface{}{"device1", "device2"},
		},
		{
			name:     "empty integer value",
			setting:  "timeout",
			value:    "",
			format:   `{"type":"integer","versions":["v3"]}`,
			expected: 0,
		},
		{
			name:     "empty boolean value",
			setting:  "enabled",
			value:    "",
			format:   `{"type":"boolean","versions":["v3"]}`,
			expected: false,
		},
		{
			name:     "empty array value",
			setting:  "devices",
			value:    "",
			format:   `{"type":"array","versions":["v3"]}`,
			expected: []interface{}{},
		},
		{
			name:      "invalid integer",
			setting:   "port",
			value:     "not-a-number",
			format:    `{"type":"integer","versions":["v3"]}`,
			expectErr: true,
		},
		{
			name:      "invalid boolean",
			setting:   "active",
			value:     "maybe",
			format:    `{"type":"boolean","versions":["v3"]}`,
			expectErr: true,
		},
		{
			name:      "invalid array",
			setting:   "devices",
			value:     "not-json",
			format:    `{"type":"array","versions":["v3"]}`,
			expectErr: true,
		},
		{
			name:      "invalid format JSON",
			setting:   "test",
			value:     "value",
			format:    `invalid json`,
			expectErr: true,
		},
		{
			name:     "unknown type defaults to string",
			setting:  "custom",
			value:    "test_value",
			format:   `{"type":"unknown","versions":["v3"]}`,
			expected: "test_value",
		},
		{
			name:     "text type backward compatibility",
			setting:  "legacy_setting",
			value:    "legacy_value",
			format:   `{"type":"text","versions":["v3"]}`,
			expected: "legacy_value",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := convertValueUsingFormat(tt.setting, tt.value, tt.format)

			if tt.expectErr {
				if err == nil {
					t.Errorf("Expected error but got none for setting %s", tt.setting)
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error for setting %s: %v", tt.setting, err)
				return
			}

			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("convertValueUsingFormat(%q, %q, %q) = %v (type %T), expected %v (type %T)",
					tt.setting, tt.value, tt.format, result, result, tt.expected, tt.expected)
			}
		})
	}
}

// TestIsSettingSupportedForVersion tests the version filtering logic
func TestIsSettingSupportedForVersion(t *testing.T) {
	tests := []struct {
		name       string
		formatJSON string
		version    string
		expected   bool
	}{
		{
			name:       "version v3 supported",
			formatJSON: `{"type":"string","versions":["v3"]}`,
			version:    "v3",
			expected:   true,
		},
		{
			name:       "version v3 not supported",
			formatJSON: `{"type":"string","versions":["v2"]}`,
			version:    "v3",
			expected:   false,
		},
		{
			name:       "multiple versions including v3",
			formatJSON: `{"type":"string","versions":["v2","v3","v4"]}`,
			version:    "v3",
			expected:   true,
		},
		{
			name:       "multiple versions not including v3",
			formatJSON: `{"type":"string","versions":["v1","v2","v4"]}`,
			version:    "v3",
			expected:   false,
		},
		{
			name:       "empty versions array defaults to true",
			formatJSON: `{"type":"string","versions":[]}`,
			version:    "v3",
			expected:   true,
		},
		{
			name:       "no versions field defaults to true",
			formatJSON: `{"type":"string"}`,
			version:    "v3",
			expected:   true,
		},
		{
			name:       "invalid JSON defaults to true",
			formatJSON: `invalid json`,
			version:    "v3",
			expected:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isSettingSupportedForVersion(tt.formatJSON, tt.version)
			if result != tt.expected {
				t.Errorf("isSettingSupportedForVersion(%q, %q) = %v, expected %v",
					tt.formatJSON, tt.version, result, tt.expected)
			}
		})
	}
}

// TestExtractAPIVersion tests the API version extraction from URL paths
func TestExtractAPIVersion(t *testing.T) {
	tests := []struct {
		name     string
		urlPath  string
		expected string
	}{
		{
			name:     "v3 API path",
			urlPath:  "/api/v3/gateway/authenticate",
			expected: "v3",
		},
		{
			name:     "v2 API path",
			urlPath:  "/api/v2/gateway/authenticate",
			expected: "v2",
		},
		{
			name:     "v1 API path",
			urlPath:  "/api/v1/gateway/authenticate",
			expected: "v1",
		},
		{
			name:     "v10 API path",
			urlPath:  "/api/v10/gateway/authenticate",
			expected: "v10",
		},
		{
			name:     "no version in path",
			urlPath:  "/api/gateway/authenticate",
			expected: "v1", // default
		},
		{
			name:     "malformed path",
			urlPath:  "/some/random/path",
			expected: "v1", // default
		},
		{
			name:     "root path",
			urlPath:  "/",
			expected: "v1", // default
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := extractAPIVersion(tt.urlPath)
			if result != tt.expected {
				t.Errorf("extractAPIVersion(%q) = %q, expected %q", tt.urlPath, result, tt.expected)
			}
		})
	}
}

// TestConvertSettingsToGatewaySettings_VersionFiltering tests that settings are filtered by version
func TestConvertSettingsToGatewaySettings_VersionFiltering(t *testing.T) {
	defer restoreOverrides()

	settings := []gatewayConfig.ResolvedGatewayConfigSetting{
		{
			Setting: "v3_setting",
			Value:   "v3_value",
			Format:  `{"type":"string","versions":["v3"]}`,
		},
		{
			Setting: "v2_setting",
			Value:   "v2_value",
			Format:  `{"type":"string","versions":["v2"]}`,
		},
		{
			Setting: "multi_version_setting",
			Value:   "multi_value",
			Format:  `{"type":"string","versions":["v2","v3","v4"]}`,
		},
		{
			Setting: "legacy_setting",
			Value:   "legacy_value",
			Format:  `{"type":"string"}`, // No versions field - should be included
		},
	}

	// Test with v3
	result, err := convertSettingsToGatewaySettingsWithVersion(settings, "v3")
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
		return
	}

	// Should only include v3_setting, multi_version_setting, and legacy_setting
	expected := map[string]interface{}{
		"v3_setting":            "v3_value",
		"multi_version_setting": "multi_value",
		"legacy_setting":        "legacy_value",
	}

	if len(result) != len(expected) {
		t.Errorf("Expected %d settings, got %d", len(expected), len(result))
	}

	for key, expectedValue := range expected {
		if actualValue, exists := result[key]; !exists {
			t.Errorf("Expected setting %s to be present", key)
		} else if actualValue != expectedValue {
			t.Errorf("Setting %s: expected %v, got %v", key, expectedValue, actualValue)
		}
	}

	// Ensure v2-only setting is not included
	if _, exists := result["v2_setting"]; exists {
		t.Errorf("Setting v2_setting should not be included for v3 API")
	}

	// Test with v2
	resultV2, err := convertSettingsToGatewaySettingsWithVersion(settings, "v2")
	if err != nil {
		t.Errorf("Unexpected error for v2: %v", err)
		return
	}

	// Should only include v2_setting, multi_version_setting, and legacy_setting
	expectedV2 := map[string]interface{}{
		"v2_setting":            "v2_value",
		"multi_version_setting": "multi_value",
		"legacy_setting":        "legacy_value",
	}

	if len(resultV2) != len(expectedV2) {
		t.Errorf("Expected %d settings for v2, got %d", len(expectedV2), len(resultV2))
	}

	// Ensure v3-only setting is not included in v2 results
	if _, exists := resultV2["v3_setting"]; exists {
		t.Errorf("Setting v3_setting should not be included for v2 API")
	}
}

// TestConvertSettingsToGatewaySettings_Legacy tests the legacy function that defaults to v3
func TestConvertSettingsToGatewaySettings_Legacy(t *testing.T) {
	defer restoreOverrides()

	settings := []gatewayConfig.ResolvedGatewayConfigSetting{
		{
			Setting: "v3_setting",
			Value:   "v3_value",
			Format:  `{"type":"string","versions":["v3"]}`,
		},
		{
			Setting: "v2_setting",
			Value:   "v2_value",
			Format:  `{"type":"string","versions":["v2"]}`,
		},
	}

	// Test the legacy function (should default to v3)
	result, err := convertSettingsToGatewaySettings(settings)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
		return
	}

	// Should only include v3_setting (since legacy function defaults to v3)
	if _, exists := result["v3_setting"]; !exists {
		t.Errorf("Expected v3_setting to be present in legacy function result")
	}

	// Should NOT include v2_setting (since legacy function defaults to v3)
	if _, exists := result["v2_setting"]; exists {
		t.Errorf("v2_setting should not be included when legacy function defaults to v3")
	}
}

// TestConvertSettingsToGatewaySettingsWithVersion_ErrorHandling tests error handling in conversion
func TestConvertSettingsToGatewaySettingsWithVersion_ErrorHandling(t *testing.T) {
	defer restoreOverrides()

	settings := []gatewayConfig.ResolvedGatewayConfigSetting{
		{
			Setting: "valid_setting",
			Value:   "valid_value",
			Format:  `{"type":"string","versions":["v3"]}`,
		},
		{
			Setting: "invalid_format_setting",
			Value:   "some_value",
			Format:  `{"type":"integer","versions":["v3"]}`, // integer format but non-numeric value
		},
		{
			Setting: "malformed_json_setting",
			Value:   "another_value",
			Format:  `invalid json format`, // This will cause JSON parse error
		},
	}

	// Test with v3 - should handle errors gracefully
	result, err := convertSettingsToGatewaySettingsWithVersion(settings, "v3")
	if err != nil {
		t.Errorf("Unexpected error from conversion function: %v", err)
		return
	}

	// Should include the valid setting
	if _, exists := result["valid_setting"]; !exists {
		t.Errorf("Expected valid_setting to be present")
	}

	// Should include the invalid format setting as fallback string value
	if result["invalid_format_setting"] != "some_value" {
		t.Errorf("Expected invalid_format_setting to fallback to original string value, got %v", result["invalid_format_setting"])
	}

	// Should include the malformed JSON setting as fallback string value
	if result["malformed_json_setting"] != "another_value" {
		t.Errorf("Expected malformed_json_setting to fallback to original string value, got %v", result["malformed_json_setting"])
	}
}
