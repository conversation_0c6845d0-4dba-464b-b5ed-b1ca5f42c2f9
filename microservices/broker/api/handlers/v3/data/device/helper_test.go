package device

import (
	"reflect"
	"testing"
	"time"

	helper "synapse-its.com/shared/devices/edi/helper"
)

func TestAddRedisToPayload(t *testing.T) {
	baseTime := time.Date(2021, time.February, 3, 4, 5, 6, 7, time.UTC)
	header := &helper.HeaderRecord{
		FirmwareRevision: helper.ConvertByteToDecimalFormat(11),
		FirmwareVersion:  helper.ConvertByteToDecimalFormat(22),
		CommVersion:      helper.ConvertByteToDecimalFormat(33),
	}

	tests := []struct {
		name      string
		status    *helper.RmsStatusRecord
		wantState string
	}{
		{
			name: "device not faulted",
			status: &helper.RmsStatusRecord{
				DeviceModel: "123",
				MonitorTime: baseTime,
				Fault:       "nofault",
				IsFaulted:   false,
			},
			wantState: "nofault",
		},
		{
			name: "device faulted",
			status: &helper.RmsStatusRecord{
				DeviceModel: "123",
				MonitorTime: baseTime,
				Fault:       "some fault",
				IsFaulted:   true,
			},
			wantState: "faulted",
		},
		{
			name: "device faulted with different fault reason",
			status: &helper.RmsStatusRecord{
				DeviceModel: "123",
				MonitorTime: baseTime,
				Fault:       "another fault",
				IsFaulted:   true,
			},
			wantState: "faulted",
		},
		{
			name: "device not faulted with fault reason present",
			status: &helper.RmsStatusRecord{
				DeviceModel: "123",
				MonitorTime: baseTime,
				Fault:       "cleared fault",
				IsFaulted:   false,
			},
			wantState: "nofault",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dp := &dataPayload{
				Metadata: deviceMetadata{},
				Status:   deviceStatus{},
			}

			addRedisToPayload(dp, header, tt.status)

			// Test metadata fields
			if dp.Metadata.Model != "123" {
				t.Errorf("Model = %q; want %q", dp.Metadata.Model, "123")
			}
			if dp.Metadata.FirmwareType != "1.6" {
				t.Errorf("FirmwareType = %q; want %q", dp.Metadata.FirmwareType, "1.6")
			}
			if dp.Metadata.FirmwareVersion != "0.11" {
				t.Errorf("FirmwareVersion = %q; want %q", dp.Metadata.FirmwareVersion, "0.11")
			}
			if dp.Metadata.CommVersion != "2.1" {
				t.Errorf("CommVersion = %q; want %q", dp.Metadata.CommVersion, "2.1")
			}

			// Test status fields
			wantTime := baseTime.UTC().Format(time.RFC3339Nano)
			if dp.Status.HeartbeatReceivedUTC != wantTime {
				t.Errorf("HeartbeatReceivedUTC = %q; want %q", dp.Status.HeartbeatReceivedUTC, wantTime)
			}

			// Test the fault state logic
			if dp.Status.State != tt.wantState {
				t.Errorf("State = %q; want %q", dp.Status.State, tt.wantState)
			}
		})
	}
}

func TestConvertPgDeviceInfos(t *testing.T) {
	dt := time.Date(2022, time.January, 2, 3, 4, 5, 6, time.UTC)
	mt := time.Date(2022, time.February, 3, 4, 5, 6, 7, time.UTC)
	dbEnabled := []pgDeviceInfo{
		{
			MonitorTime:               mt,
			Fault:                     "error1",
			ChannelGreenStatus:        []bool{true, false},
			ChannelYellowStatus:       []bool{false, true},
			ChannelRedStatus:          []bool{},
			ID:                        1,
			DeviceID:                  "dev1",
			Latitude:                  "10.1",
			Longitude:                 "20.2",
			IPAddress:                 "*******",
			Port:                      "8080",
			MonitorID:                 7,
			MonitorName:               "mon1",
			EngineVersion:             "2",
			EngineRevision:            "3",
			DateUploadedUTC:           dt,
			SoftwareGatewayIdentifier: "sgw",
			IsEnabled:                 true,
		},
	}

	// Test data for disabled device (should result in "nevercomm" state)
	dbDisabled := []pgDeviceInfo{
		{
			MonitorTime:               mt,
			Fault:                     "error1",
			ChannelGreenStatus:        []bool{true, false},
			ChannelYellowStatus:       []bool{false, true},
			ChannelRedStatus:          []bool{},
			ID:                        2,
			DeviceID:                  "dev2",
			Latitude:                  "10.1",
			Longitude:                 "20.2",
			IPAddress:                 "*******",
			Port:                      "8080",
			MonitorID:                 8,
			MonitorName:               "mon2",
			EngineVersion:             "2",
			EngineRevision:            "3",
			DateUploadedUTC:           dt,
			SoftwareGatewayIdentifier: "sgw",
			IsEnabled:                 false,
		},
	}

	tests := []struct {
		name    string
		input   *[]pgDeviceInfo
		wantNil bool
		wantLen int
		want0   dataPayload
	}{
		{name: "nil input", input: nil, wantNil: true},
		{name: "empty slice", input: &[]pgDeviceInfo{}, wantNil: false, wantLen: 0},
		{
			name: "enabled device", input: &dbEnabled, wantNil: false, wantLen: 1,
			want0: dataPayload{
				DeviceID:         1,
				DeviceIdentifier: "dev1",
				Location: location{
					Latitude:  "10.1",
					Longitude: "20.2",
				},
				Status: deviceStatus{
					State:                "error",
					HeartbeatReceivedUTC: "",
					LogUploadedUTC:       dt.Format(time.RFC3339Nano),
					LastFaultReason:      "error1",
					LastFaultUploadedUTC: mt.Format(time.RFC3339Nano),
					FaultedChannelStatus: channelStatus{
						ChannelRed:    []bool{},
						ChannelYellow: []bool{false, true},
						ChannelGreen:  []bool{true, false},
					},
				},
				Metadata: deviceMetadata{
					Manufacturer:            "EDI",
					Model:                   "",
					UserAssignedDeviceID:    "7",
					UserAssignedDeviceName:  "mon1",
					ApplicationVersion:      "",
					FirmwareType:            "",
					FirmwareVersion:         "",
					CommVersion:             "",
					RmsEngineFirmwareType:   "2",
					RmsEngineFirmwareVerson: "3",
					IPAddress:               "*******",
					IPort:                   "8080",
				},
			},
		},
		{
			name: "disabled device", input: &dbDisabled, wantNil: false, wantLen: 1,
			want0: dataPayload{
				DeviceID:         2,
				DeviceIdentifier: "dev2",
				Location: location{
					Latitude:  "10.1",
					Longitude: "20.2",
				},
				Status: deviceStatus{
					State:                "nevercomm",
					HeartbeatReceivedUTC: "",
					LogUploadedUTC:       dt.Format(time.RFC3339Nano),
					LastFaultReason:      "error1",
					LastFaultUploadedUTC: mt.Format(time.RFC3339Nano),
					FaultedChannelStatus: channelStatus{
						ChannelRed:    []bool{},
						ChannelYellow: []bool{false, true},
						ChannelGreen:  []bool{true, false},
					},
				},
				Metadata: deviceMetadata{
					Manufacturer:            "EDI",
					Model:                   "",
					UserAssignedDeviceID:    "8",
					UserAssignedDeviceName:  "mon2",
					ApplicationVersion:      "",
					FirmwareType:            "",
					FirmwareVersion:         "",
					CommVersion:             "",
					RmsEngineFirmwareType:   "2",
					RmsEngineFirmwareVerson: "3",
					IPAddress:               "*******",
					IPort:                   "8080",
				},
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got := convertPgDeviceInfos(tc.input)
			if tc.wantNil {
				if got != nil {
					t.Errorf("convertPgDeviceInfos(%v) = %v; want nil", tc.input, got)
				}
				return
			}
			if got == nil {
				t.Errorf("convertPgDeviceInfos(%v) returned nil; want non-nil", tc.input)
				return
			}
			if len(*got) != tc.wantLen {
				t.Errorf("len = %d; want %d", len(*got), tc.wantLen)
				return
			}
			if tc.wantLen > 0 {
				if !reflect.DeepEqual((*got)[0], tc.want0) {
					t.Errorf("element[0] = %+v; want %+v", (*got)[0], tc.want0)
				}
			}
		})
	}
}
