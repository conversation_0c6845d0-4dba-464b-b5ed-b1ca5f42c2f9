package device

import (
	"time"
)

type pgDeviceInfo struct {
	MonitorTime               time.Time `db:"monitortime"`
	Fault                     string    `db:"fault"`
	ChannelGreenStatus        []bool    `db:"channelgreenstatus"`
	ChannelYellowStatus       []bool    `db:"channelyellowstatus"`
	ChannelRedStatus          []bool    `db:"channelredstatus"`
	ID                        int       `db:"id"`
	DeviceID                  string    `db:"deviceid"`
	DeviceType                string    `db:"devicetype"`
	Latitude                  string    `db:"latitude"`
	Longitude                 string    `db:"longitude"`
	IPAddress                 string    `db:"ipaddress"`
	Port                      string    `db:"port"`
	MonitorID                 int       `db:"monitorid"`
	MonitorName               string    `db:"monitorname"`
	EngineVersion             string    `db:"engineversion"`
	EngineRevision            string    `db:"enginerevision"`
	DateUploadedUTC           time.Time `db:"dateuploadedutc"`
	SoftwareGatewayIdentifier string    `db:"softwaregatewayidentifier"`
	GatewayVersion            string    `db:"gatewayversion"`
	IsEnabled                 bool      `db:"isenabled"`
	SerialNumber              string    `db:"serialnumber"`
}

// dataPayload represents the complete device information response
type dataPayload struct {
	DeviceID         int            `json:"device_id" example:"12345"`                   // Numeric device identifier
	DeviceIdentifier string         `json:"device_identifier" example:"device-uuid-123"` // Device UUID or identifier string
	Location         location       `json:"location"`                                    // Geographic location information
	Status           deviceStatus   `json:"status"`                                      // Current device status and fault information
	Metadata         deviceMetadata `json:"device_info"`                                 // Device hardware and software metadata
}

// location represents the geographic coordinates of the device
type location struct {
	Latitude  string `json:"latitude" example:"40.7128"`   // Device latitude coordinate
	Longitude string `json:"longitude" example:"-74.0060"` // Device longitude coordinate
}

// deviceMetadata contains hardware and software information about the device
type deviceMetadata struct {
	Manufacturer            string `json:"manufacturer" example:"EDI"`                  // Device manufacturer name
	DeviceType              string `json:"device_type" example:"EDI_LEGACY"`            // Type classification of the device
	Model                   string `json:"model" example:"Model-X1"`                    // Device model identifier
	UserAssignedDeviceID    string `json:"user_assigned_device_id" example:"DEVICE001"` // User-defined device identifier
	UserAssignedDeviceName  string `json:"user_assigned_device_name" example:"Main St"` // User-friendly device name
	ApplicationVersion      string `json:"application_version" example:""`              // Application software version
	FirmwareType            string `json:"firmware_type" example:""`                    // Firmware type classification
	FirmwareVersion         string `json:"firmware_version" example:"1.5.2"`            // Firmware version number
	CommVersion             string `json:"comm_version" example:"3.0.1"`                // Communication protocol version
	RmsEngineFirmwareType   string `json:"rms_engine_firmware_type" example:"1.0.0"`    // RMS engine firmware type
	RmsEngineFirmwareVerson string `json:"rms_engine_firmware_version" example:"4.2.1"` // RMS engine firmware version
	IPAddress               string `json:"ip_address" example:"*************"`          // Device IP address
	IPort                   string `json:"port" example:"8080"`                         // Device communication port
	SerialNumber            string `json:"serial_number" example:"1234567890ABCD"`      // Device serial number (for EDI Next Gen devices)
}

// deviceStatus contains current status and last fault information
type deviceStatus struct {
	State                string        `json:"state" example:"nofault"`                                // Current device state (nofault, fault, nevercomm, etc.)
	HeartbeatReceivedUTC string        `json:"heartbeat_received_utc" example:"2024-01-15T10:30:00Z"`  // Timestamp of last heartbeat received
	LogUploadedUTC       string        `json:"log_uploaded_utc" example:"2024-01-15T10:25:00Z"`        // Timestamp of last log upload
	LastFaultReason      string        `json:"last_fault_reason" example:"Communication timeout"`      // Description of the most recent fault
	LastFaultUploadedUTC string        `json:"last_fault_uploaded_utc" example:"2024-01-15T09:15:00Z"` // Timestamp when the last fault was reported
	FaultedChannelStatus channelStatus `json:"faulted_channel_status"`                                 // The state of the channels of the device during the last fault
}

// channelStatus represents the last faulted status of device channels
type channelStatus struct {
	ChannelRed    []bool `json:"red"`    // Red channel status array (true = on)
	ChannelYellow []bool `json:"yellow"` // Yellow channel status array (true = on)
	ChannelGreen  []bool `json:"green"`  // Green channel status array (true = on)
}
