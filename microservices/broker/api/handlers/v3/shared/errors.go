package shared

// StandardResponse represents the common response structure used across all API endpoints
type StandardResponse struct {
	Status  string      `json:"status" example:"success"`            // Response status ("success" or "error")
	Data    interface{} `json:"data"`                                // Response data (null for error responses)
	Message string      `json:"message" example:"Request Succeeded"` // Human-readable message
	Code    int         `json:"code" example:"200"`                  // HTTP status code
}

// ErrorResponse represents the standard error response structure
type ErrorResponse struct {
	Status  string      `json:"status" example:"error"`              // Response status (always "error")
	Data    interface{} `json:"data"`                                // Always null for error responses
	Message string      `json:"message" example:"An error occurred"` // Human-readable error message
	Code    int         `json:"code" example:"500"`                  // HTTP status code
}

// BadRequestResponse represents a 400 Bad Request error
type BadRequestResponse struct {
	Status  string      `json:"status" example:"error"`        // Response status
	Data    interface{} `json:"data"`                          // Always null for error responses
	Message string      `json:"message" example:"Bad Request"` // Error message
	Code    int         `json:"code" example:"400"`            // HTTP status code
}

// UnauthorizedResponse represents a 401 Unauthorized error
type UnauthorizedResponse struct {
	Status  string      `json:"status" example:"error"`         // Response status
	Data    interface{} `json:"data"`                           // Always null for error responses
	Message string      `json:"message" example:"Unauthorized"` // Error message
	Code    int         `json:"code" example:"401"`             // HTTP status code
}

// ForbiddenResponse represents a 403 Forbidden error
type ForbiddenResponse struct {
	Status  string      `json:"status" example:"forbidden"`  // Response status (note: uses "forbidden" not "error")
	Data    interface{} `json:"data"`                        // Always null for error responses
	Message string      `json:"message" example:"Forbidden"` // Error message
	Code    int         `json:"code" example:"403"`          // HTTP status code
}

// NotFoundResponse represents a 404 Not Found error
type NotFoundResponse struct {
	Status  string      `json:"status" example:"error"`      // Response status
	Data    interface{} `json:"data"`                        // Always null for error responses
	Message string      `json:"message" example:"Not Found"` // Error message
	Code    int         `json:"code" example:"404"`          // HTTP status code
}

// InternalServerErrorResponse represents a 500 Internal Server Error
type InternalServerErrorResponse struct {
	Status  string      `json:"status" example:"error"`                  // Response status
	Data    interface{} `json:"data"`                                    // Always null for error responses
	Message string      `json:"message" example:"Internal Server Error"` // Error message
	Code    int         `json:"code" example:"500"`                      // HTTP status code
}
