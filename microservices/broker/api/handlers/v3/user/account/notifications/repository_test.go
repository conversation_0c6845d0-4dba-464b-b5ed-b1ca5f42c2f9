package notifications

import (
	"database/sql"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	dbexecutor "synapse-its.com/shared/mocks"
)

// stubResult implements sql.Result for testing.
type stubResult struct {
	rows int64
	err  error
}

func (r *stubResult) LastInsertId() (int64, error) { return 0, nil }
func (r *stubResult) RowsAffected() (int64, error) { return r.rows, r.err }

func Test_v3UserAccountNotificationsRepository_UpdateNotificationSmsEnabled(t *testing.T) {
	tests := []struct {
		name                   string
		userID                 string
		notificationSmsEnabled bool
		fakeSetup              func(*dbexecutor.FakeDBExecutor)
		wantErr                bool
	}{
		{
			name:                   "successful update",
			userID:                 "123",
			notificationSmsEnabled: true,
			fakeSetup: func(fake *dbexecutor.FakeDBExecutor) {
				fake.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					assert.Equal(t, "UPDATE {{User}} SET NotificationSmsEnabled = $1 WHERE Id = $2", query)
					assert.Equal(t, true, args[0])
					assert.Equal(t, "123", args[1])
					return &stubResult{rows: 1, err: nil}, nil
				}
			},
			wantErr: false,
		},
		{
			name:                   "database error",
			userID:                 "123",
			notificationSmsEnabled: true,
			fakeSetup: func(fake *dbexecutor.FakeDBExecutor) {
				fake.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("exec failed")
				}
			},
			wantErr: true,
		},
		{
			name:                   "rows affected error",
			userID:                 "123",
			notificationSmsEnabled: true,
			fakeSetup: func(fake *dbexecutor.FakeDBExecutor) {
				fake.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &stubResult{rows: 0, err: errors.New("rows error")}, nil
				}
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt // Create new variable for parallel test
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			fake := &dbexecutor.FakeDBExecutor{}
			tt.fakeSetup(fake)

			repo := &v3UserAccountNotificationsRepository{
				db: fake,
			}

			err := repo.UpdateNotificationSmsEnabled(tt.userID, tt.notificationSmsEnabled)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_getV3UserAccountNotificationsRepository(t *testing.T) {
	tests := []struct {
		name  string
		conns *connect.Connections
		want  *v3UserAccountNotificationsRepository
	}{
		{
			name: "success",
			conns: &connect.Connections{
				Postgres: &dbexecutor.FakeDBExecutor{},
			},
			want: &v3UserAccountNotificationsRepository{
				db: &dbexecutor.FakeDBExecutor{},
			},
		},
	}

	for _, tt := range tests {
		tt := tt // Create new variable for parallel test
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			got := getV3UserAccountNotificationsRepository(tt.conns)
			assert.Equal(t, tt.want, got)
		})
	}
}
