package notifications

import (
	"context"
	"encoding/json"
	"net/http"

	"github.com/google/uuid"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	UserPermissionsFromContext func(ctx context.Context) (*authorizer.UserPermissions, bool)
	UUIDGenerator              func() (uuid.UUID, error)
	GetConnections             func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	GetRepository              func(conns *connect.Connections) Repository
}

// HandlerWithDeps returns an http.HandlerFunc with injected deps.
//
// @Summary      Update user notification preferences
// @Description  Updates the authenticated user's SMS notification preferences. This endpoint allows users to enable or disable SMS notifications for their account. The setting is stored in the user's profile and affects whether they receive SMS alerts for device events, fault notifications, and other system alerts.
// @Tags         env:dev, env:qa, env:sandbox, user
// @Accept       json
// @Produce      json
// @Security     JWTAuth
// @Param        body  body      notifications.Request     true   "Notification preferences update request"
// @Success      200   {object}  notifications.Response    "Notification preferences updated successfully"
// @Failure      400   {object}  shared.BadRequestResponse              "Bad Request"
// @Failure      401   {object}  shared.UnauthorizedResponse            "Unauthorized"
// @Failure      500   {object}  shared.InternalServerErrorResponse     "Internal Server Error"
// @Router       /v3/user/account/notifications [post]
func HandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get user info
		userPermissions, ok := deps.UserPermissionsFromContext(ctx)
		if !ok {
			logger.Error("Unable to retrieve user info from request context")
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get connections
		conns, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("error - getting connections. err: (%v)", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Parse request
		var request Request
		err = json.NewDecoder(r.Body).Decode(&request)
		if err != nil {
			logger.Errorf("error - parsing request body. err: (%v)", err)
			response.CreateBadRequestResponse(w)
			return
		}

		repository := deps.GetRepository(conns)
		err = repository.UpdateNotificationSmsEnabled(userPermissions.UserID, request.NotificationSmsEnabled)
		if err != nil {
			logger.Errorf("error - updating notification sms for user failed. user_id: (%v), err: (%v)", userPermissions.UserID, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(Response{
			UserID: userPermissions.UserID,
			TraceID: func() string {
				trace, err := deps.UUIDGenerator()
				if err != nil {
					logger.Errorf("error - generating uuid. err: (%v)\n", err)
					return "unexpected"
				}
				return trace.String()
			}(),
			NotificationSmsEnabled: request.NotificationSmsEnabled,
		}, w)
	}
}

// Handler is the production-ready HTTP handler using default deps.
var Handler = HandlerWithDeps(HandlerDeps{
	UserPermissionsFromContext: authorizer.UserPermissionsFromContext,
	UUIDGenerator:              uuid.NewRandom,
	GetConnections:             connect.GetConnections,
	GetRepository:              getV3UserAccountNotificationsRepository,
})
