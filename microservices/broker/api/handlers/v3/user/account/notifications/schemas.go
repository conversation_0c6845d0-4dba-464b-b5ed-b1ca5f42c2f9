package notifications

// Request represents the notification preferences update request body
type Request struct {
	NotificationSmsEnabled bool `json:"notification_sms_enabled" example:"true" binding:"required"` // Enable or disable SMS notifications for the user
}

// Response represents the notification preferences update response
type Response struct {
	UserID                 string `json:"user_id" example:"user-uuid-123"`         // User's unique identifier
	TraceID                string `json:"trace_id" example:"trace-abc-456"`        // Request trace identifier for debugging
	NotificationSmsEnabled bool   `json:"notification_sms_enabled" example:"true"` // Current SMS notification setting after update
}
