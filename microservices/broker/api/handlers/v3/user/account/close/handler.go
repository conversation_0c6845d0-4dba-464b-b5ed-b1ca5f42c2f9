package close

import (
	"context"
	"net/http"

	authorizer "synapse-its.com/shared/api/authorizer"
	response "synapse-its.com/shared/api/response"
	connect "synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	UserPermissionsFromContext func(ctx context.Context) (*authorizer.UserPermissions, bool)
	GetConnections             func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	InactivateAccount          func(db connect.DatabaseExecutor, userID string) error
	RemoveTokens               func(db connect.DatabaseExecutor, userID string) error
}

// HandlerWithDeps returns an http.HandlerFunc with injected deps.
//
// @Summary      Close user account
// @Description  Permanently closes the authenticated user's account by marking it as deleted and removing all associated authentication tokens. This action is irreversible - once an account is closed, the user will no longer be able to authenticate or access any system resources. All active JWT tokens for the user are immediately invalidated and removed from the database.
// @Tags         env:dev, env:qa, env:sandbox, user
// @Produce      json
// @Security     JWTAuth
// @Success      200   {string}  string                                  "Account closed successfully"
// @Failure      401   {object}  shared.UnauthorizedResponse            "Unauthorized"
// @Failure      500   {object}  shared.InternalServerErrorResponse     "Internal Server Error"
// @Router       /v3/user/account/close [post]
func HandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get user info
		userPermissions, ok := deps.UserPermissionsFromContext(ctx)
		if !ok {
			logger.Error("Unable to retrieve user info from request context")
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get connections
		conns, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("%v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := conns.Postgres

		// Inactivate account
		if err := deps.InactivateAccount(pg, userPermissions.UserID); err != nil {
			logger.Errorf("error - setting user account to inactive. user_id: (%v), err: (%v)", userPermissions.UserID, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Remove tokens
		if err := deps.RemoveTokens(pg, userPermissions.UserID); err != nil {
			logger.Errorf("error - removing user's tokens. user_id: (%v), err: (%v)", userPermissions.UserID, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse("", w)
	}
}

// Handler is the production-ready HTTP handler using default deps.
var Handler = HandlerWithDeps(HandlerDeps{
	UserPermissionsFromContext: authorizer.UserPermissionsFromContext,
	GetConnections:             connect.GetConnections,
	InactivateAccount:          inactivateUserAccount,
	RemoveTokens:               removeUserTokens,
})

// inactivateUserAccount and removeUserTokens remain unchanged below.
var inactivateUserAccount = func(db connect.DatabaseExecutor, userID string) error {
	query := "UPDATE {{User}} SET IsDeleted = true WHERE Id = $1"
	result, err := db.Exec(query, userID)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	logger.Debugf("UserId (%v) has been inactivated, row(s) affected = (%v)", userID, rowsAffected)
	return nil
}

var removeUserTokens = func(db connect.DatabaseExecutor, userID string) error {
	query := "DELETE FROM {{UserToken}} WHERE UserId = $1"
	result, err := db.Exec(query, userID)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	logger.Debugf("(%v) UserTokens for UserId (%v) have been deleted", rowsAffected, userID)
	return nil
}
