package close

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"

	authorizer "synapse-its.com/shared/api/authorizer"
	connect "synapse-its.com/shared/connect"
	dbexecutor "synapse-its.com/shared/mocks"
)

// TestHandlerWithDeps_UserInfoMissing ensures a 500 when no user info is found.
func TestHandlerWithDeps_UserInfoMissing(t *testing.T) {
	t.Parallel()
	h := HandlerWithDeps(HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return nil, false
		},
		GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
			t.Fatal("GetConnections should not be called when user info is missing")
			return nil, nil
		},
		InactivateAccount: func(db connect.DatabaseExecutor, id string) error {
			t.<PERSON>al("InactivateAccount should not be called when user info is missing")
			return nil
		},
		RemoveTokens: func(db connect.DatabaseExecutor, id string) error {
			t.Fatal("RemoveTokens should not be called when user info is missing")
			return nil
		},
	})

	req := httptest.NewRequest("POST", "/close", nil)
	rr := httptest.NewRecorder()

	h(rr, req)
	assert.Equal(t, http.StatusInternalServerError, rr.Code)

	var body map[string]interface{}
	err := json.Unmarshal(rr.Body.Bytes(), &body)
	assert.NoError(t, err)
	assert.Equal(t, "error", body["status"])
	assert.Equal(t, "Internal Server Error", body["message"])
	assert.Equal(t, float64(http.StatusInternalServerError), body["code"])
	assert.Nil(t, body["data"])
}

// TestHandlerWithDeps_GetConnectionsError ensures a 500 when GetConnections fails.
func TestHandlerWithDeps_GetConnectionsError(t *testing.T) {
	t.Parallel()
	h := HandlerWithDeps(HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return &authorizer.UserPermissions{UserID: "1"}, true
		},
		GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
			return nil, errors.New("conn fail")
		},
		InactivateAccount: func(db connect.DatabaseExecutor, id string) error {
			t.Fatal("InactivateAccount should not be called when conn fails")
			return nil
		},
		RemoveTokens: func(db connect.DatabaseExecutor, id string) error {
			t.Fatal("RemoveTokens should not be called when conn fails")
			return nil
		},
	})

	req := httptest.NewRequest("POST", "/close", nil)
	rr := httptest.NewRecorder()

	h(rr, req)
	assert.Equal(t, http.StatusInternalServerError, rr.Code)

	var body map[string]interface{}
	err := json.Unmarshal(rr.Body.Bytes(), &body)
	assert.NoError(t, err)
	assert.Equal(t, "error", body["status"])
}

// TestHandlerWithDeps_InactivateError ensures a 500 when InactivateAccount fails.
func TestHandlerWithDeps_InactivateError(t *testing.T) {
	t.Parallel()
	h := HandlerWithDeps(HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return &authorizer.UserPermissions{UserID: "2"}, true
		},
		GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
			return &connect.Connections{Postgres: nil}, nil
		},
		InactivateAccount: func(db connect.DatabaseExecutor, id string) error {
			return errors.New("fail inactivate")
		},
		RemoveTokens: func(db connect.DatabaseExecutor, id string) error {
			t.Fatal("RemoveTokens should not be called when inactivate fails")
			return nil
		},
	})

	req := httptest.NewRequest("POST", "/close", nil)
	rr := httptest.NewRecorder()

	h(rr, req)
	assert.Equal(t, http.StatusInternalServerError, rr.Code)
}

// TestHandlerWithDeps_RemoveTokensError ensures a 500 when RemoveTokens fails.
func TestHandlerWithDeps_RemoveTokensError(t *testing.T) {
	t.Parallel()
	h := HandlerWithDeps(HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return &authorizer.UserPermissions{UserID: "3"}, true
		},
		GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
			return &connect.Connections{Postgres: nil}, nil
		},
		InactivateAccount: func(db connect.DatabaseExecutor, id string) error { return nil },
		RemoveTokens: func(db connect.DatabaseExecutor, id string) error {
			return errors.New("fail remove tokens")
		},
	})

	req := httptest.NewRequest("POST", "/close", nil)
	rr := httptest.NewRecorder()

	h(rr, req)
	assert.Equal(t, http.StatusInternalServerError, rr.Code)
}

// TestHandlerWithDeps_Success ensures a 200 and expected JSON on success.
func TestHandlerWithDeps_Success(t *testing.T) {
	t.Parallel()
	h := HandlerWithDeps(HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return &authorizer.UserPermissions{UserID: "4"}, true
		},
		GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
			return &connect.Connections{Postgres: nil}, nil
		},
		InactivateAccount: func(db connect.DatabaseExecutor, id string) error { return nil },
		RemoveTokens:      func(db connect.DatabaseExecutor, id string) error { return nil },
	})

	req := httptest.NewRequest("POST", "/close", nil)
	rr := httptest.NewRecorder()

	h(rr, req)
	assert.Equal(t, http.StatusOK, rr.Code)

	var body map[string]interface{}
	err := json.Unmarshal(rr.Body.Bytes(), &body)
	assert.NoError(t, err)
	assert.Equal(t, "success", body["status"])
	assert.Equal(t, float64(http.StatusOK), body["code"])
	assert.Equal(t, "Request Succeeded", body["message"])
	assert.Equal(t, "", body["data"])
}

// stubResult implements sql.Result for testing.
type stubResult struct {
	rows int64
	err  error
}

func (r *stubResult) LastInsertId() (int64, error) { return 0, nil }
func (r *stubResult) RowsAffected() (int64, error) { return r.rows, r.err }

// Tests for inactivateUserAccount
func Test_inactivateUserAccount_Success(t *testing.T) {
	t.Parallel()
	const userID = "123"
	const expectedQuery = "UPDATE {{User}} SET IsDeleted = true WHERE Id = $1"

	fake := &dbexecutor.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			assert.Equal(t, expectedQuery, query)
			assert.Equal(t, userID, args[0])
			return &stubResult{rows: 1, err: nil}, nil
		},
	}

	err := inactivateUserAccount(fake, userID)
	assert.NoError(t, err)
}

func Test_inactivateUserAccount_ExecError(t *testing.T) {
	t.Parallel()
	fake := &dbexecutor.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return nil, errors.New("exec failed")
		},
	}

	err := inactivateUserAccount(fake, "1")
	assert.EqualError(t, err, "exec failed")
}

func Test_inactivateUserAccount_RowsError(t *testing.T) {
	t.Parallel()
	fake := &dbexecutor.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return &stubResult{rows: 0, err: errors.New("rows error")}, nil
		},
	}

	err := inactivateUserAccount(fake, "1")
	assert.EqualError(t, err, "rows error")
}

// Tests for removeUserTokens
func Test_removeUserTokens_Success(t *testing.T) {
	t.Parallel()
	const userID = "456"
	const expectedQuery = "DELETE FROM {{UserToken}} WHERE UserId = $1"

	fake := &dbexecutor.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			assert.Equal(t, expectedQuery, query)
			assert.Equal(t, userID, args[0])
			return &stubResult{rows: 2, err: nil}, nil
		},
	}

	err := removeUserTokens(fake, userID)
	assert.NoError(t, err)
}

func Test_removeUserTokens_ExecError(t *testing.T) {
	t.Parallel()
	fake := &dbexecutor.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return nil, errors.New("delete failed")
		},
	}

	err := removeUserTokens(fake, "1")
	assert.EqualError(t, err, "delete failed")
}

func Test_removeUserTokens_RowsError(t *testing.T) {
	t.Parallel()
	fake := &dbexecutor.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return &stubResult{rows: 0, err: errors.New("rows err")}, nil
		},
	}

	err := removeUserTokens(fake, "1")
	assert.EqualError(t, err, "rows err")
}
