package passwordreset

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/api/password"
	"synapse-its.com/shared/connect"
)

func TestHandlerWithDeps_Success(t *testing.T) {
	// Setup test data with proper UUIDs
	userID := "123e4567-e89b-12d3-a456-************"
	authMethodID := "987fcdeb-51a2-43d1-b654-************"
	userPermissions := &authorizer.UserPermissions{
		UserID: userID,
	}

	// Setup request body
	requestBody := password.PasswordUpdateRequest{
		CurrentPassword: "CurrentPassword123!",
		NewPassword:     "NewSecurePassword123!",
		ConfirmPassword: "NewSecurePassword123!",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/password-reset", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("jwt-token", "test-jwt-token")

	// Setup mocks
	connections := &connect.Connections{Postgres: nil} // We don't need actual DB for this test

	// Mock password hasher
	mockPasswordHasher := password.NewPasswordHasher()

	// Mock dependencies
	deps := HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return userPermissions, true
		},
		GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
			return connections, nil
		},
		GetAuthMethodIdFromToken: func(pg connect.DatabaseExecutor, jwtToken string) (string, error) {
			return authMethodID, nil
		},
		ValidateAuthMethod: func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
			return nil // Success
		},
		ValidateCurrentPassword: func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, currentPassword string) error {
			return nil // Success
		},
		UpdatePassword: func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, newPasswordHash string) error {
			return nil // Success
		},
		PasswordHasher: mockPasswordHasher,
	}

	// Execute
	rr := httptest.NewRecorder()
	handler := HandlerWithDeps(deps)
	handler(rr, req)

	// Assert success
	assert.Equal(t, http.StatusOK, rr.Code)
}

func TestHandlerWithDeps_Unauthorized_NoUserPermissions(t *testing.T) {
	// Setup request body
	requestBody := password.PasswordUpdateRequest{
		CurrentPassword: "CurrentPassword123!",
		NewPassword:     "NewSecurePassword123!",
		ConfirmPassword: "NewSecurePassword123!",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/password-reset", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("jwt-token", "test-jwt-token")

	// Mock dependencies - no user permissions
	deps := HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return nil, false
		},
		GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
			return nil, nil
		},
		GetAuthMethodIdFromToken: func(pg connect.DatabaseExecutor, jwtToken string) (string, error) {
			return "", nil
		},
		ValidateCurrentPassword: func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, currentPassword string) error {
			return nil
		},
	}

	// Execute
	rr := httptest.NewRecorder()
	handler := HandlerWithDeps(deps)
	handler(rr, req)

	// Assert
	assert.Equal(t, http.StatusInternalServerError, rr.Code)
}

func TestHandlerWithDeps_InternalError_GetConnectionsFails(t *testing.T) {
	// Setup test data
	userID := "test-user-id"
	userPermissions := &authorizer.UserPermissions{
		UserID: userID,
	}

	// Setup request body
	requestBody := password.PasswordUpdateRequest{
		CurrentPassword: "CurrentPassword123!",
		NewPassword:     "NewSecurePassword123!",
		ConfirmPassword: "NewSecurePassword123!",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/password-reset", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("jwt-token", "test-jwt-token")

	// Mock dependencies - GetConnections fails
	deps := HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return userPermissions, true
		},
		GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
			return nil, errors.New("database connection failed")
		},
		GetAuthMethodIdFromToken: func(pg connect.DatabaseExecutor, jwtToken string) (string, error) {
			return "", nil
		},
		ValidateCurrentPassword: func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, currentPassword string) error {
			return nil
		},
	}

	// Execute
	rr := httptest.NewRecorder()
	handler := HandlerWithDeps(deps)
	handler(rr, req)

	// Assert
	assert.Equal(t, http.StatusInternalServerError, rr.Code)
}

func TestHandlerWithDeps_Unauthorized_NoJWTToken(t *testing.T) {
	// Setup test data
	userID := "test-user-id"
	userPermissions := &authorizer.UserPermissions{
		UserID: userID,
	}

	// Setup request body
	requestBody := password.PasswordUpdateRequest{
		CurrentPassword: "CurrentPassword123!",
		NewPassword:     "NewSecurePassword123!",
		ConfirmPassword: "NewSecurePassword123!",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/password-reset", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	// Note: No JWT token header set

	// Setup mocks
	connections := &connect.Connections{Postgres: nil}

	// Mock dependencies
	deps := HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return userPermissions, true
		},
		GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
			return connections, nil
		},
		GetAuthMethodIdFromToken: func(pg connect.DatabaseExecutor, jwtToken string) (string, error) {
			return "", nil
		},
		ValidateCurrentPassword: func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, currentPassword string) error {
			return nil
		},
	}

	// Execute
	rr := httptest.NewRecorder()
	handler := HandlerWithDeps(deps)
	handler(rr, req)

	// Assert
	assert.Equal(t, http.StatusUnauthorized, rr.Code)
}

func TestHandlerWithDeps_NotFound_AuthMethodNotFound(t *testing.T) {
	// Setup test data
	userID := "test-user-id"
	userPermissions := &authorizer.UserPermissions{
		UserID: userID,
	}

	// Setup request body
	requestBody := password.PasswordUpdateRequest{
		CurrentPassword: "CurrentPassword123!",
		NewPassword:     "NewSecurePassword123!",
		ConfirmPassword: "NewSecurePassword123!",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/password-reset", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("jwt-token", "test-jwt-token")

	// Setup mocks
	connections := &connect.Connections{Postgres: nil}

	// Mock dependencies - auth method not found
	deps := HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return userPermissions, true
		},
		GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
			return connections, nil
		},
		GetAuthMethodIdFromToken: func(pg connect.DatabaseExecutor, jwtToken string) (string, error) {
			return "", errors.New("auth method not found for token")
		},
	}

	// Execute
	rr := httptest.NewRecorder()
	handler := HandlerWithDeps(deps)
	handler(rr, req)

	// Assert
	assert.Equal(t, http.StatusUnauthorized, rr.Code)
}

func TestHandler_ProductionHandler(t *testing.T) {
	// Test that the production Handler variable is properly initialized
	assert.NotNil(t, Handler)

	// Test with a simple request to ensure it doesn't panic
	req := httptest.NewRequest(http.MethodPost, "/v3/user/password-reset", nil)
	rr := httptest.NewRecorder()

	// This should not panic and should return unauthorized (no JWT context)
	Handler(rr, req)
	assert.Equal(t, http.StatusInternalServerError, rr.Code)
}

func TestHandlerWithDeps_BadRequest_InvalidAuthMethodID(t *testing.T) {
	// Setup test data
	userID := "123e4567-e89b-12d3-a456-************"
	userPermissions := &authorizer.UserPermissions{
		UserID: userID,
	}

	// Setup request body
	requestBody := password.PasswordUpdateRequest{
		CurrentPassword: "CurrentPassword123!",
		NewPassword:     "NewSecurePassword123!",
		ConfirmPassword: "NewSecurePassword123!",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/password-reset", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("jwt-token", "test-jwt-token")

	// Setup mocks
	connections := &connect.Connections{Postgres: nil}

	// Mock dependencies - return invalid UUID for auth method ID
	deps := HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return userPermissions, true
		},
		GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
			return connections, nil
		},
		GetAuthMethodIdFromToken: func(pg connect.DatabaseExecutor, jwtToken string) (string, error) {
			return "invalid-uuid", nil
		},
		ValidateAuthMethod: func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
			return nil
		},
		UpdatePassword: func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, newPasswordHash string) error {
			return nil
		},
		PasswordHasher: password.NewPasswordHasher(),
	}

	// Execute
	rr := httptest.NewRecorder()
	handler := HandlerWithDeps(deps)
	handler(rr, req)

	// Assert
	assert.Equal(t, http.StatusBadRequest, rr.Code)
}

func TestHandlerWithDeps_BadRequest_InvalidUserID(t *testing.T) {
	// Setup test data with invalid user ID
	userPermissions := &authorizer.UserPermissions{
		UserID: "invalid-uuid",
	}

	// Setup request body
	requestBody := password.PasswordUpdateRequest{
		CurrentPassword: "CurrentPassword123!",
		NewPassword:     "NewSecurePassword123!",
		ConfirmPassword: "NewSecurePassword123!",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/password-reset", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("jwt-token", "test-jwt-token")

	// Setup mocks
	connections := &connect.Connections{Postgres: nil}
	authMethodID := "987fcdeb-51a2-43d1-b654-************"

	// Mock dependencies
	deps := HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return userPermissions, true
		},
		GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
			return connections, nil
		},
		GetAuthMethodIdFromToken: func(pg connect.DatabaseExecutor, jwtToken string) (string, error) {
			return authMethodID, nil
		},
		ValidateAuthMethod: func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
			return nil
		},
		UpdatePassword: func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, newPasswordHash string) error {
			return nil
		},
		PasswordHasher: password.NewPasswordHasher(),
	}

	// Execute
	rr := httptest.NewRecorder()
	handler := HandlerWithDeps(deps)
	handler(rr, req)

	// Assert
	assert.Equal(t, http.StatusBadRequest, rr.Code)
}

func TestHandlerWithDeps_Forbidden_ValidateCurrentPasswordError(t *testing.T) {
	// Setup test data
	userID := "123e4567-e89b-12d3-a456-************"
	authMethodID := "987fcdeb-51a2-43d1-b654-************"
	userPermissions := &authorizer.UserPermissions{
		UserID: userID,
	}

	// Setup request body
	requestBody := password.PasswordUpdateRequest{
		CurrentPassword: "WrongPassword123!",
		NewPassword:     "NewSecurePassword123!",
		ConfirmPassword: "NewSecurePassword123!",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/password-reset", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("jwt-token", "test-jwt-token")

	// Setup mocks
	connections := &connect.Connections{Postgres: nil}

	// Mock dependencies - ValidateCurrentPassword fails
	deps := HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return userPermissions, true
		},
		GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
			return connections, nil
		},
		GetAuthMethodIdFromToken: func(pg connect.DatabaseExecutor, jwtToken string) (string, error) {
			return authMethodID, nil
		},
		ValidateAuthMethod: func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
			return nil
		},
		ValidateCurrentPassword: func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, currentPassword string) error {
			return errors.New("current password validation failed")
		},
		UpdatePassword: func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, newPasswordHash string) error {
			return nil
		},
		PasswordHasher: password.NewPasswordHasher(),
	}

	// Execute
	rr := httptest.NewRecorder()
	handler := HandlerWithDeps(deps)
	handler(rr, req)

	// Assert
	assert.Equal(t, http.StatusForbidden, rr.Code)
}

func TestHandlerWithDeps_Forbidden_ValidateAuthMethodError(t *testing.T) {
	// Setup test data
	userID := "123e4567-e89b-12d3-a456-************"
	authMethodID := "987fcdeb-51a2-43d1-b654-************"
	userPermissions := &authorizer.UserPermissions{
		UserID: userID,
	}

	// Setup request body
	requestBody := password.PasswordUpdateRequest{
		CurrentPassword: "CurrentPassword123!",
		NewPassword:     "NewSecurePassword123!",
		ConfirmPassword: "NewSecurePassword123!",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/password-reset", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("jwt-token", "test-jwt-token")

	// Setup mocks
	connections := &connect.Connections{Postgres: nil}

	// Mock dependencies - ValidateAuthMethod fails
	deps := HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return userPermissions, true
		},
		GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
			return connections, nil
		},
		GetAuthMethodIdFromToken: func(pg connect.DatabaseExecutor, jwtToken string) (string, error) {
			return authMethodID, nil
		},
		ValidateAuthMethod: func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
			return errors.New("auth method validation failed")
		},
		UpdatePassword: func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, newPasswordHash string) error {
			return nil
		},
		PasswordHasher: password.NewPasswordHasher(),
	}

	// Execute
	rr := httptest.NewRecorder()
	handler := HandlerWithDeps(deps)
	handler(rr, req)

	// Assert
	assert.Equal(t, http.StatusForbidden, rr.Code)
}

func TestHandlerWithDeps_BadRequest_InvalidRequestBody(t *testing.T) {
	// Setup test data
	userID := "123e4567-e89b-12d3-a456-************"
	authMethodID := "987fcdeb-51a2-43d1-b654-************"
	userPermissions := &authorizer.UserPermissions{
		UserID: userID,
	}

	// Setup invalid request body (missing required fields)
	invalidBody := `{"invalid": "data"}`
	req := httptest.NewRequest(http.MethodPost, "/v3/user/password-reset", bytes.NewBufferString(invalidBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("jwt-token", "test-jwt-token")

	// Setup mocks
	connections := &connect.Connections{Postgres: nil}

	// Mock dependencies
	deps := HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return userPermissions, true
		},
		GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
			return connections, nil
		},
		GetAuthMethodIdFromToken: func(pg connect.DatabaseExecutor, jwtToken string) (string, error) {
			return authMethodID, nil
		},
		ValidateAuthMethod: func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
			return nil
		},
		UpdatePassword: func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, newPasswordHash string) error {
			return nil
		},
		PasswordHasher: password.NewPasswordHasher(),
	}

	// Execute
	rr := httptest.NewRecorder()
	handler := HandlerWithDeps(deps)
	handler(rr, req)

	// Assert
	assert.Equal(t, http.StatusBadRequest, rr.Code)
}

func TestHandlerWithDeps_InternalError_UpdatePasswordError(t *testing.T) {
	// Setup test data
	userID := "123e4567-e89b-12d3-a456-************"
	authMethodID := "987fcdeb-51a2-43d1-b654-************"
	userPermissions := &authorizer.UserPermissions{
		UserID: userID,
	}

	// Setup request body
	requestBody := password.PasswordUpdateRequest{
		CurrentPassword: "CurrentPassword123!",
		NewPassword:     "NewSecurePassword123!",
		ConfirmPassword: "NewSecurePassword123!",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/password-reset", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("jwt-token", "test-jwt-token")

	// Setup mocks
	connections := &connect.Connections{Postgres: nil}

	// Mock dependencies - UpdatePassword fails
	deps := HandlerDeps{
		UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
			return userPermissions, true
		},
		GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
			return connections, nil
		},
		GetAuthMethodIdFromToken: func(pg connect.DatabaseExecutor, jwtToken string) (string, error) {
			return authMethodID, nil
		},
		ValidateAuthMethod: func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
			return nil
		},
		ValidateCurrentPassword: func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, currentPassword string) error {
			return nil
		},
		UpdatePassword: func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, newPasswordHash string) error {
			return errors.New("database update failed")
		},
		PasswordHasher: password.NewPasswordHasher(),
	}

	// Execute
	rr := httptest.NewRecorder()
	handler := HandlerWithDeps(deps)
	handler(rr, req)

	// Assert
	assert.Equal(t, http.StatusInternalServerError, rr.Code)
}
