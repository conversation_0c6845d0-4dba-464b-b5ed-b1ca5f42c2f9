package passwordreset

import (
	"context"
	"net/http"

	"github.com/google/uuid"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/api/authtypes"
	"synapse-its.com/shared/api/password"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	authmethods "synapse-its.com/shared/rest/onramp/user/auth-methods"
)

// HandlerDeps bundles dependencies for injection and testing
type HandlerDeps struct {
	UserPermissionsFromContext func(ctx context.Context) (*authorizer.UserPermissions, bool)
	GetConnections             func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	GetAuthMethodIdFromToken   func(pg connect.DatabaseExecutor, jwtToken string) (string, error)
	ValidateAuthMethod         func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error
	ValidateCurrentPassword    func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, currentPassword string) error
	UpdatePassword             func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, newPasswordHash string) error
	PasswordHasher             password.PasswordHasher
}

// HandlerWithDeps returns an http.HandlerFunc with injected dependencies
// This handler gets the user from JWT context and looks up their auth method,
// then delegates to the shared reset password logic.
//
// @Summary      Reset user password
// @Description  Allows authenticated users to reset their password by providing current password, new password and confirmation.
// @Tags         env:dev, env:qa, env:sandbox, user
// @Accept       json
// @Produce      json
// @Security     JWTAuth
// @Param        body  body      password.PasswordUpdateRequest  true  "Password update request with current password"
// @Success      200   {object}  response.SuccessResponse              "Password reset successful"
// @Failure      400   {object}  response.BadRequestResponse           "Bad request - invalid password or validation failed"
// @Failure      401   {object}  response.UnauthorizedResponse         "Unauthorized"
// @Failure      403   {object}  response.ForbiddenResponse            "Forbidden - current password incorrect"
// @Failure      404   {object}  response.NotFoundResponse             "User or auth method not found"
// @Failure      500   {object}  response.InternalErrorResponse  "Internal Server Error"
// @Router       /v3/user/password-reset [post]
func HandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get user info from JWT context
		userPermissions, ok := deps.UserPermissionsFromContext(ctx)
		if !ok {
			logger.Error("unable to retrieve user info from context")
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get database connection
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Get JWT token from request header
		jwtToken := r.Header.Get("jwt-token")
		if jwtToken == "" {
			logger.Error("JWT token not found in request header")
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Get auth method ID from the JWT token
		authMethodIdStr, err := deps.GetAuthMethodIdFromToken(pg, jwtToken)
		if err != nil {
			logger.Errorf("Error getting auth method from token: %v", err)
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Parse auth method ID as UUID
		authMethodId, err := uuid.Parse(authMethodIdStr)
		if err != nil {
			logger.Errorf("Error parsing auth method ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse user ID as UUID
		userId, err := uuid.Parse(userPermissions.UserID)
		if err != nil {
			logger.Errorf("Error parsing user ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate auth method exists and type is USERNAME_PASSWORD
		err = deps.ValidateAuthMethod(pg, authMethodId, userId, string(authtypes.AuthMethodTypeUsernamePassword))
		if err != nil {
			logger.Errorf("Error validating auth method: %v", err)
			response.CreateForbiddenResponse(w)
			return
		}

		// Parse and validate request body
		requestBody, err := password.ExtractAndValidatePasswordUpdate(r.Body)
		if err != nil {
			logger.Errorf("Error validating request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate current password matches the auth method (no need to hash first)
		err = deps.ValidateCurrentPassword(pg, authMethodId, requestBody.CurrentPassword)
		if err != nil {
			logger.Errorf("Error validating current password: %v", err)
			response.CreateForbiddenResponse(w)
			return
		}

		// Hash the new password
		hashedNewPassword := deps.PasswordHasher.HashPassword(requestBody.NewPassword)
		err = deps.UpdatePassword(pg, authMethodId, hashedNewPassword)
		if err != nil {
			logger.Errorf("Error updating password: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Create response
		response.CreateSuccessResponse(nil, w)
	}
}

// Handler is the production-ready HTTP handler using default dependencies
var Handler = HandlerWithDeps(HandlerDeps{
	UserPermissionsFromContext: authorizer.UserPermissionsFromContext,
	GetConnections:             connect.GetConnections,
	GetAuthMethodIdFromToken:   authorizer.GetAuthMethodIdFromToken,
	ValidateAuthMethod:         authmethods.ValidateAuthMethod,
	ValidateCurrentPassword:    authmethods.ValidateCurrentPassword,
	UpdatePassword:             authmethods.UpdatePassword,
	PasswordHasher:             password.NewPasswordHasher(),
})
