package instruction

import "encoding/json"

// userInstructionRequest represents the device instruction request body
type userInstructionRequest struct {
	DeviceID    json.RawMessage `json:"device_id" binding:"required"`                             // Device identifier (integer ID or UUID string format)
	Instruction string          `json:"instruction" example:"get_device_logs" binding:"required"` // Instruction command to send to device
}

// userInstructionResponse represents the instruction response (currently unused)
type userInstructionResponse struct {
	Trace string `json:"trace_id" example:"trace-abc-123"` // Trace identifier for request tracking
}
