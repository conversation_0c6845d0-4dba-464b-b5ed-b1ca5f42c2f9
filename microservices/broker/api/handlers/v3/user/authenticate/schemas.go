// Package v3 encapsulates logic that is shared between v3 functionalities
package authenticate

// API Request/Response Schemas

// credentials represents the authentication request body
type credentials struct {
	Username string `json:"username" example:"<EMAIL>" binding:"required"` // User's username or email address
	Password string `json:"password" example:"securePassword123" binding:"required"`    // User's password
}

// authResponse represents the successful authentication response
type authResponse struct {
	Status  string                  `json:"status" example:"success"`                    // Response status
	Data    dataUserResponsePayload `json:"data"`                                        // Authentication data containing user info and JWT token
	Message string                  `json:"message" example:"Authentication successful"` // Success message
	Code    int                     `json:"code" example:"200"`                          // HTTP status code
}

type dataTokenPayload struct {
	Token string `json:"token"`
}

// dataUserResponsePayload is the payload that gets hydrated and added to the lambda output data attribute
type dataUserResponsePayload struct {
	User  userDetailRecord `json:"user"`                                                    // User information
	Token string           `json:"token" example:"eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."` // JWT token for authentication
}

// userDetailRecord defines the structure of the user record returned on successful authentication
type userDetailRecord struct {
	UserID         int64  `json:"user_id" example:"12345"`                 // Unique user identifier
	UserIdentifier string `json:"user_identifier" example:"user-uuid-123"` // User UUID identifier
	Username       string `json:"username" example:"<EMAIL>"` // Username/email used for login
	OrganizationID string `json:"organization_id" example:"org-uuid-456"`  // Organization UUID the user belongs to
	APIKey         string `json:"api_key" example:"api-key-789"`           // API key (legacy field)
	Role           string `json:"role" example:"admin"`                    // User's role within the organization
	// TODO: Find out if this can be removed (was from legacy code)
	// Firstname   string `json:"first_name"`
	// Lastname    string `json:"last_name"`
	// Email       string `json:"email"`
	// Mobile      string `json:"mobile"`
	// LastLogin   string `json:"last_login"`
	// Description string `json:"description"`
}

// Database Structs

// dbUser represents a user record from the database
type dbUser struct {
	Id                      string `db:"id"`
	Orig_ID                 int64  `db:"origid"`
	UserName                string `db:"username"`
	TokenDurationHours      int64  `db:"tokendurationhours"`
	OrganizationIdentifier  string `db:"organizationidentifier"`
	APIKey                  string `db:"apikey"`
	AuthMethodId            string `db:"authmethodid"`
	WebEnabled              int64  `db:"webenabled"`
	WebTokenDurationSeconds int64  `db:"webtokendurationseconds"`
	ExpirationUTC           string `db:"expirationutc"`
	Password                string `db:"password"`
	IsEnabled               int64  `db:"isenabled"`
	FailedLoginAttempts     int64  `db:"failedloginattempts"`
}

// dbSoftwareGateway represents a software gateway record from the database
type dbSoftwareGateway struct {
	SoftwareGatewayIdentifier string `db:"softwaregatewayidentifier"`
}
