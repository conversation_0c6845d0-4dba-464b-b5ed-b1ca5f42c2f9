package authenticate

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	jwttokens "synapse-its.com/shared/api/jwttokens"
	connect "synapse-its.com/shared/connect"
)

// Test_NewUserHandler tests the NewUserHandler function
func Test_NewUserHandler(t *testing.T) {
	t.Parallel()
	// Create a new user handler with default dependencies
	handler := NewUserHandler()

	// Verify that the handler is properly initialized with default dependencies
	assert.NotNil(t, handler, "Handler should not be nil")

	// Check that all dependencies are set
	assert.NotNil(t, handler.DBProvider, "DBProvider should be set")
	assert.NotNil(t, handler.PasswordHasher, "PasswordHasher should be set")
	assert.NotNil(t, handler.VerifyAndUpgrade, "VerifyAndUpgrade should be set")
	assert.NotNil(t, handler.JwtCreator, "JwtCreator should be set")
	assert.NotNil(t, handler.<PERSON>, "TokenPersister should be set")
	assert.NotNil(t, handler.UserPermissionsCreator, "UserPermissionsCreator should be set")
	assert.NotNil(t, handler.TimeProvider, "TimeProvider should be set")
	assert.NotNil(t, handler.NonceGenerator, "NonceGenerator should be set")
}

// Test_UserHandler_UserAccess tests the UserAccess method of the UserHandler struct
func Test_UserHandler_UserAccess(t *testing.T) {
	t.Parallel()
	// Define test cases using table-driven test pattern
	tests := []struct {
		name               string                                    // Required: descriptive test name
		username           string                                    // Input username
		password           string                                    // Input password
		setupMocks         func(*UserHandler, *MockDatabaseExecutor) // Mock setup function
		expectedStatusCode int                                       // Expected HTTP status code
		expectedErr        error                                     // Expected error if any
		wantErr            bool                                      // Whether error is expected
	}{
		{
			name:     "successful_login",
			username: "testuser",
			password: "password123",
			setupMocks: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_password"
				}

				// Mock ComparePassword to return true for valid password
				h.VerifyAndUpgrade = func(userID string, candidate string, storedHash string) (bool, string, error) {
					return true, "hashed_password", nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = "user123"
					user.Orig_ID = 12345
					user.TokenDurationHours = 24
					user.OrganizationIdentifier = "org1"
					user.APIKey = "apikey123"
					user.Password = "hashed_password"
				}).Return(nil)

				// Mock Exec for updating login time
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock TimeProvider
				h.TimeProvider = func() time.Time {
					return time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
				}

				// Mock UserPermissionsCreator
				h.UserPermissionsCreator = func(pg connect.DatabaseExecutor, userID string) (*jwttokens.UserPermissions, error) {
					return &jwttokens.UserPermissions{
						SoftwareGateway: []jwttokens.UserSoftwareGatewayAccess{},
						Device:          []jwttokens.UserDeviceAccess{},
					}, nil
				}

				// Mock JwtCreator
				h.JwtCreator = func(username string, duration time.Duration, userPermissions jwttokens.UserPermissions) (string, time.Time, error) {
					return "jwt-token", time.Now().Add(duration), nil
				}

				// Mock TokenPersister
				h.TokenPersister = func(pg connect.DatabaseExecutor, userID string, authMethodID string, jwt string, expiresAt time.Time) error {
					return nil
				}
			},
			expectedStatusCode: http.StatusOK,
			expectedErr:        nil,
			wantErr:            false,
		},
		{
			name:     "db_connection_error",
			username: "testuser",
			password: "password123",
			setupMocks: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider to return error
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return nil, errors.New("connection error")
				}

				// Mock password hasher to avoid nil pointer dereference
				h.PasswordHasher = func(password string) string {
					return "hashed_" + password
				}
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectedErr:        errors.New("connection error"),
			wantErr:            true,
		},
		{
			name:     "invalid_credentials_username_not_found",
			username: "testuser",
			password: "wrongpassword",
			setupMocks: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_" + password
				}

				// Mock QueryRowStruct to return no rows (user not found)
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Return(sql.ErrNoRows)

				// Note: No Exec mock needed - when user is not found, we don't try to update failed login attempts
			},
			expectedStatusCode: http.StatusUnauthorized,
			expectedErr:        ErrInvalidCredentials,
			wantErr:            true,
		},
		{
			name:     "database_query_error",
			username: "testuser",
			password: "password123",
			setupMocks: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_password"
				}

				// Mock QueryRowStruct to return a database error
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Return(errors.New("database error"))
			},
			expectedStatusCode: http.StatusUnauthorized,
			expectedErr:        ErrDatabaseQuery,
			wantErr:            true,
		},
		{
			name:     "invalid_password",
			username: "testuser",
			password: "wrongpassword",
			setupMocks: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_password"
				}

				// Mock ComparePassword to return false for invalid password
				h.VerifyAndUpgrade = func(userID string, candidate string, storedHash string) (bool, string, error) {
					return false, "", nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = "user123"
					user.Orig_ID = 12345
					user.TokenDurationHours = 24
					user.OrganizationIdentifier = "org1"
					user.APIKey = "apikey123"
					user.Password = "hashed_password"
				}).Return(nil)

				// Mock Exec for updating failed login attempts
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything).Return(mockResult, nil)
			},
			expectedStatusCode: http.StatusUnauthorized,
			expectedErr:        ErrInvalidCredentials,
			wantErr:            true,
		},
		{
			name:     "login_time_update_error_non_critical",
			username: "testuser",
			password: "password123",
			setupMocks: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_password"
				}

				// Mock VerifyAndUpgrade to return true with NO hash upgrade (empty string)
				h.VerifyAndUpgrade = func(userID string, candidate string, storedHash string) (bool, string, error) {
					return true, "", nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = "user123"
					user.Orig_ID = 12345
					user.TokenDurationHours = 24
					user.OrganizationIdentifier = "org1"
					user.APIKey = "apikey123"
					user.Password = "hashed_password"
				}).Return(nil)

				// Mock TimeProvider
				h.TimeProvider = func() time.Time {
					return time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
				}

				// Mock Exec for updating login time to return error (non-critical)
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything, mock.Anything).Return(mockResult, errors.New("update error"))

				// Mock UserPermissionsCreator
				h.UserPermissionsCreator = func(pg connect.DatabaseExecutor, userID string) (*jwttokens.UserPermissions, error) {
					return &jwttokens.UserPermissions{
						SoftwareGateway: []jwttokens.UserSoftwareGatewayAccess{},
						Device:          []jwttokens.UserDeviceAccess{},
					}, nil
				}

				// Mock JwtCreator
				h.JwtCreator = func(username string, duration time.Duration, userPermissions jwttokens.UserPermissions) (string, time.Time, error) {
					return "jwt-token", time.Now().Add(duration), nil
				}

				// Mock TokenPersister
				h.TokenPersister = func(pg connect.DatabaseExecutor, userID string, authMethodID string, jwt string, expiresAt time.Time) error {
					return nil
				}
			},
			expectedStatusCode: http.StatusOK, // Non-critical error, should still succeed
			expectedErr:        nil,
			wantErr:            false,
		},
		{
			name:     "password_verification_error",
			username: "testuser",
			password: "password123",
			setupMocks: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_password"
				}

				// Mock VerifyAndUpgrade to return error
				h.VerifyAndUpgrade = func(userID string, candidate string, storedHash string) (bool, string, error) {
					return false, "", errors.New("verification error")
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = "user123"
					user.Orig_ID = 12345
					user.TokenDurationHours = 24
					user.OrganizationIdentifier = "org1"
					user.APIKey = "apikey123"
					user.Password = "hashed_password"
				}).Return(nil)
			},
			expectedStatusCode: http.StatusUnauthorized,
			expectedErr:        ErrInvalidCredentials,
			wantErr:            true,
		},
		{
			name:     "password_hash_upgrade_success",
			username: "testuser",
			password: "password123",
			setupMocks: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_password"
				}

				// Mock VerifyAndUpgrade to return true with new hash (upgrade scenario)
				h.VerifyAndUpgrade = func(userID string, candidate string, storedHash string) (bool, string, error) {
					return true, "new_upgraded_hash", nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = "user123"
					user.Orig_ID = 12345
					user.TokenDurationHours = 24
					user.OrganizationIdentifier = "org1"
					user.APIKey = "apikey123"
					user.Password = "old_hash"
				}).Return(nil)

				// Mock TimeProvider
				h.TimeProvider = func() time.Time {
					return time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
				}

				// Mock Exec for hash update and login time update
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock UserPermissionsCreator
				h.UserPermissionsCreator = func(pg connect.DatabaseExecutor, userID string) (*jwttokens.UserPermissions, error) {
					return &jwttokens.UserPermissions{
						SoftwareGateway: []jwttokens.UserSoftwareGatewayAccess{},
						Device:          []jwttokens.UserDeviceAccess{},
					}, nil
				}

				// Mock JwtCreator
				h.JwtCreator = func(username string, duration time.Duration, userPermissions jwttokens.UserPermissions) (string, time.Time, error) {
					return "jwt-token", time.Now().Add(duration), nil
				}

				// Mock TokenPersister
				h.TokenPersister = func(pg connect.DatabaseExecutor, userID string, authMethodID string, jwt string, expiresAt time.Time) error {
					return nil
				}
			},
			expectedStatusCode: http.StatusOK,
			expectedErr:        nil,
			wantErr:            false,
		},
		{
			name:     "password_hash_upgrade_persistence_error",
			username: "testuser",
			password: "password123",
			setupMocks: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_password"
				}

				// Mock VerifyAndUpgrade to return true with new hash (upgrade scenario)
				h.VerifyAndUpgrade = func(userID string, candidate string, storedHash string) (bool, string, error) {
					return true, "new_upgraded_hash", nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = "user123"
					user.Orig_ID = 12345
					user.TokenDurationHours = 24
					user.OrganizationIdentifier = "org1"
					user.APIKey = "apikey123"
					user.Password = "old_hash"
				}).Return(nil)

				// Mock Exec for hash update to return error
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything, mock.Anything).Return(mockResult, errors.New("hash update error"))
			},
			expectedStatusCode: http.StatusUnauthorized,
			expectedErr:        errors.New("hash update error"),
			wantErr:            true,
		},
		{
			name:     "user_permissions_error",
			username: "testuser",
			password: "password123",
			setupMocks: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_password"
				}

				// Mock VerifyAndUpgrade to return true for valid password
				h.VerifyAndUpgrade = func(userID string, candidate string, storedHash string) (bool, string, error) {
					return true, "", nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = "user123"
					user.Orig_ID = 12345
					user.TokenDurationHours = 24
					user.OrganizationIdentifier = "org1"
					user.APIKey = "apikey123"
					user.Password = "hashed_password"
				}).Return(nil)

				// Mock TimeProvider
				h.TimeProvider = func() time.Time {
					return time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
				}

				// Mock Exec for updating login time
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock UserPermissionsCreator to return error
				h.UserPermissionsCreator = func(pg connect.DatabaseExecutor, userID string) (*jwttokens.UserPermissions, error) {
					return nil, errors.New("permissions error")
				}
			},
			expectedStatusCode: http.StatusUnauthorized,
			expectedErr:        ErrUserPermissions,
			wantErr:            true,
		},
		{
			name:     "jwt_creation_error",
			username: "testuser",
			password: "password123",
			setupMocks: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_password"
				}

				// Mock VerifyAndUpgrade to return true for valid password
				h.VerifyAndUpgrade = func(userID string, candidate string, storedHash string) (bool, string, error) {
					return true, "", nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = "user123"
					user.Orig_ID = 12345
					user.TokenDurationHours = 24
					user.OrganizationIdentifier = "org1"
					user.APIKey = "apikey123"
					user.Password = "hashed_password"
				}).Return(nil)

				// Mock TimeProvider
				h.TimeProvider = func() time.Time {
					return time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
				}

				// Mock Exec for updating login time
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock UserPermissionsCreator
				h.UserPermissionsCreator = func(pg connect.DatabaseExecutor, userID string) (*jwttokens.UserPermissions, error) {
					return &jwttokens.UserPermissions{
						SoftwareGateway: []jwttokens.UserSoftwareGatewayAccess{},
						Device:          []jwttokens.UserDeviceAccess{},
					}, nil
				}

				// Mock JwtCreator to return error
				h.JwtCreator = func(username string, duration time.Duration, userPermissions jwttokens.UserPermissions) (string, time.Time, error) {
					return "", time.Time{}, errors.New("JWT creation error")
				}
			},
			expectedStatusCode: http.StatusUnauthorized,
			expectedErr:        ErrJWTCreation,
			wantErr:            true,
		},
		{
			name:     "token_persistence_error",
			username: "testuser",
			password: "password123",
			setupMocks: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_password"
				}

				// Mock VerifyAndUpgrade to return true for valid password
				h.VerifyAndUpgrade = func(userID string, candidate string, storedHash string) (bool, string, error) {
					return true, "", nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = "user123"
					user.Orig_ID = 12345
					user.TokenDurationHours = 24
					user.OrganizationIdentifier = "org1"
					user.APIKey = "apikey123"
					user.Password = "hashed_password"
				}).Return(nil)

				// Mock TimeProvider
				h.TimeProvider = func() time.Time {
					return time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
				}

				// Mock Exec for updating login time
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock UserPermissionsCreator
				h.UserPermissionsCreator = func(pg connect.DatabaseExecutor, userID string) (*jwttokens.UserPermissions, error) {
					return &jwttokens.UserPermissions{
						SoftwareGateway: []jwttokens.UserSoftwareGatewayAccess{},
						Device:          []jwttokens.UserDeviceAccess{},
					}, nil
				}

				// Mock JwtCreator
				h.JwtCreator = func(username string, duration time.Duration, userPermissions jwttokens.UserPermissions) (string, time.Time, error) {
					return "jwt-token", time.Now().Add(duration), nil
				}

				// Mock TokenPersister to return error
				h.TokenPersister = func(pg connect.DatabaseExecutor, userID string, authMethodID string, jwt string, expiresAt time.Time) error {
					return errors.New("token persistence error")
				}
			},
			expectedStatusCode: http.StatusUnauthorized,
			expectedErr:        ErrTokenPersistence,
			wantErr:            true,
		},
	}

	// Execute each test case
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			// Create handler and mock DB
			handler := &UserHandler{}
			mockDB := new(MockDatabaseExecutor)

			// Setup mocks
			if tt.setupMocks != nil {
				tt.setupMocks(handler, mockDB)
			}

			// Create request and response recorder
			req, err := http.NewRequest("POST", "/authenticate", nil)
			assert.NoError(t, err, "Failed to create HTTP request")
			w := httptest.NewRecorder()

			// Call the handler
			handler.UserAccess(tt.username, tt.password, w, req)

			// Assert response status code
			assert.Equal(t, tt.expectedStatusCode, w.Code, "Expected status code %d but got %d", tt.expectedStatusCode, w.Code)

			// Check for error responses
			if tt.wantErr {
				// For error cases, verify the status code is not OK
				assert.NotEqual(t, http.StatusOK, w.Code, "Expected error status code but got OK")
			} else {
				// For success cases, verify the status code is OK
				assert.Equal(t, http.StatusOK, w.Code, "Expected OK status code but got error")
			}

			// Verify all mock expectations were met
			mockDB.AssertExpectations(t)
		})
	}
}
