package authenticate

import (
	"net/http"

	response "synapse-its.com/shared/api/response"
)

// Interfaces for testing
type UserAccessor interface {
	UserAccess(userName, password string, w http.ResponseWriter, r *http.Request)
}

// For testing purposes
var (
	newUserHandlerFunc = func() UserAccessor { return NewUserHandler() }
)

// Handler authenticates users with username/password and returns a JWT token.
//
// @Summary      Authenticate user and get JW<PERSON> token
// @Description  Authenticates a user with username and password credentials. On successful authentication, returns user information and a JWT token that must be used in the 'jwt-token' header for accessing protected endpoints. The JWT token expires after 744 hours (31 days) and contains user permissions and organization information.
// @Tags         env:dev, env:qa, env:sandbox, user
// @Accept       json
// @Produce      json
// @Param        body  body      authenticate.credentials                true   "User authentication credentials"
// @Success      200   {object}  authenticate.authResponse               "Authentication successful - returns user info and JWT token"
// @Failure      401   {object}  shared.UnauthorizedResponse             "Unauthorized - invalid credentials"
// @Failure      500   {object}  shared.InternalServerErrorResponse      "Internal Server Error"
// @Router       /v3/user/authenticate [post]
func Handler(w http.ResponseWriter, r *http.Request) {
	// Process as a normal auth request
	userName, password, err := extractUserNameAndPassword(r.Body)
	if err != nil {
		response.CreateUnauthorizedResponse(w)
		return
	}
	userHandler := newUserHandlerFunc()
	userHandler.UserAccess(userName, password, w, r)
}
