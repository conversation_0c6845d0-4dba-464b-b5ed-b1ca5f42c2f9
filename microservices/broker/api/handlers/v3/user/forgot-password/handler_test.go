package forgotpassword

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	authDomain "synapse-its.com/shared/rest/domain/auth"
	forgotPasswordShared "synapse-its.com/shared/rest/onramp/auth/forgotpassword"
)

// Mock DatabaseExecutor for testing
type mockDatabaseExecutor struct{}

func (m *mockDatabaseExecutor) Exec(query string, args ...interface{}) (sql.Result, error) {
	return nil, nil
}

func (m *mockDatabaseExecutor) ExecMultiple(queries string) error {
	return nil
}

func (m *mockDatabaseExecutor) QueryGeneric(query string, args ...interface{}) ([]map[string]interface{}, error) {
	return nil, nil
}

func (m *mockDatabaseExecutor) QueryGenericSlice(dest interface{}, query string, args ...interface{}) error {
	return nil
}

func (m *mockDatabaseExecutor) QueryRow(query string, args ...interface{}) (map[string]interface{}, error) {
	return nil, nil
}

func (m *mockDatabaseExecutor) QueryRowStruct(dest interface{}, query string, args ...interface{}) error {
	return nil
}

func (m *mockDatabaseExecutor) EscapeIdentifier(identifier string) string {
	return identifier
}

func (m *mockDatabaseExecutor) ReplaceNamespace(query string) string {
	return query
}

func (m *mockDatabaseExecutor) Close() error {
	return nil
}

// Mock PubSub client for testing
type mockPubsubClient struct {
	publishError      error
	publishedMessages []*pubsub.Message
}

type mockTopic struct {
	client *mockPubsubClient
}

type mockPublishResult struct {
	err error
}

func (m *mockPublishResult) Get(ctx context.Context) (string, error) {
	if m.err != nil {
		return "", m.err
	}
	return "message-id", nil
}

func (m *mockTopic) Publish(ctx context.Context, msg *pubsub.Message) connect.PsPublishResult {
	m.client.publishedMessages = append(m.client.publishedMessages, msg)
	return &mockPublishResult{err: m.client.publishError}
}

func (m *mockTopic) Exists(ctx context.Context) (bool, error) {
	return true, nil
}

func (m *mockPubsubClient) Topic(name string) connect.PsTopic {
	return &mockTopic{client: m}
}

func (m *mockPubsubClient) Subscription(name string) connect.PsSubscription {
	return nil // Not needed for our tests
}

func (m *mockPubsubClient) CreateTopic(ctx context.Context, name string) (connect.PsTopic, error) {
	return nil, nil // Not needed for our tests
}

func (m *mockPubsubClient) CreateSubscription(ctx context.Context, name string, cfg connect.SubscriptionConfig) (connect.PsSubscription, error) {
	return nil, nil // Not needed for our tests
}

func (m *mockPubsubClient) Close() error {
	return nil
}

func TestRequestHandler_Success(t *testing.T) {
	// Setup request
	requestBody := forgotPasswordShared.ForgotPasswordRequest{
		Username: "<EMAIL>",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/forgot-password-request", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	// Mock GetConnections to return valid connections
	originalGetConnections := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
		return &connect.Connections{
			Postgres: &mockDatabaseExecutor{}, // Mock database
			Pubsub:   &mockPubsubClient{},     // Mock PubSub client
		}, nil
	}
	defer func() {
		connect.GetConnections = originalGetConnections
	}()

	// Execute request
	rr := httptest.NewRecorder()
	RequestHandler(rr, req)

	// The handler will delegate to the shared handler, which will likely fail
	// due to the mock database, but we can verify the delegation path is reached
	// The important thing is that our handler logic (connection checks, etc.) works
	assert.NotEqual(t, 0, rr.Code) // Some response was generated
}

func TestRequestHandler_GetConnectionsError(t *testing.T) {
	// Setup request with context that will cause GetConnections to fail
	requestBody := forgotPasswordShared.ForgotPasswordRequest{
		Username: "<EMAIL>",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/forgot-password-request", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request without connections in context (GetConnections will return error)
	rr := httptest.NewRecorder()
	RequestHandler(rr, req)

	// Should return 500 due to GetConnections error
	assert.Equal(t, http.StatusInternalServerError, rr.Code)
	assert.Contains(t, rr.Body.String(), "Internal Server Error")
}

func TestRequestHandler_NilConnections(t *testing.T) {
	// Setup request with nil connections (using checkConnections=false to avoid validation panic)
	requestBody := forgotPasswordShared.ForgotPasswordRequest{
		Username: "<EMAIL>",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/forgot-password-request", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	// Setup nil connections in context - this will cause our handler's nil check to trigger
	ctx := connect.WithConnections(req.Context(), nil)
	req = req.WithContext(ctx)

	// Mock GetConnections to return nil connections without validation panic
	originalGetConnections := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
		return nil, nil // Return nil connections without error
	}
	defer func() {
		connect.GetConnections = originalGetConnections
	}()

	// Execute request
	rr := httptest.NewRecorder()
	RequestHandler(rr, req)

	// Should return 500 due to nil connections
	assert.Equal(t, http.StatusInternalServerError, rr.Code)
	assert.Contains(t, rr.Body.String(), "Database or PubSub connection not available")
}

func TestRequestHandler_NilPostgres(t *testing.T) {
	// Setup request with connections but nil Postgres
	requestBody := forgotPasswordShared.ForgotPasswordRequest{
		Username: "<EMAIL>",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/forgot-password-request", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	// Mock GetConnections to return connections with nil Postgres
	originalGetConnections := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
		return &connect.Connections{
			Postgres: nil,
		}, nil
	}
	defer func() {
		connect.GetConnections = originalGetConnections
	}()

	// Execute request
	rr := httptest.NewRecorder()
	RequestHandler(rr, req)

	// Should return 500 due to nil Postgres connection
	assert.Equal(t, http.StatusInternalServerError, rr.Code)
	assert.Contains(t, rr.Body.String(), "Database or PubSub connection not available")
}

func TestRedeemHandler_Success(t *testing.T) {
	// Setup request
	requestBody := forgotPasswordShared.ForgotPasswordRedeem{
		Token:              "valid-token-123",
		NewPassword:        "NewPassword123!",
		ConfirmNewPassword: "NewPassword123!",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/forgot-password-redeem", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	// Mock GetConnections to return valid connections
	originalGetConnections := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
		return &connect.Connections{
			Postgres: &mockDatabaseExecutor{}, // Mock database
		}, nil
	}
	defer func() {
		connect.GetConnections = originalGetConnections
	}()

	// Execute request
	rr := httptest.NewRecorder()
	RedeemHandler(rr, req)

	// The handler will delegate to the shared handler, which will likely fail
	// due to the mock database, but we can verify the delegation path is reached
	// The important thing is that our handler logic (connection checks, etc.) works
	assert.NotEqual(t, 0, rr.Code) // Some response was generated
}

func TestRedeemHandler_GetConnectionsError(t *testing.T) {
	// Setup request with context that will cause GetConnections to fail
	requestBody := forgotPasswordShared.ForgotPasswordRedeem{
		Token:              "valid-token-123",
		NewPassword:        "NewPassword123!",
		ConfirmNewPassword: "NewPassword123!",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/forgot-password-redeem", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request without connections in context (GetConnections will return error)
	rr := httptest.NewRecorder()
	RedeemHandler(rr, req)

	// Should return 500 due to GetConnections error
	assert.Equal(t, http.StatusInternalServerError, rr.Code)
	assert.Contains(t, rr.Body.String(), "Internal Server Error")
}

func TestRedeemHandler_NilConnections(t *testing.T) {
	// Setup request with nil connections
	requestBody := forgotPasswordShared.ForgotPasswordRedeem{
		Token:              "valid-token-123",
		NewPassword:        "NewPassword123!",
		ConfirmNewPassword: "NewPassword123!",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/forgot-password-redeem", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	// Mock GetConnections to return nil connections without validation panic
	originalGetConnections := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
		return nil, nil // Return nil connections without error
	}
	defer func() {
		connect.GetConnections = originalGetConnections
	}()

	// Execute request
	rr := httptest.NewRecorder()
	RedeemHandler(rr, req)

	// Should return 500 due to nil connections
	assert.Equal(t, http.StatusInternalServerError, rr.Code)
	assert.Contains(t, rr.Body.String(), "Database connection not available")
}

func TestRedeemHandler_NilPostgres(t *testing.T) {
	// Setup request with connections but nil Postgres
	requestBody := forgotPasswordShared.ForgotPasswordRedeem{
		Token:              "valid-token-123",
		NewPassword:        "NewPassword123!",
		ConfirmNewPassword: "NewPassword123!",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/forgot-password-redeem", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	// Mock GetConnections to return connections with nil Postgres
	originalGetConnections := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
		return &connect.Connections{
			Postgres: nil,
		}, nil
	}
	defer func() {
		connect.GetConnections = originalGetConnections
	}()

	// Execute request
	rr := httptest.NewRecorder()
	RedeemHandler(rr, req)

	// Should return 500 due to nil Postgres connection
	assert.Equal(t, http.StatusInternalServerError, rr.Code)
	assert.Contains(t, rr.Body.String(), "Database connection not available")
}

// TestBrokerEmailSender removed - now using PubSub-based email sender from shared package

func TestSimpleSessionManager_GetSession(t *testing.T) {
	manager := &simpleSessionManager{}

	// GetSession should always return nil, false
	session, found := manager.GetSession("session-123")
	assert.Nil(t, session)
	assert.False(t, found)
}

func TestSimpleSessionManager_SetSession(t *testing.T) {
	manager := &simpleSessionManager{}

	// SetSession should not panic (it's a no-op)
	session := &authDomain.Session{
		UserID: "user-123",
	}
	manager.SetSession("session-123", session)
	// No assertion needed - if it doesn't panic, the test passes
}

func TestSimpleSessionManager_ClearSession(t *testing.T) {
	manager := &simpleSessionManager{}

	// ClearSession should not panic (it's a no-op)
	manager.ClearSession("session-123")
	// No assertion needed - if it doesn't panic, the test passes
}

func TestSimpleSessionManager_RevokeAllUserSessions(t *testing.T) {
	manager := &simpleSessionManager{}

	// RevokeAllUserSessions should not panic (it's a no-op)
	manager.RevokeAllUserSessions("user-123")
	// No assertion needed - if it doesn't panic, the test passes
}

func TestRequestHandler_NilPubsub(t *testing.T) {
	requestBody := forgotPasswordShared.ForgotPasswordRequest{
		Username: "<EMAIL>",
	}
	jsonBody, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/v3/user/forgot-password-request", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	// Mock GetConnections to return connections with nil PubSub
	originalGetConnections := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
		return &connect.Connections{
			Postgres: &mockDatabaseExecutor{},
			Pubsub:   nil, // Nil PubSub connection
		}, nil
	}
	defer func() {
		connect.GetConnections = originalGetConnections
	}()

	rr := httptest.NewRecorder()
	RequestHandler(rr, req)

	// Should return 500 due to nil PubSub connection
	assert.Equal(t, http.StatusInternalServerError, rr.Code)
	assert.Contains(t, rr.Body.String(), "Database or PubSub connection not available")
}

// TestBrokerEmailSender_WithContext removed - now using PubSub-based email sender from shared package
