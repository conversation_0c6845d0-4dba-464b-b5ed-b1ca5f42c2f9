package forgotpassword

import (
	"net/http"

	_ "synapse-its.com/broker/api/handlers/v3/shared" // Import for Swagger documentation
	"synapse-its.com/shared/api/password"
	"synapse-its.com/shared/connect"
	authDomain "synapse-its.com/shared/rest/domain/auth"
	authPostgres "synapse-its.com/shared/rest/external/postgres"
	forgotPasswordShared "synapse-its.com/shared/rest/onramp/auth/forgotpassword"
)

// Using PubSub-based email sender from shared forgot password package

// RequestHandler handles forgot password requests without requiring authentication
//
// @Summary      Request forgot password
// @Description  Initiates a forgot password flow by sending a reset email to the user
// @Tags         env:dev, env:qa, env:sandbox, user
// @Accept       json
// @Produce      json
// @Param        body  body      forgotPasswordShared.ForgotPasswordRequest  true  "Forgot password request with username/email"
// @Success      200   {object}  shared.StandardResponse              "Reset email sent successfully"
// @Failure      400   {object}  shared.BadRequestResponse           "Bad request - invalid username or validation failed"
// @Failure      500   {object}  shared.InternalServerErrorResponse  "Internal Server Error"
// @Router       /v3/user/forgot-password-request [post]
func RequestHandler(w http.ResponseWriter, r *http.Request) {
	// Get connections from context (injected by middleware)
	connections, err := connect.GetConnections(r.Context())
	if err != nil {
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}

	// Validate connections
	if connections == nil || connections.Postgres == nil || connections.Pubsub == nil {
		http.Error(w, "Database or PubSub connection not available", http.StatusInternalServerError)
		return
	}

	// Create repository and PubSub email sender
	repo := authPostgres.NewAuthRepository(connections.Postgres)
	emailSender := forgotPasswordShared.NewPubSubEmailSender(connections.Pubsub, "etl-notifications")

	// Delegate to shared handler
	handler := forgotPasswordShared.RequestForgotPasswordHandler(repo, emailSender)
	handler(w, r)
}

// RedeemHandler handles forgot password redemption without requiring authentication
//
// @Summary      Redeem forgot password token
// @Description  Redeems a forgot password token and sets a new password
// @Tags         env:dev, env:qa, env:sandbox, user
// @Accept       json
// @Produce      json
// @Param        body  body      forgotPasswordShared.ForgotPasswordRedeem  true  "Forgot password redeem request with token and new password"
// @Success      200   {object}  shared.StandardResponse              "Password updated successfully"
// @Failure      400   {object}  shared.BadRequestResponse           "Bad request - invalid token, password, or validation failed"
// @Failure      406   {object}  shared.ErrorResponse                 "Token expired or already used"
// @Failure      500   {object}  shared.InternalServerErrorResponse  "Internal Server Error"
// @Router       /v3/user/forgot-password-redeem [post]
func RedeemHandler(w http.ResponseWriter, r *http.Request) {
	// Get connections from context (injected by middleware)
	connections, err := connect.GetConnections(r.Context())
	if err != nil {
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}

	// Validate connections
	if connections == nil || connections.Postgres == nil {
		http.Error(w, "Database connection not available", http.StatusInternalServerError)
		return
	}

	// Create repository, password hasher, and session manager
	repo := authPostgres.NewAuthRepository(connections.Postgres)
	passwordHasher := password.NewPasswordHasher()

	// For session management, we'll use a simple implementation
	// In production, this should integrate with your session store
	sessionManager := &simpleSessionManager{}

	// Delegate to shared handler
	handler := forgotPasswordShared.RedeemForgotPasswordHandler(repo, passwordHasher, sessionManager)
	handler(w, r)
}

// Simple session manager implementation for broker
type simpleSessionManager struct{}

func (s *simpleSessionManager) GetSession(sessionID string) (*authDomain.Session, bool) {
	// Broker doesn't manage sessions directly
	return nil, false
}

func (s *simpleSessionManager) SetSession(sessionID string, session *authDomain.Session) {
	// Broker doesn't manage sessions directly - no-op
}

func (s *simpleSessionManager) ClearSession(sessionID string) {
	// Broker doesn't manage sessions directly - no-op
}

func (s *simpleSessionManager) RevokeAllUserSessions(userID string) {
	// In production, this would revoke all active sessions for the user
	// For now, this is a no-op since broker doesn't manage sessions directly
}
