package synapse

import (
	"net/http"

	"github.com/gorilla/mux"
	"synapse-its.com/shared/api/middleware"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
)

// SetUpSubrouter sets up the subrouter for the synapse API
func SetUpSubrouter(r *mux.Router) {
	// Create subrouter and add middlewares
	router := r.PathPrefix("/synapse").Subrouter()
	router.Use(middleware.SynapseAuth)

	// Inject dependencies
	purgeExpiredHandler := newPurgeExpiredHandler(purgeExpiredDeps{
		getConnections: connect.GetConnections,
	})

	// Register routes
	router.HandleFunc("", defaultHandler).Methods(http.MethodGet)
	router.HandleFunc("/", defaultHandler).Methods(http.MethodGet)
	router.HandleFunc("/purge-expired", purgeExpiredHandler).Methods(http.MethodPost)
}

// defaultHandler is the default handler for the root path
func defaultHandler(w http.ResponseWriter, r *http.Request) {
	response.CreateSuccessResponse("Synapse API", w)
}
