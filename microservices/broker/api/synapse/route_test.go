package synapse

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
)

// Mock the connect.GetConnections function
var mockGetConnections func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)

// Save the original function and restore it after the test
var originalGetConnections = connect.GetConnections

func TestSetUpSubrouter(t *testing.T) {
	// Setup and teardown
	setup := func() {
		// Save original function
		originalGetConnections = connect.GetConnections
		// Replace with mock
		connect.GetConnections = mockGetConnections
	}

	teardown := func() {
		// Restore original function
		connect.GetConnections = originalGetConnections
	}

	tests := []struct {
		name           string
		path           string
		method         string
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "root path GET",
			path:           "/synapse",
			method:         http.MethodGet,
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   `{"code":401,"data":null,"message":"Unauthorized","status":"error"}`,
		},
		{
			name:           "root path with slash GET",
			path:           "/synapse/",
			method:         http.MethodGet,
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   `{"code":401,"data":null,"message":"Unauthorized","status":"error"}`,
		},
		{
			name:           "purge-expired POST",
			path:           "/synapse/purge-expired",
			method:         http.MethodPost,
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   `{"code":401,"data":null,"message":"Unauthorized","status":"error"}`,
		},
		{
			name:           "invalid path",
			path:           "/synapse/invalid",
			method:         http.MethodGet,
			expectedStatus: http.StatusNotFound,
			expectedBody:   "404 page not found\n",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			setup()
			defer teardown()

			// Create router
			router := mux.NewRouter()

			// Mock the GetConnections function
			mockGetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{}, nil
			}

			// Call the actual SetUpSubrouter function
			SetUpSubrouter(router)

			// Create test request
			req := httptest.NewRequest(tt.method, tt.path, nil)
			w := httptest.NewRecorder()

			// Execute request
			router.ServeHTTP(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.expectedBody != "" {
				if tt.name == "invalid path" {
					assert.Equal(t, tt.expectedBody, w.Body.String())
				} else {
					assert.JSONEq(t, tt.expectedBody, w.Body.String())
				}
			}
		})
	}
}

func TestDefaultHandler(t *testing.T) {
	tests := []struct {
		name           string
		method         string
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "GET request",
			method:         http.MethodGet,
			expectedStatus: http.StatusOK,
			expectedBody:   `{"code":200,"data":"Synapse API","message":"Request Succeeded","status":"success"}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test request
			req := httptest.NewRequest(tt.method, "/", nil)
			w := httptest.NewRecorder()

			// Execute handler
			defaultHandler(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.JSONEq(t, tt.expectedBody, w.Body.String())
		})
	}
}
