package routes

import (
	"net/http"

	"github.com/gorilla/mux"

	V3DataDevice "synapse-its.com/broker/api/handlers/v3/data/device"
	V3DataFault "synapse-its.com/broker/api/handlers/v3/data/fault"
	V3GatewayAuthenticate "synapse-its.com/broker/api/handlers/v3/gateway/authenticate"
	V3GatewayIngest "synapse-its.com/broker/api/handlers/v3/gateway/ingest"
	V3GatewayUpdate "synapse-its.com/broker/api/handlers/v3/gateway/update"
	V3UserAccountClose "synapse-its.com/broker/api/handlers/v3/user/account/close"
	V3UserAccountNotifications "synapse-its.com/broker/api/handlers/v3/user/account/notifications"
	V3UserAuthenticate "synapse-its.com/broker/api/handlers/v3/user/authenticate"
	V3UserForgotPassword "synapse-its.com/broker/api/handlers/v3/user/forgot-password"
	V3UserInstruction "synapse-its.com/broker/api/handlers/v3/user/instruction"
	V3UserMergeUser "synapse-its.com/broker/api/handlers/v3/user/merge-user"
	V3UserPasswordReset "synapse-its.com/broker/api/handlers/v3/user/password-reset"
	V3UserProfile "synapse-its.com/broker/api/handlers/v3/user/profile"
	V4DataDevice "synapse-its.com/broker/api/handlers/v4/data/device"
	V4DataFault "synapse-its.com/broker/api/handlers/v4/data/fault"
	V4DataInstruction "synapse-its.com/broker/api/handlers/v4/data/instruction"
	"synapse-its.com/broker/api/synapse"
	defaultapi "synapse-its.com/shared/api/handlers/defaultapi"
	"synapse-its.com/shared/api/middleware"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/httplogger"
)

// NewRouter initializes the router and registers routes.
func NewRouter(connections *connect.Connections, batch bqbatch.Batcher) *mux.Router {
	router := mux.NewRouter()

	// Setup Swagger UI
	setupSwaggerUI(router)

	// Apply middleware globally (e.g., logging, authentication).
	router.Use(middleware.ConnectionsMiddleware(connections))
	router.Use(middleware.BQBatchMiddleware(batch))
	router.Use(middleware.JWTAuthorizerMiddleware)
	router.Use(httplogger.LoggingMiddleware)

	// Define default endpoints
	router.HandleFunc("/", defaultapi.Handler).Methods(http.MethodGet)

	// Define Legacy API endpoints (will be deprecated once flutter app is updated)
	router.HandleFunc("/data/v2/device", V3DataDevice.Handler).Methods(http.MethodGet)
	router.HandleFunc("/data/v2/fault", V3DataFault.Handler).Methods(http.MethodGet)
	router.HandleFunc("/user/v2/profile", V3UserProfile.Handler).Methods(http.MethodGet)
	router.HandleFunc("/user/v2/account/close", V3UserAccountClose.Handler).Methods(http.MethodPost)
	router.HandleFunc("/user/v2/account/notifications", V3UserAccountNotifications.Handler).Methods(http.MethodPost)
	router.HandleFunc("/user/v3/authenticate", V3UserAuthenticate.Handler).Methods(http.MethodPost)
	router.HandleFunc("/user/v3/instruction", V3UserInstruction.Handler).Methods(http.MethodPost)
	
	// Forgot password endpoints (no authentication required)
	router.HandleFunc("/user/v3/forgot-password-request", V3UserForgotPassword.RequestHandler).Methods(http.MethodPost)
	router.HandleFunc("/user/v3/forgot-password-redeem", V3UserForgotPassword.RedeemHandler).Methods(http.MethodPost)

	// Setup API subrouters with organization scoping
	setupAPIRoutes(router)

	// Define Synapse API endpoints
	synapse.SetUpSubrouter(router)

	return router
}

// setupAPIRoutes configures the API subrouters with organization scoping
func setupAPIRoutes(router *mux.Router) {
	// Create main API subrouter
	apiRouter := router.PathPrefix("/api").Subrouter()

	// Setup V3 API subrouter with organization scoping
	setupV3Routes(apiRouter)

	// Setup V4 API subrouter with organization scoping
	setupV4Routes(apiRouter)
}

// setupV3Routes configures all V3 API endpoints (no organization scoping)
func setupV3Routes(apiRouter *mux.Router) {
	// Create V3 subrouter
	v3Router := apiRouter.PathPrefix("/v3").Subrouter()

	// Gateway endpoints
	v3Router.HandleFunc("/gateway/authenticate", V3GatewayAuthenticate.Handler).Methods(http.MethodPost)
	v3Router.HandleFunc("/gateway/ingest", V3GatewayIngest.Handler).Methods(http.MethodPost)
	v3Router.HandleFunc("/gateway/update", V3GatewayUpdate.Handler).Methods(http.MethodPost)

	// Data endpoints
	v3Router.HandleFunc("/data/device", V3DataDevice.Handler).Methods(http.MethodGet)
	v3Router.HandleFunc("/data/fault", V3DataFault.Handler).Methods(http.MethodGet)

	// User endpoints
	v3Router.HandleFunc("/user/account/close", V3UserAccountClose.Handler).Methods(http.MethodPost)
	v3Router.HandleFunc("/user/account/notifications", V3UserAccountNotifications.Handler).Methods(http.MethodPost)
	v3Router.HandleFunc("/user/authenticate", V3UserAuthenticate.Handler).Methods(http.MethodPost)
	v3Router.HandleFunc("/user/forgot-password-request", V3UserForgotPassword.RequestHandler).Methods(http.MethodPost)
	v3Router.HandleFunc("/user/forgot-password-redeem", V3UserForgotPassword.RedeemHandler).Methods(http.MethodPost)
	v3Router.HandleFunc("/user/instruction", V3UserInstruction.Handler).Methods(http.MethodPost)
	v3Router.HandleFunc("/user/password-reset", V3UserPasswordReset.Handler).Methods(http.MethodPost)
	v3Router.HandleFunc("/user/profile", V3UserProfile.Handler).Methods(http.MethodGet)
	v3Router.HandleFunc("/user/{userId}/merge-user", V3UserMergeUser.Handler).Methods(http.MethodPost)
}

// setupV4Routes configures all V4 API endpoints with organization scoping
func setupV4Routes(apiRouter *mux.Router) {
	// Create V4 subrouter
	v4Router := apiRouter.PathPrefix("/v4").Subrouter()

	// Organization-scoped routes (require {orgId} path parameter)
	orgRouter := v4Router.PathPrefix("/organization/{organizationId}").Subrouter()

	// Data endpoints
	orgRouter.HandleFunc("/data/device", V4DataDevice.Handler).Methods(http.MethodGet)
	orgRouter.HandleFunc("/data/fault", V4DataFault.Handler).Methods(http.MethodGet)
	orgRouter.HandleFunc("/data/instruction", V4DataInstruction.Handler).Methods(http.MethodPost)

	// Legacy route (maintain backward compatibility for existing clients)
	v4Router.HandleFunc("/data/device", V4DataDevice.Handler).Methods(http.MethodGet)
}
