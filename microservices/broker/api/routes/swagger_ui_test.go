package routes

import (
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"testing"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// helper to build router with only swagger routes
func newSwaggerRouterOnly() *mux.Router {
	r := mux.NewRouter()
	setupSwaggerUI(r)
	return r
}

func TestDocsRedirectAddsSecurityHeaders(t *testing.T) {
	r := newSwaggerRouterOnly()
	req := httptest.NewRequest(http.MethodGet, "/docs/broker", nil)
	w := httptest.NewRecorder()

	r.ServeHTTP(w, req)

	// Expect a 301 redirect to trailing slash
	assert.Equal(t, http.StatusMovedPermanently, w.Code)
	assert.Equal(t, "/docs/broker/", w.Header().Get("Location"))

	// Security headers should be present even on redirect
	assertSecurityHeaders(t, w.<PERSON>())
}

func TestSwaggerUIHandlerServesUnderDocsPath(t *testing.T) {
	r := newSwaggerRouterOnly()
	req := httptest.NewRequest(http.MethodGet, "/docs/broker/", nil)
	w := httptest.NewRecorder()

	r.ServeHTTP(w, req)

	// Handler comes from swaggo; verify not 404 and has security headers
	assert.NotEqual(t, http.StatusNotFound, w.Code)
	assertSecurityHeaders(t, w.Header())
}

func TestOpenAPISpecServedWithSecurityHeaders(t *testing.T) {
	r := newSwaggerRouterOnly()
	ensureWorkingDirHasOpenAPISpec(t)

	// Test backward compatibility endpoint (now serves environment-specific file)
	req := httptest.NewRequest(http.MethodGet, "/openapi/broker.yaml", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	// In test environment, this may return 404 since we don't have environment-specific files
	// In real deployment, the Dockerfile generates these files
	assert.Contains(t, []int{http.StatusOK, http.StatusNotFound}, w.Code)
	assertSecurityHeaders(t, w.Header())

	if w.Code == http.StatusOK {
		assert.NotEmpty(t, w.Body.Bytes())
	}
}

func TestEnvironmentSpecificOpenAPISpecs(t *testing.T) {
	tests := []struct {
		name        string
		environment string
		requestPath string
		shouldAllow bool
		description string
	}{
		{
			name:        "dev_environment_can_access_dev_spec",
			environment: "dev",
			requestPath: "/openapi/broker-dev.yaml",
			shouldAllow: true,
			description: "Dev environment should access its own spec",
		},
		{
			name:        "dev_environment_can_access_qa_spec",
			environment: "dev",
			requestPath: "/openapi/broker-qa.yaml",
			shouldAllow: true,
			description: "Dev environment should access QA spec for testing",
		},
		{
			name:        "dev_environment_can_access_sandbox_spec",
			environment: "dev",
			requestPath: "/openapi/broker-sandbox.yaml",
			shouldAllow: true,
			description: "Dev environment should access sandbox spec for testing",
		},
		{
			name:        "dev_environment_cannot_access_prod_spec",
			environment: "dev",
			requestPath: "/openapi/broker.yaml",
			shouldAllow: true, // Dev can access broker.yaml (backward compatibility)
			description: "Dev environment can access broker.yaml for backward compatibility",
		},
		{
			name:        "qa_environment_can_access_qa_spec",
			environment: "qa",
			requestPath: "/openapi/broker-qa.yaml",
			shouldAllow: true,
			description: "QA environment should access its own spec",
		},
		{
			name:        "qa_environment_can_access_legacy_broker_spec",
			environment: "qa",
			requestPath: "/openapi/broker.yaml",
			shouldAllow: true, // QA can access broker.yaml (backward compatibility)
			description: "QA environment can access broker.yaml for backward compatibility",
		},
		{
			name:        "prod_environment_can_access_prod_spec",
			environment: "prod",
			requestPath: "/openapi/broker.yaml",
			shouldAllow: true,
			description: "Production environment should access its own spec (broker.yaml)",
		},
		{
			name:        "prod_environment_cannot_access_dev_spec",
			environment: "prod",
			requestPath: "/openapi/broker-dev.yaml",
			shouldAllow: false,
			description: "Production environment should NOT access development spec",
		},
		{
			name:        "sandbox_environment_can_access_sandbox_spec",
			environment: "sandbox",
			requestPath: "/openapi/broker-sandbox.yaml",
			shouldAllow: true,
			description: "Sandbox environment should access its own spec",
		},
		{
			name:        "sandbox_environment_can_access_legacy_broker_spec",
			environment: "sandbox",
			requestPath: "/openapi/broker.yaml",
			shouldAllow: true,
			description: "Sandbox environment can access broker.yaml for backward compatibility",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Setenv("ENVIRONMENT", tt.environment)

			r := newSwaggerRouterOnly()
			req := httptest.NewRequest(http.MethodGet, tt.requestPath, nil)
			w := httptest.NewRecorder()

			r.ServeHTTP(w, req)

			if tt.shouldAllow {
				// Should return 200 (if file exists) or 404 (if file doesn't exist in test)
				assert.Contains(t, []int{http.StatusOK, http.StatusNotFound}, w.Code, tt.description)
				// Security headers should be present for allowed routes
				assertSecurityHeaders(t, w.Header())
			} else {
				// Should return 404 (route not registered) - no security headers expected
				assert.Equal(t, http.StatusNotFound, w.Code, tt.description)
			}
		})
	}
}

func TestGetEnvironmentFunction(t *testing.T) {
	tests := []struct {
		name     string
		envValue string
		expected string
	}{
		{
			name:     "dev environment",
			envValue: "dev",
			expected: "dev",
		},
		{
			name:     "qa environment",
			envValue: "qa",
			expected: "qa",
		},
		{
			name:     "prod environment",
			envValue: "prod",
			expected: "prod",
		},
		{
			name:     "sandbox environment",
			envValue: "sandbox",
			expected: "sandbox",
		},
		{
			name:     "empty environment defaults to prod",
			envValue: "",
			expected: "prod",
		},
		{
			name:     "invalid environment defaults to prod",
			envValue: "invalid",
			expected: "prod",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Setenv("ENVIRONMENT", tt.envValue)
			result := getEnvironment()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIsLocalDevelopmentFunction(t *testing.T) {
	tests := []struct {
		name          string
		swaggerScheme string
		expected      bool
	}{
		{
			name:          "HTTP scheme indicates local development",
			swaggerScheme: "HTTP",
			expected:      true,
		},
		{
			name:          "http scheme (lowercase) indicates local development",
			swaggerScheme: "http",
			expected:      true,
		},
		{
			name:          "HTTPS scheme indicates cloud development",
			swaggerScheme: "HTTPS",
			expected:      false,
		},
		{
			name:          "https scheme (lowercase) indicates cloud development",
			swaggerScheme: "https",
			expected:      false,
		},
		{
			name:          "empty scheme indicates cloud development",
			swaggerScheme: "",
			expected:      false,
		},
		{
			name:          "invalid scheme indicates cloud development",
			swaggerScheme: "invalid",
			expected:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Setenv("SWAGGER_SCHEME", tt.swaggerScheme)
			result := isLocalDevelopment()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestLocalDevelopmentSpecServing(t *testing.T) {
	r := newSwaggerRouterOnly()

	// Test local development spec endpoint
	req := httptest.NewRequest(http.MethodGet, "/openapi/broker-dev-local.yaml", nil)
	w := httptest.NewRecorder()

	r.ServeHTTP(w, req)

	// Note: This will return 404 in test environment since we don't have actual files
	// In real deployment, the Dockerfile generates these files
	assert.Contains(t, []int{http.StatusOK, http.StatusNotFound}, w.Code)
	assertSecurityHeaders(t, w.Header())
}

func TestGetEnvConfig(t *testing.T) {
	tests := []struct {
		name          string
		environment   string
		swaggerScheme string
		expectedURL   string
		expectedFiles int
	}{
		{
			name:          "dev environment with local development",
			environment:   "dev",
			swaggerScheme: "HTTP",
			expectedURL:   "/openapi/broker-dev-local.yaml",
			expectedFiles: 4, // dev, dev-local, qa, sandbox
		},
		{
			name:          "dev environment with cloud development",
			environment:   "dev",
			swaggerScheme: "HTTPS",
			expectedURL:   "/openapi/broker-dev.yaml",
			expectedFiles: 4, // dev, dev-local, qa, sandbox
		},
		{
			name:          "qa environment",
			environment:   "qa",
			swaggerScheme: "HTTPS",
			expectedURL:   "/openapi/broker-qa.yaml",
			expectedFiles: 1, // qa only
		},
		{
			name:          "sandbox environment",
			environment:   "sandbox",
			swaggerScheme: "HTTPS",
			expectedURL:   "/openapi/broker-sandbox.yaml",
			expectedFiles: 1, // sandbox only
		},
		{
			name:          "prod environment",
			environment:   "prod",
			swaggerScheme: "HTTPS",
			expectedURL:   "/openapi/broker.yaml",
			expectedFiles: 1, // broker.yaml only
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Setenv("ENVIRONMENT", tt.environment)
			t.Setenv("SWAGGER_SCHEME", tt.swaggerScheme)

			config := getEnvConfig()

			assert.Equal(t, tt.environment, config.current)
			assert.Equal(t, tt.expectedURL, config.specURL)
			assert.Len(t, config.specFiles, tt.expectedFiles)

			if tt.swaggerScheme == "HTTP" {
				assert.True(t, config.isLocal)
			} else {
				assert.False(t, config.isLocal)
			}
		})
	}
}

func TestBackwardCompatibilityWithLocalDevelopment(t *testing.T) {
	tests := []struct {
		name          string
		environment   string
		swaggerScheme string
		expectedFile  string
	}{
		{
			name:          "local development serves local file",
			environment:   "dev",
			swaggerScheme: "HTTP",
			expectedFile:  "broker-dev-local.yaml",
		},
		{
			name:          "cloud development serves regular dev file",
			environment:   "dev",
			swaggerScheme: "HTTPS",
			expectedFile:  "broker-dev.yaml",
		},
		{
			name:          "production serves prod file regardless of scheme",
			environment:   "prod",
			swaggerScheme: "HTTP",
			expectedFile:  "broker.yaml",
		},
		{
			name:          "sandbox serves sandbox file",
			environment:   "sandbox",
			swaggerScheme: "HTTPS",
			expectedFile:  "broker-sandbox.yaml",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Setenv("ENVIRONMENT", tt.environment)
			t.Setenv("SWAGGER_SCHEME", tt.swaggerScheme)

			r := newSwaggerRouterOnly()
			req := httptest.NewRequest(http.MethodGet, "/openapi/broker.yaml", nil)
			w := httptest.NewRecorder()

			r.ServeHTTP(w, req)

			// Note: This will return 404 in test environment since we don't have actual files
			// In real deployment, the Dockerfile generates these files
			assert.Contains(t, []int{http.StatusOK, http.StatusNotFound}, w.Code)
			assertSecurityHeaders(t, w.Header())

			// The test validates the logic path; actual file serving is tested in integration
		})
	}
}

func assertSecurityHeaders(t *testing.T, h http.Header) {
	t.Helper()
	checks := map[string]string{
		"X-Content-Type-Options": "nosniff",
		"X-Frame-Options":        "DENY",
		"Referrer-Policy":        "no-referrer",
		"X-Robots-Tag":           "noindex",
	}
	for k, v := range checks {
		assert.Equal(t, v, h.Get(k), "header %s should be %q", k, v)
	}
	assert.NotEmpty(t, h.Get("Content-Security-Policy"), "Content-Security-Policy header should be set")
}

// ensureWorkingDirHasOpenAPISpec attempts to change the working directory so that
// the relative path ./openapi/broker.yaml resolves (as used by http.ServeFile).
func ensureWorkingDirHasOpenAPISpec(t *testing.T) {
	t.Helper()
	const specRel = "openapi/broker.yaml"
	// Check current directory and a few parents for the spec file
	wd, _ := os.Getwd()
	orig := wd
	for i := 0; i < 5; i++ {
		candidate := filepath.Join(wd, specRel)
		if _, err := os.Stat(candidate); err == nil {
			// If we had to walk up, chdir to wd so relative path works
			if wd != orig {
				require.NoError(t, os.Chdir(wd), "failed to chdir to %s", wd)
				t.Cleanup(func() { _ = os.Chdir(orig) })
			}
			return
		}
		parent := filepath.Dir(wd)
		if parent == wd {
			break
		}
		wd = parent
	}
	// Fallback: move three levels up from routes package to repo root
	repoRoot := filepath.Clean(filepath.Join("..", "..", ".."))
	require.NoError(t, os.Chdir(repoRoot), "failed to chdir to repo root")
	_, err := os.Stat(specRel)
	require.NoError(t, err, "could not locate %s after chdir", specRel)
	t.Cleanup(func() { _ = os.Chdir(orig) })
}
