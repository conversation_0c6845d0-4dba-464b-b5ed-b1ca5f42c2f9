package routes

import (
	"fmt"
	"net/http"
	"os"
	"strings"

	"github.com/gorilla/mux"
	httpSwagger "github.com/swaggo/http-swagger/v2"
)

// docsSecurity is a middleware that applies basic security headers to the docs endpoint.
func docsSecurity(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Basic hardening
		w.Header().Set("X-Content-Type-Options", "nosniff")
		w.Header().Set("X-Frame-Options", "DENY")
		w.Header().Set("Referrer-Policy", "no-referrer")
		w.Header().Set("X-Robots-Tag", "noindex")

		// CSP for Swagger UI:
		// - allow inline scripts (required by swagger-ui's index)
		// - allow inline styles (swagger-ui uses them)
		// - allow fetching the spec from same origin
		// - block framing
		w.<PERSON><PERSON>().Set("Content-Security-Policy",
			"default-src 'self'; "+
				"img-src 'self' data:; "+
				"style-src 'self' 'unsafe-inline'; "+
				"script-src 'self' 'unsafe-inline'; "+
				"connect-src 'self'; "+
				"frame-ancestors 'none';")

		next.ServeHTTP(w, r)
	})
}

const (
	envDev     = "dev"
	envQA      = "qa"
	envProd    = "prod"
	envSandbox = "sandbox"
)

// envConfig holds environment-specific configuration
type envConfig struct {
	current   string
	isLocal   bool
	specURL   string
	specFiles map[string]string // route -> file path
}

// createFileHandler creates a handler that serves a static file with security headers
func createFileHandler(filePath string) http.Handler {
	return docsSecurity(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, filePath)
	}))
}

// getEnvConfig returns the environment configuration
func getEnvConfig() *envConfig {
	env := getEnvironment()
	isLocal := isLocalDevelopment()
	
	config := &envConfig{
		current: env,
		isLocal: isLocal,
		specFiles: make(map[string]string),
	}
	
	// Determine the primary spec URL for Swagger UI
	if env == envDev && isLocal {
		config.specURL = "/openapi/broker-dev-local.yaml"
	} else if env == envProd {
		config.specURL = "/openapi/broker.yaml"
	} else {
		config.specURL = fmt.Sprintf("/openapi/broker-%s.yaml", env)
	}
	
	// Configure available spec files based on environment
	switch env {
	case envDev:
		config.specFiles["/openapi/broker-dev.yaml"] = "./openapi/broker-dev.yaml"
		config.specFiles["/openapi/broker-dev-local.yaml"] = "./openapi/broker-dev-local.yaml"
		config.specFiles["/openapi/broker-qa.yaml"] = "./openapi/broker-qa.yaml"
		config.specFiles["/openapi/broker-sandbox.yaml"] = "./openapi/broker-sandbox.yaml"
	case envQA:
		config.specFiles["/openapi/broker-qa.yaml"] = "./openapi/broker-qa.yaml"
	case envSandbox:
		config.specFiles["/openapi/broker-sandbox.yaml"] = "./openapi/broker-sandbox.yaml"
	case envProd:
		config.specFiles["/openapi/broker.yaml"] = "./openapi/broker.yaml"
	}
	
	return config
}

func setupSwaggerUI(router *mux.Router) {
	// Redirect /docs/broker -> /docs/broker/
	router.Handle("/docs/broker", docsSecurity(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		http.Redirect(w, r, "/docs/broker/", http.StatusMovedPermanently)
	})))

	config := getEnvConfig()

	// Serve Swagger UI via library; it fetches the environment-specific spec
	uiHandler := httpSwagger.Handler(
		httpSwagger.URL(config.specURL),
		httpSwagger.DomID("swagger-ui"),
	)
	router.PathPrefix("/docs/broker/").Handler(docsSecurity(uiHandler))

	// Register handlers for all allowed spec files
	for route, filePath := range config.specFiles {
		router.Handle(route, createFileHandler(filePath))
	}

	// Keep backward compatibility - serve the current environment spec at the legacy path
	// Note: In production, this route is the same as the main route
	if config.current != envProd {
		var legacyFile string
		if config.current == envDev && config.isLocal {
			legacyFile = "./openapi/broker-dev-local.yaml"
		} else {
			legacyFile = fmt.Sprintf("./openapi/broker-%s.yaml", config.current)
		}
		router.Handle("/openapi/broker.yaml", createFileHandler(legacyFile))
	}
}

// getEnvironment returns the current environment, defaulting to "prod"
func getEnvironment() string {
	env := os.Getenv("ENVIRONMENT")
	if env == "" {
		return envProd // Default to production for safety
	}

	// Validate environment
	validEnvs := map[string]bool{
		envDev:     true,
		envQA:      true,
		envProd:    true,
		envSandbox: true,
	}

	if validEnvs[env] {
		return env
	}

	// If invalid environment, default to prod
	return envProd
}

// isLocalDevelopment returns true if running in local development mode (HTTP scheme)
func isLocalDevelopment() bool {
	return strings.ToUpper(os.Getenv("SWAGGER_SCHEME")) == "HTTP"
}
