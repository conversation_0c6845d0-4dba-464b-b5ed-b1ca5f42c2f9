package brokerShared

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/google/uuid"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
)

// ParseInt64OrUUID parses the json.RawMessage to determine if it's an int64 (OrigID) or UUID string
// Returns (origID, uuid, error) where exactly one of origID or uuid will be non-zero/non-empty
func ParseInt64OrUUID(IDRaw json.RawMessage) (int64, string, error) {
	// If the input is empty or nil, return an error
	if len(IDRaw) == 0 || IDRaw == nil {
		return 0, "", fmt.Errorf("ID is required")
	}

	// Try to parse as string first to check if it's a UUID
	var uuidStr string
	if err := json.Unmarshal(IDRaw, &uuidStr); err == nil {
		// Use the google/uuid library to validate UUID format
		if parsedUUID, err := uuid.Parse(uuidStr); err == nil {
			return 0, parsedUUID.String(), nil
		}
		// If it's a string but not a valid UUID, try parsing as int64
		if origID, err := strconv.ParseInt(uuidStr, 10, 64); err == nil {
			return origID, "", nil
		}
		return 0, "", fmt.Errorf("ID string is neither a valid UUID nor a valid integer")
	}

	// If it's not a string, try to parse as int64
	var origID int64
	if err := json.Unmarshal(IDRaw, &origID); err == nil {
		return origID, "", nil
	}

	return 0, "", fmt.Errorf("ID must be either an integer (OrigID) or a UUID string")
}

// ParseJsonToUUID parses a json.RawMessage and checks if it can be parsed as a valid UUID string.
func ParseJsonToUUID(IDRaw json.RawMessage) (uuid.UUID, error) {
	// Check for empty or nil input
	if len(IDRaw) == 0 || IDRaw == nil {
		return uuid.Nil, fmt.Errorf("ID is required")
	}

	// Attempt to unmarshal as a string
	var str string
	if err := json.Unmarshal(IDRaw, &str); err != nil {
		return uuid.Nil, fmt.Errorf("ID must be a UUID string")
	}

	// Attempt to parse the string as a UUID
	parsedUUID, err := uuid.Parse(str)
	if err != nil {
		return uuid.Nil, fmt.Errorf("ID is not a valid UUID: %v", err)
	}

	return parsedUUID, nil
}

func ValidateDeviceAccess(pg connect.DatabaseExecutor, userPermissions *authorizer.UserPermissions, deviceOrigID int64, deviceUUID string, requiredPermissions ...string) (string, error) {
	// Check if userPermissions is nil
	if userPermissions == nil {
		return "", fmt.Errorf("user permissions cannot be nil")
	}

	// Check device access permissions based on which ID type was provided
	var canAccess bool
	var err error

	if deviceUUID != "" {
		// Using UUID - preferred method
		canAccess, err = userPermissions.CanAccessDevice(pg, deviceUUID, requiredPermissions...)
		if err != nil {
			return "", fmt.Errorf("failed to check device access by UUID: %w", err)
		}
	} else {
		// Using OrigID - deprecated method
		deviceUUID, err = userPermissions.CanAccessDeviceByOrigID(pg, deviceOrigID, requiredPermissions...)
		if err != nil {
			return "", fmt.Errorf("failed to check device access by OrigID: %w", err)
		}
		canAccess = deviceUUID != ""
	}

	if !canAccess {
		if deviceUUID != "" {
			return "", fmt.Errorf("user does not have permission to manage device %s", deviceUUID)
		} else {
			return "", fmt.Errorf("user does not have permission to manage device %d", deviceOrigID)
		}
	}

	return deviceUUID, nil
}
