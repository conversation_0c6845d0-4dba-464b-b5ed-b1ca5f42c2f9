"module name","license","repository"
"@ampproject/remapping@2.3.0","Apache-2.0","https://github.com/ampproject/remapping"
"@angular-devkit/architect@0.1902.13","MIT","https://github.com/angular/angular-cli"
"@angular-devkit/build-angular@19.2.13","MIT","https://github.com/angular/angular-cli"
"@angular-devkit/build-webpack@0.1902.13","MIT","https://github.com/angular/angular-cli"
"@angular-devkit/core@19.2.13","MIT","https://github.com/angular/angular-cli"
"@angular-devkit/schematics@19.2.13","MIT","https://github.com/angular/angular-cli"
"@angular/animations@19.2.13","MIT","https://github.com/angular/angular"
"@angular/build@19.2.13","MIT","https://github.com/angular/angular-cli"
"@angular/cdk@19.2.17","MIT","https://github.com/angular/components"
"@angular/cli@19.2.13","MIT","https://github.com/angular/angular-cli"
"@angular/common@19.2.13","MIT","https://github.com/angular/angular"
"@angular/compiler-cli@19.2.13","MIT","https://github.com/angular/angular"
"@angular/compiler@19.2.13","MIT","https://github.com/angular/angular"
"@angular/core@19.2.13","MIT","https://github.com/angular/angular"
"@angular/forms@19.2.13","MIT","https://github.com/angular/angular"
"@angular/platform-browser-dynamic@19.2.13","MIT","https://github.com/angular/angular"
"@angular/platform-browser@19.2.13","MIT","https://github.com/angular/angular"
"@angular/router@19.2.13","MIT","https://github.com/angular/angular"
"@ant-design/colors@7.2.1","MIT","https://github.com/ant-design/ant-design-colors"
"@ant-design/fast-color@2.0.6","MIT","https://github.com/ant-design/fast-color"
"@ant-design/icons-angular@19.0.0","MIT","https://github.com/ant-design/ant-design-icons/tree/master/packages/icons-angular"
"@babel/code-frame@7.27.1","MIT","https://github.com/babel/babel"
"@babel/compat-data@7.27.2","MIT","https://github.com/babel/babel"
"@babel/core@7.26.10","MIT","https://github.com/babel/babel"
"@babel/core@7.26.9","MIT","https://github.com/babel/babel"
"@babel/generator@7.26.10","MIT","https://github.com/babel/babel"
"@babel/generator@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-annotate-as-pure@7.25.9","MIT","https://github.com/babel/babel"
"@babel/helper-annotate-as-pure@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-compilation-targets@7.27.2","MIT","https://github.com/babel/babel"
"@babel/helper-create-class-features-plugin@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-create-regexp-features-plugin@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-define-polyfill-provider@0.6.4","MIT","https://github.com/babel/babel-polyfills"
"@babel/helper-member-expression-to-functions@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-module-imports@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-module-transforms@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-optimise-call-expression@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-plugin-utils@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-remap-async-to-generator@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-replace-supers@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-skip-transparent-expression-wrappers@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-split-export-declaration@7.24.7","MIT","https://github.com/babel/babel"
"@babel/helper-string-parser@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-validator-identifier@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-validator-option@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-wrap-function@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helpers@7.27.1","MIT","https://github.com/babel/babel"
"@babel/parser@7.27.2","MIT","https://github.com/babel/babel"
"@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2","MIT","https://github.com/babel/babel-plugin-proposal-private-property-in-object"
"@babel/plugin-syntax-import-assertions@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-import-attributes@7.26.0","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-unicode-sets-regex@7.18.6","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-arrow-functions@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-async-generator-functions@7.26.8","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-async-to-generator@7.25.9","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-block-scoped-functions@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-block-scoping@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-class-properties@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-class-static-block@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-classes@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-computed-properties@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-destructuring@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-dotall-regex@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-duplicate-keys@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-dynamic-import@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-exponentiation-operator@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-export-namespace-from@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-for-of@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-function-name@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-json-strings@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-literals@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-logical-assignment-operators@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-member-expression-literals@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-modules-amd@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-modules-commonjs@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-modules-systemjs@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-modules-umd@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-named-capturing-groups-regex@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-new-target@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-nullish-coalescing-operator@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-numeric-separator@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-object-rest-spread@7.27.2","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-object-super@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-optional-catch-binding@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-optional-chaining@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-parameters@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-private-methods@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-private-property-in-object@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-property-literals@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-regenerator@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-regexp-modifiers@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-reserved-words@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-runtime@7.26.10","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-shorthand-properties@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-spread@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-sticky-regex@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-template-literals@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-typeof-symbol@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-unicode-escapes@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-unicode-property-regex@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-unicode-regex@7.27.1","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-unicode-sets-regex@7.27.1","MIT","https://github.com/babel/babel"
"@babel/preset-env@7.26.9","MIT","https://github.com/babel/babel"
"@babel/preset-modules@0.1.6-no-external-plugins","MIT","https://github.com/babel/preset-modules"
"@babel/runtime@7.26.10","MIT","https://github.com/babel/babel"
"@babel/template@7.27.2","MIT","https://github.com/babel/babel"
"@babel/traverse@7.27.1","MIT","https://github.com/babel/babel"
"@babel/types@7.27.1","MIT","https://github.com/babel/babel"
"@colors/colors@1.5.0","MIT","https://github.com/DABH/colors.js"
"@ctrl/tinycolor@3.6.1","MIT","https://github.com/scttcper/tinycolor"
"@discoveryjs/json-ext@0.6.3","MIT","https://github.com/discoveryjs/json-ext"
"@esbuild/linux-x64@0.25.4","MIT","https://github.com/evanw/esbuild"
"@inquirer/checkbox@4.1.8","MIT","https://github.com/SBoudrias/Inquirer.js"
"@inquirer/confirm@5.1.6","MIT","https://github.com/SBoudrias/Inquirer.js"
"@inquirer/core@10.1.13","MIT","https://github.com/SBoudrias/Inquirer.js"
"@inquirer/editor@4.2.13","MIT","https://github.com/SBoudrias/Inquirer.js"
"@inquirer/expand@4.0.15","MIT","https://github.com/SBoudrias/Inquirer.js"
"@inquirer/figures@1.0.12","MIT","https://github.com/SBoudrias/Inquirer.js"
"@inquirer/input@4.1.12","MIT","https://github.com/SBoudrias/Inquirer.js"
"@inquirer/number@3.0.15","MIT","https://github.com/SBoudrias/Inquirer.js"
"@inquirer/password@4.0.15","MIT","https://github.com/SBoudrias/Inquirer.js"
"@inquirer/prompts@7.3.2","MIT","https://github.com/SBoudrias/Inquirer.js"
"@inquirer/rawlist@4.1.3","MIT","https://github.com/SBoudrias/Inquirer.js"
"@inquirer/search@3.0.15","MIT","https://github.com/SBoudrias/Inquirer.js"
"@inquirer/select@4.2.3","MIT","https://github.com/SBoudrias/Inquirer.js"
"@inquirer/type@1.5.5","MIT","https://github.com/SBoudrias/Inquirer.js"
"@inquirer/type@3.0.7","MIT","https://github.com/SBoudrias/Inquirer.js"
"@isaacs/cliui@8.0.2","ISC","https://github.com/yargs/cliui"
"@isaacs/fs-minipass@4.0.1","ISC","https://github.com/npm/fs-minipass"
"@istanbuljs/schema@0.1.3","MIT","https://github.com/istanbuljs/schema"
"@jridgewell/gen-mapping@0.3.8","MIT","https://github.com/jridgewell/gen-mapping"
"@jridgewell/resolve-uri@3.1.2","MIT","https://github.com/jridgewell/resolve-uri"
"@jridgewell/set-array@1.2.1","MIT","https://github.com/jridgewell/set-array"
"@jridgewell/source-map@0.3.6","MIT","https://github.com/jridgewell/source-map"
"@jridgewell/sourcemap-codec@1.5.0","MIT","https://github.com/jridgewell/sourcemap-codec"
"@jridgewell/trace-mapping@0.3.25","MIT","https://github.com/jridgewell/trace-mapping"
"@jsonjoy.com/base64@1.1.2","Apache-2.0","https://github.com/jsonjoy-com/base64"
"@jsonjoy.com/json-pack@1.2.0","Apache-2.0","https://github.com/jsonjoy-com/json-pack"
"@jsonjoy.com/util@1.6.0","Apache-2.0","https://github.com/jsonjoy-com/util"
"@leichtgewicht/ip-codec@2.0.5","MIT","https://github.com/martinheidegger/ip-codec"
"@listr2/prompt-adapter-inquirer@2.0.18","MIT","https://github.com/listr2/listr2"
"@lmdb/lmdb-linux-x64@3.2.6","MIT","https://github.com/kriszyp/lmdb-js"
"@msgpackr-extract/msgpackr-extract-linux-x64@3.0.3","MIT","https://github.com/kriszyp/msgpackr-extract"
"@napi-rs/nice-linux-x64-gnu@1.0.1","MIT","https://github.com/Brooooooklyn/nice"
"@napi-rs/nice-linux-x64-musl@1.0.1","MIT","https://github.com/Brooooooklyn/nice"
"@napi-rs/nice@1.0.1","MIT","https://github.com/Brooooooklyn/nice"
"@ngtools/webpack@19.2.13","MIT","https://github.com/angular/angular-cli"
"@nodelib/fs.scandir@2.1.5","MIT","https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.scandir"
"@nodelib/fs.stat@2.0.5","MIT","https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.stat"
"@nodelib/fs.walk@1.2.8","MIT","https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.walk"
"@npmcli/agent@3.0.0","ISC","https://github.com/npm/agent"
"@npmcli/fs@4.0.0","ISC","https://github.com/npm/fs"
"@npmcli/git@6.0.3","ISC","https://github.com/npm/git"
"@npmcli/installed-package-contents@3.0.0","ISC","https://github.com/npm/installed-package-contents"
"@npmcli/node-gyp@4.0.0","ISC","https://github.com/npm/node-gyp"
"@npmcli/package-json@6.2.0","ISC","https://github.com/npm/package-json"
"@npmcli/promise-spawn@8.0.2","ISC","https://github.com/npm/promise-spawn"
"@npmcli/redact@3.2.2","ISC","https://github.com/npm/redact"
"@npmcli/run-script@9.1.0","ISC","https://github.com/npm/run-script"
"@parcel/watcher-linux-x64-glibc@2.5.1","MIT","https://github.com/parcel-bundler/watcher"
"@parcel/watcher-linux-x64-musl@2.5.1","MIT","https://github.com/parcel-bundler/watcher"
"@parcel/watcher@2.5.1","MIT","https://github.com/parcel-bundler/watcher"
"@pkgjs/parseargs@0.11.0","MIT","https://github.com/pkgjs/parseargs"
"@rollup/rollup-linux-x64-gnu@4.34.8","MIT","https://github.com/rollup/rollup"
"@rollup/rollup-linux-x64-gnu@4.41.1","MIT","https://github.com/rollup/rollup"
"@rollup/rollup-linux-x64-musl@4.34.8","MIT","https://github.com/rollup/rollup"
"@rollup/rollup-linux-x64-musl@4.41.1","MIT","https://github.com/rollup/rollup"
"@schematics/angular@19.2.13","MIT","https://github.com/angular/angular-cli"
"@sigstore/bundle@3.1.0","Apache-2.0","https://github.com/sigstore/sigstore-js"
"@sigstore/core@2.0.0","Apache-2.0","https://github.com/sigstore/sigstore-js"
"@sigstore/protobuf-specs@0.4.2","Apache-2.0","https://github.com/sigstore/protobuf-specs"
"@sigstore/sign@3.1.0","Apache-2.0","https://github.com/sigstore/sigstore-js"
"@sigstore/tuf@3.1.1","Apache-2.0","https://github.com/sigstore/sigstore-js"
"@sigstore/verify@2.1.1","Apache-2.0","https://github.com/sigstore/sigstore-js"
"@sindresorhus/merge-streams@2.3.0","MIT","https://github.com/sindresorhus/merge-streams"
"@socket.io/component-emitter@3.1.2","MIT","https://github.com/socketio/emitter"
"@tufjs/canonical-json@2.0.0","MIT","https://github.com/theupdateframework/tuf-js"
"@tufjs/models@3.0.1","MIT","https://github.com/theupdateframework/tuf-js"
"@types/body-parser@1.19.5","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/bonjour@3.5.13","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/connect-history-api-fallback@1.5.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/connect@3.4.38","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/cors@2.8.18","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/eslint-scope@3.7.7","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/eslint@9.6.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/estree@1.0.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/estree@1.0.7","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/express-serve-static-core@4.19.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/express-serve-static-core@5.0.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/express@4.17.22","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/http-errors@2.0.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/http-proxy@1.17.16","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/jasmine@5.1.8","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/json-schema@7.0.15","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/mime@1.3.5","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/node-forge@1.3.11","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/node@22.15.21","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/papaparse@5.3.16","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/qs@6.14.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/range-parser@1.2.7","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/retry@0.12.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/send@0.17.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/serve-index@1.9.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/serve-static@1.15.7","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/sockjs@0.3.36","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/ws@8.18.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@vitejs/plugin-basic-ssl@1.2.0","MIT","https://github.com/vitejs/vite-plugin-basic-ssl"
"@webassemblyjs/ast@1.14.1","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/floating-point-hex-parser@1.13.2","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-api-error@1.13.2","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-buffer@1.14.1","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-numbers@1.13.2","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-wasm-bytecode@1.13.2","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-wasm-section@1.14.1","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/ieee754@1.13.2","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/leb128@1.13.2","Apache-2.0","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/utf8@1.13.2","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wasm-edit@1.14.1","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wasm-gen@1.14.1","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wasm-opt@1.14.1","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wasm-parser@1.14.1","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wast-printer@1.14.1","MIT","https://github.com/xtuc/webassemblyjs"
"@xtuc/ieee754@1.2.0","BSD-3-Clause","https://github.com/feross/ieee754"
"@xtuc/long@4.2.2","Apache-2.0","https://github.com/dcodeIO/long.js"
"@yarnpkg/lockfile@1.1.0","BSD-2-Clause","https://github.com/yarnpkg/yarn/blob/master/packages/lockfile"
"abbrev@3.0.1","ISC","https://github.com/npm/abbrev-js"
"accepts@1.3.8","MIT","https://github.com/jshttp/accepts"
"acorn@8.14.1","MIT","https://github.com/acornjs/acorn"
"adjust-sourcemap-loader@4.0.0","MIT","https://github.com/bholloway/adjust-sourcemap-loader"
"agent-base@7.1.3","MIT","https://github.com/TooTallNate/proxy-agents"
"ajv-formats@2.1.1","MIT","https://github.com/ajv-validator/ajv-formats"
"ajv-formats@3.0.1","MIT","https://github.com/ajv-validator/ajv-formats"
"ajv-keywords@5.1.0","MIT","https://github.com/epoberezkin/ajv-keywords"
"ajv@8.17.1","MIT","https://github.com/ajv-validator/ajv"
"ansi-colors@4.1.3","MIT","https://github.com/doowb/ansi-colors"
"ansi-escapes@4.3.2","MIT","https://github.com/sindresorhus/ansi-escapes"
"ansi-escapes@7.0.0","MIT","https://github.com/sindresorhus/ansi-escapes"
"ansi-html-community@0.0.8","Apache-2.0","https://github.com/mahdyar/ansi-html-community"
"ansi-regex@5.0.1","MIT","https://github.com/chalk/ansi-regex"
"ansi-regex@6.1.0","MIT","https://github.com/chalk/ansi-regex"
"ansi-styles@4.3.0","MIT","https://github.com/chalk/ansi-styles"
"ansi-styles@6.2.1","MIT","https://github.com/chalk/ansi-styles"
"anymatch@3.1.3","ISC","https://github.com/micromatch/anymatch"
"argparse@2.0.1","Python-2.0","https://github.com/nodeca/argparse"
"array-flatten@1.1.1","MIT","https://github.com/blakeembrey/array-flatten"
"autoprefixer@10.4.20","MIT","https://github.com/postcss/autoprefixer"
"babel-loader@9.2.1","MIT","https://github.com/babel/babel-loader"
"babel-plugin-polyfill-corejs2@0.4.13","MIT","https://github.com/babel/babel-polyfills"
"babel-plugin-polyfill-corejs3@0.11.1","MIT","https://github.com/babel/babel-polyfills"
"babel-plugin-polyfill-regenerator@0.6.4","MIT","https://github.com/babel/babel-polyfills"
"balanced-match@1.0.2","MIT","https://github.com/juliangruber/balanced-match"
"base64-js@1.5.1","MIT","https://github.com/beatgammit/base64-js"
"base64id@2.0.0","MIT","https://github.com/faeldt/base64id"
"batch@0.6.1","MIT","https://github.com/visionmedia/batch"
"beasties@0.3.2","Apache-2.0","https://github.com/danielroe/beasties"
"big.js@5.2.2","MIT","https://github.com/MikeMcl/big.js"
"binary-extensions@2.3.0","MIT","https://github.com/sindresorhus/binary-extensions"
"bl@4.1.0","MIT","https://github.com/rvagg/bl"
"body-parser@1.20.3","MIT","https://github.com/expressjs/body-parser"
"bonjour-service@1.3.0","MIT","https://github.com/onlxltd/bonjour-service"
"boolbase@1.0.0","ISC","https://github.com/fb55/boolbase"
"brace-expansion@1.1.12","MIT","https://github.com/juliangruber/brace-expansion"
"brace-expansion@2.0.2","MIT","https://github.com/juliangruber/brace-expansion"
"braces@3.0.3","MIT","https://github.com/micromatch/braces"
"browserslist@4.24.5","MIT","https://github.com/browserslist/browserslist"
"buffer-from@1.1.2","MIT","https://github.com/LinusU/buffer-from"
"buffer@5.7.1","MIT","https://github.com/feross/buffer"
"bundle-name@4.1.0","MIT","https://github.com/sindresorhus/bundle-name"
"bytes@3.1.2","MIT","https://github.com/visionmedia/bytes.js"
"cacache@19.0.1","ISC","https://github.com/npm/cacache"
"call-bind-apply-helpers@1.0.2","MIT","https://github.com/ljharb/call-bind-apply-helpers"
"call-bound@1.0.4","MIT","https://github.com/ljharb/call-bound"
"callsites@3.1.0","MIT","https://github.com/sindresorhus/callsites"
"caniuse-lite@1.0.30001718","CC-BY-4.0","https://github.com/browserslist/caniuse-lite"
"chalk@4.1.2","MIT","https://github.com/chalk/chalk"
"chardet@0.7.0","MIT","https://github.com/runk/node-chardet"
"chokidar@3.6.0","MIT","https://github.com/paulmillr/chokidar"
"chokidar@4.0.3","MIT","https://github.com/paulmillr/chokidar"
"chownr@2.0.0","ISC","https://github.com/isaacs/chownr"
"chownr@3.0.0","BlueOak-1.0.0","https://github.com/isaacs/chownr"
"chrome-trace-event@1.0.4","MIT","https://github.com/samccone/chrome-trace-event"
"cli-cursor@3.1.0","MIT","https://github.com/sindresorhus/cli-cursor"
"cli-cursor@5.0.0","MIT","https://github.com/sindresorhus/cli-cursor"
"cli-spinners@2.9.2","MIT","https://github.com/sindresorhus/cli-spinners"
"cli-truncate@4.0.0","MIT","https://github.com/sindresorhus/cli-truncate"
"cli-width@4.1.0","ISC","https://github.com/knownasilya/cli-width"
"cliui@7.0.4","ISC","https://github.com/yargs/cliui"
"cliui@8.0.1","ISC","https://github.com/yargs/cliui"
"clone-deep@4.0.1","MIT","https://github.com/jonschlinkert/clone-deep"
"clone@1.0.4","MIT","https://github.com/pvorb/node-clone"
"color-convert@2.0.1","MIT","https://github.com/Qix-/color-convert"
"color-name@1.1.4","MIT","https://github.com/colorjs/color-name"
"colorette@2.0.20","MIT","https://github.com/jorgebucaran/colorette"
"commander@2.20.3","MIT","https://github.com/tj/commander.js"
"common-path-prefix@3.0.0","ISC","https://github.com/novemberborn/common-path-prefix"
"compressible@2.0.18","MIT","https://github.com/jshttp/compressible"
"compression@1.8.0","MIT","https://github.com/expressjs/compression"
"concat-map@0.0.1","MIT","https://github.com/substack/node-concat-map"
"connect-history-api-fallback@2.0.0","MIT","https://github.com/bripkens/connect-history-api-fallback"
"connect@3.7.0","MIT","https://github.com/senchalabs/connect"
"content-disposition@0.5.4","MIT","https://github.com/jshttp/content-disposition"
"content-type@1.0.5","MIT","https://github.com/jshttp/content-type"
"convert-source-map@1.9.0","MIT","https://github.com/thlorenz/convert-source-map"
"convert-source-map@2.0.0","MIT","https://github.com/thlorenz/convert-source-map"
"cookie-signature@1.0.6","MIT","https://github.com/visionmedia/node-cookie-signature"
"cookie@0.7.1","MIT","https://github.com/jshttp/cookie"
"cookie@0.7.2","MIT","https://github.com/jshttp/cookie"
"copy-anything@2.0.6","MIT","https://github.com/mesqueeb/copy-anything"
"copy-webpack-plugin@12.0.2","MIT","https://github.com/webpack-contrib/copy-webpack-plugin"
"core-js-compat@3.42.0","MIT","https://github.com/zloirock/core-js"
"core-util-is@1.0.3","MIT","https://github.com/isaacs/core-util-is"
"cors@2.8.5","MIT","https://github.com/expressjs/cors"
"cosmiconfig@9.0.0","MIT","https://github.com/cosmiconfig/cosmiconfig"
"cross-spawn@7.0.6","MIT","https://github.com/moxystudio/node-cross-spawn"
"css-loader@7.1.2","MIT","https://github.com/webpack-contrib/css-loader"
"css-select@5.1.0","BSD-2-Clause","https://github.com/fb55/css-select"
"css-what@6.1.0","BSD-2-Clause","https://github.com/fb55/css-what"
"cssesc@3.0.0","MIT","https://github.com/mathiasbynens/cssesc"
"custom-event@1.0.1","MIT","https://github.com/webmodules/custom-event"
"date-fns@2.30.0","MIT","https://github.com/date-fns/date-fns"
"date-format@4.0.14","MIT","https://github.com/nomiddlename/date-format"
"debug@2.6.9","MIT","https://github.com/visionmedia/debug"
"debug@4.3.7","MIT","https://github.com/debug-js/debug"
"debug@4.4.1","MIT","https://github.com/debug-js/debug"
"default-browser-id@5.0.0","MIT","https://github.com/sindresorhus/default-browser-id"
"default-browser@5.2.1","MIT","https://github.com/sindresorhus/default-browser"
"defaults@1.0.4","MIT","https://github.com/sindresorhus/node-defaults"
"define-lazy-prop@3.0.0","MIT","https://github.com/sindresorhus/define-lazy-prop"
"depd@1.1.2","MIT","https://github.com/dougwilson/nodejs-depd"
"depd@2.0.0","MIT","https://github.com/dougwilson/nodejs-depd"
"destroy@1.2.0","MIT","https://github.com/stream-utils/destroy"
"detect-libc@1.0.3","Apache-2.0","https://github.com/lovell/detect-libc"
"detect-libc@2.0.4","Apache-2.0","https://github.com/lovell/detect-libc"
"detect-node@2.1.0","MIT","https://github.com/iliakan/detect-node"
"di@0.0.1","MIT","https://github.com/vojtajina/node-di"
"dns-packet@5.6.1","MIT","https://github.com/mafintosh/dns-packet"
"dom-serialize@2.2.1","MIT","https://github.com/webmodules/dom-serialize"
"dom-serializer@2.0.0","MIT","https://github.com/cheeriojs/dom-serializer"
"domelementtype@2.3.0","BSD-2-Clause","https://github.com/fb55/domelementtype"
"domhandler@5.0.3","BSD-2-Clause","https://github.com/fb55/domhandler"
"domutils@3.2.2","BSD-2-Clause","https://github.com/fb55/domutils"
"dunder-proto@1.0.1","MIT","https://github.com/es-shims/dunder-proto"
"eastasianwidth@0.2.0","MIT","https://github.com/komagata/eastasianwidth"
"ee-first@1.1.1","MIT","https://github.com/jonathanong/ee-first"
"electron-to-chromium@1.5.157","ISC","https://github.com/kilian/electron-to-chromium"
"emoji-regex@10.4.0","MIT","https://github.com/mathiasbynens/emoji-regex"
"emoji-regex@8.0.0","MIT","https://github.com/mathiasbynens/emoji-regex"
"emoji-regex@9.2.2","MIT","https://github.com/mathiasbynens/emoji-regex"
"emojis-list@3.0.0","MIT","https://github.com/kikobeats/emojis-list"
"encodeurl@1.0.2","MIT","https://github.com/pillarjs/encodeurl"
"encodeurl@2.0.0","MIT","https://github.com/pillarjs/encodeurl"
"encoding@0.1.13","MIT","https://github.com/andris9/encoding"
"engine.io-parser@5.2.3","MIT","https://github.com/socketio/socket.io"
"engine.io@6.6.4","MIT","https://github.com/socketio/socket.io"
"enhanced-resolve@5.18.1","MIT","https://github.com/webpack/enhanced-resolve"
"ent@2.2.2","MIT","https://github.com/ljharb/ent"
"entities@4.5.0","BSD-2-Clause","https://github.com/fb55/entities"
"entities@6.0.0","BSD-2-Clause","https://github.com/fb55/entities"
"env-paths@2.2.1","MIT","https://github.com/sindresorhus/env-paths"
"environment@1.1.0","MIT","https://github.com/sindresorhus/environment"
"err-code@2.0.3","MIT","https://github.com/IndigoUnited/js-err-code"
"errno@0.1.8","MIT","https://github.com/rvagg/node-errno"
"error-ex@1.3.2","MIT","https://github.com/qix-/node-error-ex"
"es-define-property@1.0.1","MIT","https://github.com/ljharb/es-define-property"
"es-errors@1.3.0","MIT","https://github.com/ljharb/es-errors"
"es-module-lexer@1.7.0","MIT","https://github.com/guybedford/es-module-lexer"
"es-object-atoms@1.1.1","MIT","https://github.com/ljharb/es-object-atoms"
"esbuild-wasm@0.25.4","MIT","https://github.com/evanw/esbuild"
"esbuild@0.25.4","MIT","https://github.com/evanw/esbuild"
"escalade@3.2.0","MIT","https://github.com/lukeed/escalade"
"escape-html@1.0.3","MIT","https://github.com/component/escape-html"
"eslint-scope@5.1.1","BSD-2-Clause","https://github.com/eslint/eslint-scope"
"esrecurse@4.3.0","BSD-2-Clause","https://github.com/estools/esrecurse"
"estraverse@4.3.0","BSD-2-Clause","https://github.com/estools/estraverse"
"estraverse@5.3.0","BSD-2-Clause","https://github.com/estools/estraverse"
"esutils@2.0.3","BSD-2-Clause","https://github.com/estools/esutils"
"etag@1.8.1","MIT","https://github.com/jshttp/etag"
"eventemitter3@4.0.7","MIT","https://github.com/primus/eventemitter3"
"eventemitter3@5.0.1","MIT","https://github.com/primus/eventemitter3"
"events@3.3.0","MIT","https://github.com/Gozala/events"
"exponential-backoff@3.1.2","Apache-2.0","https://github.com/coveooss/exponential-backoff"
"express@4.21.2","MIT","https://github.com/expressjs/express"
"extend@3.0.2","MIT","https://github.com/justmoon/node-extend"
"external-editor@3.1.0","MIT","https://github.com/mrkmg/node-external-editor"
"fast-deep-equal@3.1.3","MIT","https://github.com/epoberezkin/fast-deep-equal"
"fast-glob@3.3.3","MIT","https://github.com/mrmlnc/fast-glob"
"fast-uri@3.0.6","BSD-3-Clause","https://github.com/fastify/fast-uri"
"fastq@1.19.1","ISC","https://github.com/mcollina/fastq"
"faye-websocket@0.11.4","Apache-2.0","https://github.com/faye/faye-websocket-node"
"fdir@6.4.4","MIT","https://github.com/thecodrr/fdir"
"fill-range@7.1.1","MIT","https://github.com/jonschlinkert/fill-range"
"finalhandler@1.1.2","MIT","https://github.com/pillarjs/finalhandler"
"finalhandler@1.3.1","MIT","https://github.com/pillarjs/finalhandler"
"find-cache-dir@4.0.0","MIT","https://github.com/sindresorhus/find-cache-dir"
"find-up@6.3.0","MIT","https://github.com/sindresorhus/find-up"
"flat@5.0.2","BSD-3-Clause","https://github.com/hughsk/flat"
"flatted@3.3.3","ISC","https://github.com/WebReflection/flatted"
"follow-redirects@1.15.9","MIT","https://github.com/follow-redirects/follow-redirects"
"foreground-child@3.3.1","ISC","https://github.com/tapjs/foreground-child"
"forwarded@0.2.0","MIT","https://github.com/jshttp/forwarded"
"fraction.js@4.3.7","MIT","https://github.com/rawify/Fraction.js"
"fresh@0.5.2","MIT","https://github.com/jshttp/fresh"
"fs-extra@8.1.0","MIT","https://github.com/jprichardson/node-fs-extra"
"fs-minipass@2.1.0","ISC","https://github.com/npm/fs-minipass"
"fs-minipass@3.0.3","ISC","https://github.com/npm/fs-minipass"
"fs.realpath@1.0.0","ISC","https://github.com/isaacs/fs.realpath"
"function-bind@1.1.2","MIT","https://github.com/Raynos/function-bind"
"gensync@1.0.0-beta.2","MIT","https://github.com/loganfsmyth/gensync"
"get-caller-file@2.0.5","ISC","https://github.com/stefanpenner/get-caller-file"
"get-east-asian-width@1.3.0","MIT","https://github.com/sindresorhus/get-east-asian-width"
"get-intrinsic@1.3.0","MIT","https://github.com/ljharb/get-intrinsic"
"get-proto@1.0.1","MIT","https://github.com/ljharb/get-proto"
"glob-parent@5.1.2","ISC","https://github.com/gulpjs/glob-parent"
"glob-parent@6.0.2","ISC","https://github.com/gulpjs/glob-parent"
"glob-to-regexp@0.4.1","BSD-2-Clause","https://github.com/fitzgen/glob-to-regexp"
"glob@10.4.5","ISC","https://github.com/isaacs/node-glob"
"glob@7.2.3","ISC","https://github.com/isaacs/node-glob"
"globals@11.12.0","MIT","https://github.com/sindresorhus/globals"
"globby@14.1.0","MIT","https://github.com/sindresorhus/globby"
"gopd@1.2.0","MIT","https://github.com/ljharb/gopd"
"graceful-fs@4.2.11","ISC","https://github.com/isaacs/node-graceful-fs"
"handle-thing@2.0.1","MIT","https://github.com/indutny/handle-thing"
"has-flag@4.0.0","MIT","https://github.com/sindresorhus/has-flag"
"has-symbols@1.1.0","MIT","https://github.com/inspect-js/has-symbols"
"has-tostringtag@1.0.2","MIT","https://github.com/inspect-js/has-tostringtag"
"hasown@2.0.2","MIT","https://github.com/inspect-js/hasOwn"
"hosted-git-info@8.1.0","ISC","https://github.com/npm/hosted-git-info"
"hpack.js@2.1.6","MIT","https://github.com/indutny/hpack.js"
"html-escaper@2.0.2","MIT","https://github.com/WebReflection/html-escaper"
"htmlparser2@10.0.0","MIT","https://github.com/fb55/htmlparser2"
"http-cache-semantics@4.2.0","BSD-2-Clause","https://github.com/kornelski/http-cache-semantics"
"http-deceiver@1.2.7","MIT","https://github.com/indutny/http-deceiver"
"http-errors@1.6.3","MIT","https://github.com/jshttp/http-errors"
"http-errors@2.0.0","MIT","https://github.com/jshttp/http-errors"
"http-parser-js@0.5.10","MIT","https://github.com/creationix/http-parser-js"
"http-proxy-agent@7.0.2","MIT","https://github.com/TooTallNate/proxy-agents"
"http-proxy-middleware@2.0.9","MIT","https://github.com/chimurai/http-proxy-middleware"
"http-proxy-middleware@3.0.5","MIT","https://github.com/chimurai/http-proxy-middleware"
"http-proxy@1.18.1","MIT","https://github.com/http-party/node-http-proxy"
"https-proxy-agent@7.0.6","MIT","https://github.com/TooTallNate/proxy-agents"
"hyperdyperid@1.2.0","MIT","https://github.com/streamich/hyperdyperid"
"iconv-lite@0.4.24","MIT","https://github.com/ashtuchkin/iconv-lite"
"iconv-lite@0.6.3","MIT","https://github.com/ashtuchkin/iconv-lite"
"icss-utils@5.1.0","ISC","https://github.com/css-modules/icss-utils"
"ieee754@1.2.1","BSD-3-Clause","https://github.com/feross/ieee754"
"ignore-walk@7.0.0","ISC","https://github.com/npm/ignore-walk"
"ignore@7.0.4","MIT","https://github.com/kaelzhang/node-ignore"
"image-size@0.5.5","MIT","https://github.com/image-size/image-size"
"immutable@5.1.2","MIT","https://github.com/immutable-js/immutable-js"
"import-fresh@3.3.1","MIT","https://github.com/sindresorhus/import-fresh"
"imurmurhash@0.1.4","MIT","https://github.com/jensyt/imurmurhash-js"
"inflight@1.0.6","ISC","https://github.com/npm/inflight"
"inherits@2.0.3","ISC","https://github.com/isaacs/inherits"
"inherits@2.0.4","ISC","https://github.com/isaacs/inherits"
"ini@5.0.0","ISC","https://github.com/npm/ini"
"ip-address@9.0.5","MIT","https://github.com/beaugunderson/ip-address"
"ipaddr.js@1.9.1","MIT","https://github.com/whitequark/ipaddr.js"
"ipaddr.js@2.2.0","MIT","https://github.com/whitequark/ipaddr.js"
"is-arrayish@0.2.1","MIT","https://github.com/qix-/node-is-arrayish"
"is-binary-path@2.1.0","MIT","https://github.com/sindresorhus/is-binary-path"
"is-core-module@2.16.1","MIT","https://github.com/inspect-js/is-core-module"
"is-docker@3.0.0","MIT","https://github.com/sindresorhus/is-docker"
"is-extglob@2.1.1","MIT","https://github.com/jonschlinkert/is-extglob"
"is-fullwidth-code-point@3.0.0","MIT","https://github.com/sindresorhus/is-fullwidth-code-point"
"is-fullwidth-code-point@4.0.0","MIT","https://github.com/sindresorhus/is-fullwidth-code-point"
"is-fullwidth-code-point@5.0.0","MIT","https://github.com/sindresorhus/is-fullwidth-code-point"
"is-glob@4.0.3","MIT","https://github.com/micromatch/is-glob"
"is-inside-container@1.0.0","MIT","https://github.com/sindresorhus/is-inside-container"
"is-interactive@1.0.0","MIT","https://github.com/sindresorhus/is-interactive"
"is-network-error@1.1.0","MIT","https://github.com/sindresorhus/is-network-error"
"is-number@7.0.0","MIT","https://github.com/jonschlinkert/is-number"
"is-plain-obj@3.0.0","MIT","https://github.com/sindresorhus/is-plain-obj"
"is-plain-object@2.0.4","MIT","https://github.com/jonschlinkert/is-plain-object"
"is-plain-object@5.0.0","MIT","https://github.com/jonschlinkert/is-plain-object"
"is-regex@1.2.1","MIT","https://github.com/inspect-js/is-regex"
"is-unicode-supported@0.1.0","MIT","https://github.com/sindresorhus/is-unicode-supported"
"is-what@3.14.1","MIT","https://github.com/mesqueeb/is-what"
"is-wsl@3.1.0","MIT","https://github.com/sindresorhus/is-wsl"
"isarray@1.0.0","MIT","https://github.com/juliangruber/isarray"
"isbinaryfile@4.0.10","MIT","https://github.com/gjtorikian/isBinaryFile"
"isexe@2.0.0","ISC","https://github.com/isaacs/isexe"
"isexe@3.1.1","ISC","https://github.com/isaacs/isexe"
"isobject@3.0.1","MIT","https://github.com/jonschlinkert/isobject"
"istanbul-lib-coverage@3.2.2","BSD-3-Clause","https://github.com/istanbuljs/istanbuljs"
"istanbul-lib-instrument@5.2.1","BSD-3-Clause","https://github.com/istanbuljs/istanbuljs"
"istanbul-lib-instrument@6.0.3","BSD-3-Clause","https://github.com/istanbuljs/istanbuljs"
"istanbul-lib-report@3.0.1","BSD-3-Clause","https://github.com/istanbuljs/istanbuljs"
"istanbul-lib-source-maps@4.0.1","BSD-3-Clause","https://github.com/istanbuljs/istanbuljs"
"istanbul-reports@3.1.7","BSD-3-Clause","https://github.com/istanbuljs/istanbuljs"
"jackspeak@3.4.3","BlueOak-1.0.0","https://github.com/isaacs/jackspeak"
"jasmine-core@4.6.1","MIT","https://github.com/jasmine/jasmine"
"jasmine-core@5.6.0","MIT","https://github.com/jasmine/jasmine"
"jest-worker@27.5.1","MIT","https://github.com/facebook/jest"
"jiti@1.21.7","MIT","https://github.com/unjs/jiti"
"js-tokens@4.0.0","MIT","https://github.com/lydell/js-tokens"
"js-yaml@4.1.0","MIT","https://github.com/nodeca/js-yaml"
"jsbn@1.1.0","MIT","https://github.com/andyperlitch/jsbn"
"jsesc@3.0.2","MIT","https://github.com/mathiasbynens/jsesc"
"jsesc@3.1.0","MIT","https://github.com/mathiasbynens/jsesc"
"json-parse-even-better-errors@2.3.1","MIT","https://github.com/npm/json-parse-even-better-errors"
"json-parse-even-better-errors@4.0.0","MIT","https://github.com/npm/json-parse-even-better-errors"
"json-schema-traverse@1.0.0","MIT","https://github.com/epoberezkin/json-schema-traverse"
"json5@2.2.3","MIT","https://github.com/json5/json5"
"jsonc-parser@3.3.1","MIT","https://github.com/microsoft/node-jsonc-parser"
"jsonfile@4.0.0","MIT","https://github.com/jprichardson/node-jsonfile"
"jsonparse@1.3.1","MIT","https://github.com/creationix/jsonparse"
"karma-chrome-launcher@3.2.0","MIT","https://github.com/karma-runner/karma-chrome-launcher"
"karma-coverage@2.2.1","MIT","https://github.com/karma-runner/karma-coverage"
"karma-jasmine-html-reporter@2.1.0","MIT","https://github.com/dfederm/karma-jasmine-html-reporter"
"karma-jasmine@5.1.0","MIT","https://github.com/karma-runner/karma-jasmine"
"karma-source-map-support@1.4.0","MIT","https://github.com/tschaub/karma-source-map-support"
"karma@6.4.4","MIT","https://github.com/karma-runner/karma"
"kind-of@6.0.3","MIT","https://github.com/jonschlinkert/kind-of"
"launch-editor@2.10.0","MIT","https://github.com/yyx990803/launch-editor"
"less-loader@12.2.0","MIT","https://github.com/webpack-contrib/less-loader"
"less-loader@12.3.0","MIT","https://github.com/webpack-contrib/less-loader"
"less@4.2.2","Apache-2.0","https://github.com/less/less.js"
"less@4.4.2","Apache-2.0","https://github.com/less/less.js"
"license-webpack-plugin@4.0.2","ISC","https://github.com/xz64/license-webpack-plugin"
"lines-and-columns@1.2.4","MIT","https://github.com/eventualbuddha/lines-and-columns"
"listr2@8.2.5","MIT","https://github.com/listr2/listr2"
"lmdb@3.2.6","MIT","https://github.com/kriszyp/lmdb-js"
"loader-runner@4.3.0","MIT","https://github.com/webpack/loader-runner"
"loader-utils@2.0.4","MIT","https://github.com/webpack/loader-utils"
"loader-utils@3.3.1","MIT","https://github.com/webpack/loader-utils"
"locate-path@7.2.0","MIT","https://github.com/sindresorhus/locate-path"
"lodash.debounce@4.0.8","MIT","https://github.com/lodash/lodash"
"lodash@4.17.21","MIT","https://github.com/lodash/lodash"
"log-symbols@4.1.0","MIT","https://github.com/sindresorhus/log-symbols"
"log-update@6.1.0","MIT","https://github.com/sindresorhus/log-update"
"log4js@6.9.1","Apache-2.0","https://github.com/log4js-node/log4js-node"
"lru-cache@10.4.3","ISC","https://github.com/isaacs/node-lru-cache"
"lru-cache@5.1.1","ISC","https://github.com/isaacs/node-lru-cache"
"magic-string@0.30.17","MIT","https://github.com/rich-harris/magic-string"
"make-dir@2.1.0","MIT","https://github.com/sindresorhus/make-dir"
"make-dir@4.0.0","MIT","https://github.com/sindresorhus/make-dir"
"make-fetch-happen@14.0.3","ISC","https://github.com/npm/make-fetch-happen"
"math-intrinsics@1.1.0","MIT","https://github.com/es-shims/math-intrinsics"
"media-typer@0.3.0","MIT","https://github.com/jshttp/media-typer"
"memfs@4.17.2","Apache-2.0","https://github.com/streamich/memfs"
"merge-descriptors@1.0.3","MIT","https://github.com/sindresorhus/merge-descriptors"
"merge-stream@2.0.0","MIT","https://github.com/grncdr/merge-stream"
"merge2@1.4.1","MIT","https://github.com/teambition/merge2"
"methods@1.1.2","MIT","https://github.com/jshttp/methods"
"micromatch@4.0.8","MIT","https://github.com/micromatch/micromatch"
"mime-db@1.52.0","MIT","https://github.com/jshttp/mime-db"
"mime-types@2.1.35","MIT","https://github.com/jshttp/mime-types"
"mime@1.6.0","MIT","https://github.com/broofa/node-mime"
"mime@2.6.0","MIT","https://github.com/broofa/mime"
"mimic-fn@2.1.0","MIT","https://github.com/sindresorhus/mimic-fn"
"mimic-function@5.0.1","MIT","https://github.com/sindresorhus/mimic-function"
"mini-css-extract-plugin@2.9.2","MIT","https://github.com/webpack-contrib/mini-css-extract-plugin"
"minimalistic-assert@1.0.1","ISC","https://github.com/calvinmetcalf/minimalistic-assert"
"minimatch@3.1.2","ISC","https://github.com/isaacs/minimatch"
"minimatch@9.0.5","ISC","https://github.com/isaacs/minimatch"
"minimist@1.2.8","MIT","https://github.com/minimistjs/minimist"
"minipass-collect@2.0.1","ISC","https://github.com/isaacs/minipass-collect"
"minipass-fetch@4.0.1","MIT","https://github.com/npm/minipass-fetch"
"minipass-flush@1.0.5","ISC","https://github.com/isaacs/minipass-flush"
"minipass-pipeline@1.2.4","ISC",""
"minipass-sized@1.0.3","ISC","https://github.com/isaacs/minipass-sized"
"minipass@3.3.6","ISC","https://github.com/isaacs/minipass"
"minipass@5.0.0","ISC","https://github.com/isaacs/minipass"
"minipass@7.1.2","ISC","https://github.com/isaacs/minipass"
"minizlib@2.1.2","MIT","https://github.com/isaacs/minizlib"
"minizlib@3.0.2","MIT","https://github.com/isaacs/minizlib"
"mkdirp@0.5.6","MIT","https://github.com/substack/node-mkdirp"
"mkdirp@1.0.4","MIT","https://github.com/isaacs/node-mkdirp"
"mkdirp@3.0.1","MIT","https://github.com/isaacs/node-mkdirp"
"mrmime@2.0.1","MIT","https://github.com/lukeed/mrmime"
"ms@2.0.0","MIT","https://github.com/zeit/ms"
"ms@2.1.3","MIT","https://github.com/vercel/ms"
"msgpackr-extract@3.0.3","MIT","https://github.com/kriszyp/msgpackr-extract"
"msgpackr@1.11.4","MIT","https://github.com/kriszyp/msgpackr"
"multicast-dns@7.2.5","MIT","https://github.com/mafintosh/multicast-dns"
"mute-stream@1.0.0","ISC","https://github.com/npm/mute-stream"
"mute-stream@2.0.0","ISC","https://github.com/npm/mute-stream"
"nanoid@3.3.11","MIT","https://github.com/ai/nanoid"
"needle@3.3.1","MIT","https://github.com/tomas/needle"
"negotiator@0.6.3","MIT","https://github.com/jshttp/negotiator"
"negotiator@0.6.4","MIT","https://github.com/jshttp/negotiator"
"negotiator@1.0.0","MIT","https://github.com/jshttp/negotiator"
"neo-async@2.6.2","MIT","https://github.com/suguru03/neo-async"
"ng-zorro-antd@19.3.0","MIT","https://github.com/NG-ZORRO/ng-zorro-antd"
"node-addon-api@6.1.0","MIT","https://github.com/nodejs/node-addon-api"
"node-addon-api@7.1.1","MIT","https://github.com/nodejs/node-addon-api"
"node-forge@1.3.1","(BSD-3-Clause OR GPL-2.0)","https://github.com/digitalbazaar/forge"
"node-gyp-build-optional-packages@5.2.2","MIT","https://github.com/prebuild/node-gyp-build"
"node-gyp@11.2.0","MIT","https://github.com/nodejs/node-gyp"
"node-releases@2.0.19","MIT","https://github.com/chicoxyzzy/node-releases"
"nopt@8.1.0","ISC","https://github.com/npm/nopt"
"normalize-path@3.0.0","MIT","https://github.com/jonschlinkert/normalize-path"
"normalize-range@0.1.2","MIT","https://github.com/jamestalmage/normalize-range"
"npm-bundled@4.0.0","ISC","https://github.com/npm/npm-bundled"
"npm-install-checks@7.1.1","BSD-2-Clause","https://github.com/npm/npm-install-checks"
"npm-normalize-package-bin@4.0.0","ISC","https://github.com/npm/npm-normalize-package-bin"
"npm-package-arg@12.0.2","ISC","https://github.com/npm/npm-package-arg"
"npm-packlist@9.0.0","ISC","https://github.com/npm/npm-packlist"
"npm-pick-manifest@10.0.0","ISC","https://github.com/npm/npm-pick-manifest"
"npm-registry-fetch@18.0.2","ISC","https://github.com/npm/npm-registry-fetch"
"nth-check@2.1.1","BSD-2-Clause","https://github.com/fb55/nth-check"
"object-assign@4.1.1","MIT","https://github.com/sindresorhus/object-assign"
"object-inspect@1.13.4","MIT","https://github.com/inspect-js/object-inspect"
"obuf@1.1.2","MIT","https://github.com/indutny/offset-buffer"
"on-finished@2.3.0","MIT","https://github.com/jshttp/on-finished"
"on-finished@2.4.1","MIT","https://github.com/jshttp/on-finished"
"on-headers@1.0.2","MIT","https://github.com/jshttp/on-headers"
"once@1.4.0","ISC","https://github.com/isaacs/once"
"onetime@5.1.2","MIT","https://github.com/sindresorhus/onetime"
"onetime@7.0.0","MIT","https://github.com/sindresorhus/onetime"
"onramp-ui@0.0.0","UNLICENSED",""
"open@10.1.0","MIT","https://github.com/sindresorhus/open"
"ora@5.4.1","MIT","https://github.com/sindresorhus/ora"
"ordered-binary@1.5.3","MIT","https://github.com/kriszyp/ordered-binary"
"os-tmpdir@1.0.2","MIT","https://github.com/sindresorhus/os-tmpdir"
"p-limit@4.0.0","MIT","https://github.com/sindresorhus/p-limit"
"p-locate@6.0.0","MIT","https://github.com/sindresorhus/p-locate"
"p-map@7.0.3","MIT","https://github.com/sindresorhus/p-map"
"p-retry@6.2.1","MIT","https://github.com/sindresorhus/p-retry"
"package-json-from-dist@1.0.1","BlueOak-1.0.0","https://github.com/isaacs/package-json-from-dist"
"pacote@20.0.0","ISC","https://github.com/npm/pacote"
"papaparse@5.5.3","MIT","https://github.com/mholt/PapaParse"
"parent-module@1.0.1","MIT","https://github.com/sindresorhus/parent-module"
"parse-json@5.2.0","MIT","https://github.com/sindresorhus/parse-json"
"parse-node-version@1.0.1","MIT","https://github.com/gulpjs/parse-node-version"
"parse5-html-rewriting-stream@7.0.0","MIT","https://github.com/inikulin/parse5"
"parse5-sax-parser@7.0.0","MIT","https://github.com/inikulin/parse5"
"parse5@7.3.0","MIT","https://github.com/inikulin/parse5"
"parseurl@1.3.3","MIT","https://github.com/pillarjs/parseurl"
"path-exists@5.0.0","MIT","https://github.com/sindresorhus/path-exists"
"path-is-absolute@1.0.1","MIT","https://github.com/sindresorhus/path-is-absolute"
"path-key@3.1.1","MIT","https://github.com/sindresorhus/path-key"
"path-parse@1.0.7","MIT","https://github.com/jbgutierrez/path-parse"
"path-scurry@1.11.1","BlueOak-1.0.0","https://github.com/isaacs/path-scurry"
"path-to-regexp@0.1.12","MIT","https://github.com/pillarjs/path-to-regexp"
"path-type@6.0.0","MIT","https://github.com/sindresorhus/path-type"
"picocolors@1.1.1","ISC","https://github.com/alexeyraspopov/picocolors"
"picomatch@2.3.1","MIT","https://github.com/micromatch/picomatch"
"picomatch@4.0.2","MIT","https://github.com/micromatch/picomatch"
"pify@4.0.1","MIT","https://github.com/sindresorhus/pify"
"piscina@4.8.0","MIT","https://github.com/piscinajs/piscina"
"pkg-dir@7.0.0","MIT","https://github.com/sindresorhus/pkg-dir"
"postcss-loader@8.1.1","MIT","https://github.com/webpack-contrib/postcss-loader"
"postcss-media-query-parser@0.2.3","MIT","https://github.com/dryoma/postcss-media-query-parser"
"postcss-modules-extract-imports@3.1.0","ISC","https://github.com/css-modules/postcss-modules-extract-imports"
"postcss-modules-local-by-default@4.2.0","MIT","https://github.com/css-modules/postcss-modules-local-by-default"
"postcss-modules-scope@3.2.1","ISC","https://github.com/css-modules/postcss-modules-scope"
"postcss-modules-values@4.0.0","ISC","https://github.com/css-modules/postcss-modules-values"
"postcss-selector-parser@7.1.0","MIT","https://github.com/postcss/postcss-selector-parser"
"postcss-value-parser@4.2.0","MIT","https://github.com/TrySound/postcss-value-parser"
"postcss@8.5.2","MIT","https://github.com/postcss/postcss"
"postcss@8.5.3","MIT","https://github.com/postcss/postcss"
"proc-log@5.0.0","ISC","https://github.com/npm/proc-log"
"process-nextick-args@2.0.1","MIT","https://github.com/calvinmetcalf/process-nextick-args"
"promise-retry@2.0.1","MIT","https://github.com/IndigoUnited/node-promise-retry"
"proxy-addr@2.0.7","MIT","https://github.com/jshttp/proxy-addr"
"prr@1.0.1","MIT","https://github.com/rvagg/prr"
"punycode@1.4.1","MIT","https://github.com/bestiejs/punycode.js"
"qjobs@1.2.0","MIT","https://github.com/franck34/qjobs"
"qs@6.13.0","BSD-3-Clause","https://github.com/ljharb/qs"
"queue-microtask@1.2.3","MIT","https://github.com/feross/queue-microtask"
"randombytes@2.1.0","MIT","https://github.com/crypto-browserify/randombytes"
"range-parser@1.2.1","MIT","https://github.com/jshttp/range-parser"
"raw-body@2.5.2","MIT","https://github.com/stream-utils/raw-body"
"readable-stream@2.3.8","MIT","https://github.com/nodejs/readable-stream"
"readable-stream@3.6.2","MIT","https://github.com/nodejs/readable-stream"
"readdirp@3.6.0","MIT","https://github.com/paulmillr/readdirp"
"readdirp@4.1.2","MIT","https://github.com/paulmillr/readdirp"
"reflect-metadata@0.2.2","Apache-2.0","https://github.com/rbuckton/reflect-metadata"
"regenerate-unicode-properties@10.2.0","MIT","https://github.com/mathiasbynens/regenerate-unicode-properties"
"regenerate@1.4.2","MIT","https://github.com/mathiasbynens/regenerate"
"regenerator-runtime@0.14.1","MIT","https://github.com/facebook/regenerator/tree/main/packages/runtime"
"regex-parser@2.3.1","MIT","https://github.com/IonicaBizau/regex-parser.js"
"regexpu-core@6.2.0","MIT","https://github.com/mathiasbynens/regexpu-core"
"regjsgen@0.8.0","MIT","https://github.com/bnjmnt4n/regjsgen"
"regjsparser@0.12.0","BSD-2-Clause","https://github.com/jviereck/regjsparser"
"require-directory@2.1.1","MIT","https://github.com/troygoode/node-require-directory"
"require-from-string@2.0.2","MIT","https://github.com/floatdrop/require-from-string"
"requires-port@1.0.0","MIT","https://github.com/unshiftio/requires-port"
"resolve-from@4.0.0","MIT","https://github.com/sindresorhus/resolve-from"
"resolve-url-loader@5.0.0","MIT","https://github.com/bholloway/resolve-url-loader"
"resolve@1.22.10","MIT","https://github.com/browserify/resolve"
"restore-cursor@3.1.0","MIT","https://github.com/sindresorhus/restore-cursor"
"restore-cursor@5.1.0","MIT","https://github.com/sindresorhus/restore-cursor"
"retry@0.12.0","MIT","https://github.com/tim-kos/node-retry"
"retry@0.13.1","MIT","https://github.com/tim-kos/node-retry"
"reusify@1.1.0","MIT","https://github.com/mcollina/reusify"
"rfdc@1.4.1","MIT","https://github.com/davidmarkclements/rfdc"
"rimraf@3.0.2","ISC","https://github.com/isaacs/rimraf"
"rollup@4.34.8","MIT","https://github.com/rollup/rollup"
"rollup@4.41.1","MIT","https://github.com/rollup/rollup"
"run-applescript@7.0.0","MIT","https://github.com/sindresorhus/run-applescript"
"run-parallel@1.2.0","MIT","https://github.com/feross/run-parallel"
"rxjs@7.8.1","Apache-2.0","https://github.com/reactivex/rxjs"
"rxjs@7.8.2","Apache-2.0","https://github.com/reactivex/rxjs"
"safe-buffer@5.1.2","MIT","https://github.com/feross/safe-buffer"
"safe-buffer@5.2.1","MIT","https://github.com/feross/safe-buffer"
"safe-regex-test@1.1.0","MIT","https://github.com/ljharb/safe-regex-test"
"safer-buffer@2.1.2","MIT","https://github.com/ChALkeR/safer-buffer"
"sass-loader@16.0.5","MIT","https://github.com/webpack-contrib/sass-loader"
"sass@1.85.0","MIT","https://github.com/sass/dart-sass"
"sax@1.4.1","ISC","https://github.com/isaacs/sax-js"
"schema-utils@4.3.2","MIT","https://github.com/webpack/schema-utils"
"select-hose@2.0.0","MIT","https://github.com/indutny/select-hose"
"selfsigned@2.4.1","MIT","https://github.com/jfromaniello/selfsigned"
"semver@5.7.2","ISC","https://github.com/npm/node-semver"
"semver@6.3.1","ISC","https://github.com/npm/node-semver"
"semver@7.7.1","ISC","https://github.com/npm/node-semver"
"send@0.19.0","MIT","https://github.com/pillarjs/send"
"serialize-javascript@6.0.2","BSD-3-Clause","https://github.com/yahoo/serialize-javascript"
"serve-index@1.9.1","MIT","https://github.com/expressjs/serve-index"
"serve-static@1.16.2","MIT","https://github.com/expressjs/serve-static"
"setprototypeof@1.1.0","ISC","https://github.com/wesleytodd/setprototypeof"
"setprototypeof@1.2.0","ISC","https://github.com/wesleytodd/setprototypeof"
"shallow-clone@3.0.1","MIT","https://github.com/jonschlinkert/shallow-clone"
"shebang-command@2.0.0","MIT","https://github.com/kevva/shebang-command"
"shebang-regex@3.0.0","MIT","https://github.com/sindresorhus/shebang-regex"
"shell-quote@1.8.2","MIT","https://github.com/ljharb/shell-quote"
"side-channel-list@1.0.0","MIT","https://github.com/ljharb/side-channel-list"
"side-channel-map@1.0.1","MIT","https://github.com/ljharb/side-channel-map"
"side-channel-weakmap@1.0.2","MIT","https://github.com/ljharb/side-channel-weakmap"
"side-channel@1.1.0","MIT","https://github.com/ljharb/side-channel"
"signal-exit@3.0.7","ISC","https://github.com/tapjs/signal-exit"
"signal-exit@4.1.0","ISC","https://github.com/tapjs/signal-exit"
"sigstore@3.1.0","Apache-2.0","https://github.com/sigstore/sigstore-js"
"slash@5.1.0","MIT","https://github.com/sindresorhus/slash"
"slice-ansi@5.0.0","MIT","https://github.com/chalk/slice-ansi"
"slice-ansi@7.1.0","MIT","https://github.com/chalk/slice-ansi"
"smart-buffer@4.2.0","MIT","https://github.com/JoshGlazebrook/smart-buffer"
"socket.io-adapter@2.5.5","MIT","https://github.com/socketio/socket.io-adapter"
"socket.io-parser@4.2.4","MIT","https://github.com/socketio/socket.io-parser"
"socket.io@4.8.1","MIT","https://github.com/socketio/socket.io"
"sockjs@0.3.24","MIT","https://github.com/sockjs/sockjs-node"
"socks-proxy-agent@8.0.5","MIT","https://github.com/TooTallNate/proxy-agents"
"socks@2.8.4","MIT","https://github.com/JoshGlazebrook/socks"
"source-map-js@1.2.1","BSD-3-Clause","https://github.com/7rulnik/source-map-js"
"source-map-loader@5.0.0","MIT","https://github.com/webpack-contrib/source-map-loader"
"source-map-support@0.5.21","MIT","https://github.com/evanw/node-source-map-support"
"source-map@0.6.1","BSD-3-Clause","https://github.com/mozilla/source-map"
"source-map@0.7.4","BSD-3-Clause","https://github.com/mozilla/source-map"
"spdx-correct@3.2.0","Apache-2.0","https://github.com/jslicense/spdx-correct.js"
"spdx-exceptions@2.5.0","CC-BY-3.0","https://github.com/kemitchell/spdx-exceptions.json"
"spdx-expression-parse@3.0.1","MIT","https://github.com/jslicense/spdx-expression-parse.js"
"spdx-license-ids@3.0.21","CC0-1.0","https://github.com/jslicense/spdx-license-ids"
"spdy-transport@3.0.0","MIT","https://github.com/spdy-http2/spdy-transport"
"spdy@4.0.2","MIT","https://github.com/indutny/node-spdy"
"sprintf-js@1.1.3","BSD-3-Clause","https://github.com/alexei/sprintf.js"
"ssri@12.0.0","ISC","https://github.com/npm/ssri"
"statuses@1.5.0","MIT","https://github.com/jshttp/statuses"
"statuses@2.0.1","MIT","https://github.com/jshttp/statuses"
"streamroller@3.1.5","MIT","https://github.com/log4js-node/streamroller"
"string-width@4.2.3","MIT","https://github.com/sindresorhus/string-width"
"string-width@5.1.2","MIT","https://github.com/sindresorhus/string-width"
"string-width@7.2.0","MIT","https://github.com/sindresorhus/string-width"
"string_decoder@1.1.1","MIT","https://github.com/nodejs/string_decoder"
"string_decoder@1.3.0","MIT","https://github.com/nodejs/string_decoder"
"strip-ansi@6.0.1","MIT","https://github.com/chalk/strip-ansi"
"strip-ansi@7.1.0","MIT","https://github.com/chalk/strip-ansi"
"supports-color@7.2.0","MIT","https://github.com/chalk/supports-color"
"supports-color@8.1.1","MIT","https://github.com/chalk/supports-color"
"supports-preserve-symlinks-flag@1.0.0","MIT","https://github.com/inspect-js/node-supports-preserve-symlinks-flag"
"symbol-observable@4.0.0","MIT","https://github.com/blesh/symbol-observable"
"tapable@2.2.2","MIT","https://github.com/webpack/tapable"
"tar@6.2.1","ISC","https://github.com/isaacs/node-tar"
"tar@7.4.3","ISC","https://github.com/isaacs/node-tar"
"terser-webpack-plugin@5.3.14","MIT","https://github.com/webpack-contrib/terser-webpack-plugin"
"terser@5.39.0","BSD-2-Clause","https://github.com/terser/terser"
"thingies@1.21.0","Unlicense","https://github.com/streamich/thingies"
"thunky@1.1.0","MIT","https://github.com/mafintosh/thunky"
"tinyglobby@0.2.14","MIT","https://github.com/SuperchupuDev/tinyglobby"
"tmp@0.0.33","MIT","https://github.com/raszi/node-tmp"
"tmp@0.2.3","MIT","https://github.com/raszi/node-tmp"
"to-regex-range@5.0.1","MIT","https://github.com/micromatch/to-regex-range"
"toidentifier@1.0.1","MIT","https://github.com/component/toidentifier"
"tree-dump@1.0.3","Apache-2.0","https://github.com/streamich/tree-dump"
"tree-kill@1.2.2","MIT","https://github.com/pkrumins/node-tree-kill"
"tslib@2.8.1","0BSD","https://github.com/Microsoft/tslib"
"tuf-js@3.0.1","MIT","https://github.com/theupdateframework/tuf-js"
"type-fest@0.21.3","(MIT OR CC0-1.0)","https://github.com/sindresorhus/type-fest"
"type-is@1.6.18","MIT","https://github.com/jshttp/type-is"
"typed-assert@1.0.9","MIT","https://github.com/elierotenberg/typed-assert"
"typescript@5.7.3","Apache-2.0","https://github.com/microsoft/TypeScript"
"ua-parser-js@0.7.40","MIT","https://github.com/faisalman/ua-parser-js"
"undici-types@6.21.0","MIT","https://github.com/nodejs/undici"
"unicode-canonical-property-names-ecmascript@2.0.1","MIT","https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript"
"unicode-match-property-ecmascript@2.0.0","MIT","https://github.com/mathiasbynens/unicode-match-property-ecmascript"
"unicode-match-property-value-ecmascript@2.2.0","MIT","https://github.com/mathiasbynens/unicode-match-property-value-ecmascript"
"unicode-property-aliases-ecmascript@2.1.0","MIT","https://github.com/mathiasbynens/unicode-property-aliases-ecmascript"
"unicorn-magic@0.3.0","MIT","https://github.com/sindresorhus/unicorn-magic"
"unique-filename@4.0.0","ISC","https://github.com/npm/unique-filename"
"unique-slug@5.0.0","ISC","https://github.com/npm/unique-slug"
"universalify@0.1.2","MIT","https://github.com/RyanZim/universalify"
"unpipe@1.0.0","MIT","https://github.com/stream-utils/unpipe"
"update-browserslist-db@1.1.3","MIT","https://github.com/browserslist/update-db"
"util-deprecate@1.0.2","MIT","https://github.com/TooTallNate/util-deprecate"
"utils-merge@1.0.1","MIT","https://github.com/jaredhanson/utils-merge"
"uuid@8.3.2","MIT","https://github.com/uuidjs/uuid"
"validate-npm-package-license@3.0.4","Apache-2.0","https://github.com/kemitchell/validate-npm-package-license.js"
"validate-npm-package-name@6.0.0","ISC","https://github.com/npm/validate-npm-package-name"
"vary@1.1.2","MIT","https://github.com/jshttp/vary"
"vite@6.2.7","MIT","https://github.com/vitejs/vite"
"vite@6.3.5","MIT","https://github.com/vitejs/vite"
"void-elements@2.0.1","MIT","https://github.com/hemanth/void-elements"
"watchpack@2.4.2","MIT","https://github.com/webpack/watchpack"
"wbuf@1.7.3","MIT","https://github.com/indutny/wbuf"
"wcwidth@1.0.1","MIT","https://github.com/timoxley/wcwidth"
"weak-lru-cache@1.2.2","MIT","https://github.com/kriszyp/weak-lru-cache"
"webpack-dev-middleware@7.4.2","MIT","https://github.com/webpack/webpack-dev-middleware"
"webpack-dev-server@5.2.0","MIT","https://github.com/webpack/webpack-dev-server"
"webpack-merge@6.0.1","MIT","https://github.com/survivejs/webpack-merge"
"webpack-sources@3.3.0","MIT","https://github.com/webpack/webpack-sources"
"webpack-subresource-integrity@5.1.0","MIT","https://github.com/waysact/webpack-subresource-integrity"
"webpack@5.98.0","MIT","https://github.com/webpack/webpack"
"websocket-driver@0.7.4","Apache-2.0","https://github.com/faye/websocket-driver-node"
"websocket-extensions@0.1.4","Apache-2.0","https://github.com/faye/websocket-extensions-node"
"which@1.3.1","ISC","https://github.com/isaacs/node-which"
"which@2.0.2","ISC","https://github.com/isaacs/node-which"
"which@5.0.0","ISC","https://github.com/npm/node-which"
"wildcard@2.0.1","MIT","https://github.com/DamonOehlman/wildcard"
"wrap-ansi@6.2.0","MIT","https://github.com/chalk/wrap-ansi"
"wrap-ansi@7.0.0","MIT","https://github.com/chalk/wrap-ansi"
"wrap-ansi@8.1.0","MIT","https://github.com/chalk/wrap-ansi"
"wrap-ansi@9.0.0","MIT","https://github.com/chalk/wrap-ansi"
"wrappy@1.0.2","ISC","https://github.com/npm/wrappy"
"ws@8.17.1","MIT","https://github.com/websockets/ws"
"ws@8.18.2","MIT","https://github.com/websockets/ws"
"y18n@5.0.8","ISC","https://github.com/yargs/y18n"
"yallist@3.1.1","ISC","https://github.com/isaacs/yallist"
"yallist@4.0.0","ISC","https://github.com/isaacs/yallist"
"yallist@5.0.0","BlueOak-1.0.0","https://github.com/isaacs/yallist"
"yargs-parser@20.2.9","ISC","https://github.com/yargs/yargs-parser"
"yargs-parser@21.1.1","ISC","https://github.com/yargs/yargs-parser"
"yargs@16.2.0","MIT","https://github.com/yargs/yargs"
"yargs@17.7.2","MIT","https://github.com/yargs/yargs"
"yocto-queue@1.2.1","MIT","https://github.com/sindresorhus/yocto-queue"
"yoctocolors-cjs@2.1.2","MIT","https://github.com/sindresorhus/yoctocolors"
"zone.js@0.15.1","MIT","https://github.com/angular/angular"
"@babel/code-frame@7.27.1","MIT","https://github.com/babel/babel"
"@babel/helper-validator-identifier@7.27.1","MIT","https://github.com/babel/babel"
"@bazel/runfiles@6.3.1","Apache-2.0","https://github.com/bazel-contrib/rules_nodejs"
"@colors/colors@1.5.0","MIT","https://github.com/DABH/colors.js"
"@cucumber/ci-environment@10.0.1","MIT","https://github.com/cucumber/ci-environment"
"@cucumber/cucumber-expressions@18.0.1","MIT","https://github.com/cucumber/cucumber-expressions"
"@cucumber/cucumber@11.3.0","MIT","https://github.com/cucumber/cucumber-js"
"@cucumber/gherkin-streams@5.0.1","MIT","https://github.com/cucumber/gherkin-streams"
"@cucumber/gherkin-utils@9.2.0","MIT","https://github.com/cucumber/gherkin-utils"
"@cucumber/gherkin@30.0.4","MIT","https://github.com/cucumber/gherkin"
"@cucumber/gherkin@31.0.0","MIT","https://github.com/cucumber/gherkin"
"@cucumber/html-formatter@21.10.1","MIT","https://github.com/cucumber/html-formatter"
"@cucumber/junit-xml-formatter@0.7.1","MIT","https://github.com/cucumber/junit-xml-formatter"
"@cucumber/message-streams@4.0.1","MIT","https://github.com/cucumber/message-streams"
"@cucumber/messages@26.0.1","MIT","https://github.com/cucumber/messages"
"@cucumber/messages@27.2.0","MIT","https://github.com/cucumber/messages"
"@cucumber/query@13.2.0","MIT","https://github.com/cucumber/query"
"@cucumber/tag-expressions@6.1.2","MIT","https://github.com/cucumber/tag-expressions"
"@hapi/hoek@9.3.0","BSD-3-Clause","https://github.com/hapijs/hoek"
"@hapi/topo@5.1.0","BSD-3-Clause","https://github.com/hapijs/topo"
"@isaacs/cliui@8.0.2","ISC","https://github.com/yargs/cliui"
"@pkgjs/parseargs@0.11.0","MIT","https://github.com/pkgjs/parseargs"
"@sideway/address@4.1.5","BSD-3-Clause","https://github.com/sideway/address"
"@sideway/formula@3.0.1","BSD-3-Clause","https://github.com/sideway/formula"
"@sideway/pinpoint@2.0.0","BSD-3-Clause","https://github.com/sideway/pinpoint"
"@teppeis/multimaps@3.0.0","MIT","https://github.com/teppeis/multimaps"
"@types/normalize-package-data@2.4.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/uuid@10.0.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"ansi-regex@4.1.1","MIT","https://github.com/chalk/ansi-regex"
"ansi-regex@5.0.1","MIT","https://github.com/chalk/ansi-regex"
"ansi-regex@6.1.0","MIT","https://github.com/chalk/ansi-regex"
"ansi-styles@4.3.0","MIT","https://github.com/chalk/ansi-styles"
"ansi-styles@6.2.1","MIT","https://github.com/chalk/ansi-styles"
"any-promise@1.3.0","MIT","https://github.com/kevinbeaty/any-promise"
"assertion-error-formatter@3.0.0","MIT","https://github.com/charlierudolph/node-assertion-error-formatter"
"asynckit@0.4.0","MIT","https://github.com/alexindigo/asynckit"
"axios@1.9.0","MIT","https://github.com/axios/axios"
"balanced-match@1.0.2","MIT","https://github.com/juliangruber/balanced-match"
"brace-expansion@2.0.2","MIT","https://github.com/juliangruber/brace-expansion"
"buffer-from@1.1.2","MIT","https://github.com/LinusU/buffer-from"
"call-bind-apply-helpers@1.0.2","MIT","https://github.com/ljharb/call-bind-apply-helpers"
"capital-case@1.0.4","MIT","https://github.com/blakeembrey/change-case"
"chalk@4.1.2","MIT","https://github.com/chalk/chalk"
"class-transformer@0.5.1","MIT","https://github.com/typestack/class-transformer"
"cli-table3@0.6.5","MIT","https://github.com/cli-table/cli-table3"
"color-convert@2.0.1","MIT","https://github.com/Qix-/color-convert"
"color-name@1.1.4","MIT","https://github.com/colorjs/color-name"
"combined-stream@1.0.8","MIT","https://github.com/felixge/node-combined-stream"
"commander@10.0.1","MIT","https://github.com/tj/commander.js"
"commander@13.1.0","MIT","https://github.com/tj/commander.js"
"commander@9.1.0","MIT","https://github.com/tj/commander.js"
"core-util-is@1.0.3","MIT","https://github.com/isaacs/core-util-is"
"cross-spawn@7.0.6","MIT","https://github.com/moxystudio/node-cross-spawn"
"debug@4.4.1","MIT","https://github.com/debug-js/debug"
"delayed-stream@1.0.0","MIT","https://github.com/felixge/node-delayed-stream"
"diff@4.0.2","BSD-3-Clause","https://github.com/kpdecker/jsdiff"
"dunder-proto@1.0.1","MIT","https://github.com/es-shims/dunder-proto"
"e2e@0.1.0","UNKNOWN",""
"eastasianwidth@0.2.0","MIT","https://github.com/komagata/eastasianwidth"
"emoji-regex@8.0.0","MIT","https://github.com/mathiasbynens/emoji-regex"
"emoji-regex@9.2.2","MIT","https://github.com/mathiasbynens/emoji-regex"
"error-stack-parser@2.1.4","MIT","https://github.com/stacktracejs/error-stack-parser"
"es-define-property@1.0.1","MIT","https://github.com/ljharb/es-define-property"
"es-errors@1.3.0","MIT","https://github.com/ljharb/es-errors"
"es-object-atoms@1.1.1","MIT","https://github.com/ljharb/es-object-atoms"
"es-set-tostringtag@2.1.0","MIT","https://github.com/es-shims/es-set-tostringtag"
"escape-string-regexp@1.0.5","MIT","https://github.com/sindresorhus/escape-string-regexp"
"figures@3.2.0","MIT","https://github.com/sindresorhus/figures"
"find-up-simple@1.0.1","MIT","https://github.com/sindresorhus/find-up-simple"
"follow-redirects@1.15.9","MIT","https://github.com/follow-redirects/follow-redirects"
"foreground-child@3.3.1","ISC","https://github.com/tapjs/foreground-child"
"form-data@4.0.2","MIT","https://github.com/form-data/form-data"
"function-bind@1.1.2","MIT","https://github.com/Raynos/function-bind"
"get-intrinsic@1.3.0","MIT","https://github.com/ljharb/get-intrinsic"
"get-proto@1.0.1","MIT","https://github.com/ljharb/get-proto"
"glob@10.4.5","ISC","https://github.com/isaacs/node-glob"
"global-dirs@3.0.1","MIT","https://github.com/sindresorhus/global-dirs"
"gopd@1.2.0","MIT","https://github.com/ljharb/gopd"
"has-ansi@4.0.1","MIT","https://github.com/chalk/has-ansi"
"has-flag@4.0.0","MIT","https://github.com/sindresorhus/has-flag"
"has-symbols@1.1.0","MIT","https://github.com/inspect-js/has-symbols"
"has-tostringtag@1.0.2","MIT","https://github.com/inspect-js/has-tostringtag"
"hasown@2.0.2","MIT","https://github.com/inspect-js/hasOwn"
"hosted-git-info@7.0.2","ISC","https://github.com/npm/hosted-git-info"
"immediate@3.0.6","MIT","https://github.com/calvinmetcalf/immediate"
"indent-string@4.0.0","MIT","https://github.com/sindresorhus/indent-string"
"index-to-position@1.1.0","MIT","https://github.com/sindresorhus/index-to-position"
"inherits@2.0.4","ISC","https://github.com/isaacs/inherits"
"ini@2.0.0","ISC","https://github.com/isaacs/ini"
"is-fullwidth-code-point@3.0.0","MIT","https://github.com/sindresorhus/is-fullwidth-code-point"
"is-installed-globally@0.4.0","MIT","https://github.com/sindresorhus/is-installed-globally"
"is-path-inside@3.0.3","MIT","https://github.com/sindresorhus/is-path-inside"
"is-stream@2.0.1","MIT","https://github.com/sindresorhus/is-stream"
"isarray@1.0.0","MIT","https://github.com/juliangruber/isarray"
"isexe@2.0.0","ISC","https://github.com/isaacs/isexe"
"jackspeak@3.4.3","BlueOak-1.0.0","https://github.com/isaacs/jackspeak"
"joi@17.13.3","BSD-3-Clause","https://github.com/hapijs/joi"
"js-tokens@4.0.0","MIT","https://github.com/lydell/js-tokens"
"jszip@3.10.1","(MIT OR GPL-3.0-or-later)","https://github.com/Stuk/jszip"
"knuth-shuffle-seeded@1.0.6","Apache-2.0","https://github.com/TimothyGu/knuth-shuffle-seeded"
"lie@3.3.0","MIT","https://github.com/calvinmetcalf/lie"
"lodash.merge@4.6.2","MIT","https://github.com/lodash/lodash"
"lodash.mergewith@4.6.2","MIT","https://github.com/lodash/lodash"
"lodash@4.17.21","MIT","https://github.com/lodash/lodash"
"lower-case@2.0.2","MIT","https://github.com/blakeembrey/change-case"
"lru-cache@10.4.3","ISC","https://github.com/isaacs/node-lru-cache"
"luxon@3.6.1","MIT","https://github.com/moment/luxon"
"math-intrinsics@1.1.0","MIT","https://github.com/es-shims/math-intrinsics"
"mime-db@1.52.0","MIT","https://github.com/jshttp/mime-db"
"mime-types@2.1.35","MIT","https://github.com/jshttp/mime-types"
"mime@3.0.0","MIT","https://github.com/broofa/mime"
"minimatch@9.0.5","ISC","https://github.com/isaacs/minimatch"
"minimist@1.2.8","MIT","https://github.com/minimistjs/minimist"
"minipass@7.1.2","ISC","https://github.com/isaacs/minipass"
"mkdirp@2.1.6","MIT","https://github.com/isaacs/node-mkdirp"
"ms@2.1.3","MIT","https://github.com/vercel/ms"
"mz@2.7.0","MIT","https://github.com/normalize/mz"
"no-case@3.0.4","MIT","https://github.com/blakeembrey/change-case"
"normalize-package-data@6.0.2","BSD-2-Clause","https://github.com/npm/normalize-package-data"
"object-assign@4.1.1","MIT","https://github.com/sindresorhus/object-assign"
"package-json-from-dist@1.0.1","BlueOak-1.0.0","https://github.com/isaacs/package-json-from-dist"
"pad-right@0.2.2","MIT","https://github.com/jonschlinkert/pad-right"
"pako@1.0.11","(MIT AND Zlib)","https://github.com/nodeca/pako"
"parse-json@8.3.0","MIT","https://github.com/sindresorhus/parse-json"
"path-key@3.1.1","MIT","https://github.com/sindresorhus/path-key"
"path-scurry@1.11.1","BlueOak-1.0.0","https://github.com/isaacs/path-scurry"
"picocolors@1.1.1","ISC","https://github.com/alexeyraspopov/picocolors"
"process-nextick-args@2.0.1","MIT","https://github.com/calvinmetcalf/process-nextick-args"
"progress@2.0.3","MIT","https://github.com/visionmedia/node-progress"
"property-expr@2.0.6","MIT","https://github.com/jquense/expr"
"proxy-from-env@1.1.0","MIT","https://github.com/Rob--W/proxy-from-env"
"read-package-up@11.0.0","MIT","https://github.com/sindresorhus/read-package-up"
"read-pkg@9.0.1","MIT","https://github.com/sindresorhus/read-pkg"
"readable-stream@2.3.8","MIT","https://github.com/nodejs/readable-stream"
"reflect-metadata@0.2.2","Apache-2.0","https://github.com/rbuckton/reflect-metadata"
"regexp-match-indices@1.0.2","Apache-2.0",""
"regexp-tree@0.1.27","MIT","https://github.com/DmitrySoshnikov/regexp-tree"
"repeat-string@1.6.1","MIT","https://github.com/jonschlinkert/repeat-string"
"rxjs@7.8.2","Apache-2.0","https://github.com/reactivex/rxjs"
"safe-buffer@5.1.2","MIT","https://github.com/feross/safe-buffer"
"seed-random@2.2.0","MIT","https://github.com/ForbesLindesay/seed-random"
"selenium-webdriver@4.32.0","Apache-2.0","https://github.com/SeleniumHQ/selenium"
"semver@7.7.1","ISC","https://github.com/npm/node-semver"
"setimmediate@1.0.5","MIT","https://github.com/YuzuJS/setImmediate"
"shebang-command@2.0.0","MIT","https://github.com/kevva/shebang-command"
"shebang-regex@3.0.0","MIT","https://github.com/sindresorhus/shebang-regex"
"signal-exit@4.1.0","ISC","https://github.com/tapjs/signal-exit"
"source-map-support@0.5.21","MIT","https://github.com/evanw/node-source-map-support"
"source-map@0.6.1","BSD-3-Clause","https://github.com/mozilla/source-map"
"spdx-correct@3.2.0","Apache-2.0","https://github.com/jslicense/spdx-correct.js"
"spdx-exceptions@2.5.0","CC-BY-3.0","https://github.com/kemitchell/spdx-exceptions.json"
"spdx-expression-parse@3.0.1","MIT","https://github.com/jslicense/spdx-expression-parse.js"
"spdx-license-ids@3.0.21","CC0-1.0","https://github.com/jslicense/spdx-license-ids"
"stackframe@1.3.4","MIT","https://github.com/stacktracejs/stackframe"
"string-argv@0.3.1","MIT","https://github.com/mccormicka/string-argv"
"string-width@4.2.3","MIT","https://github.com/sindresorhus/string-width"
"string-width@5.1.2","MIT","https://github.com/sindresorhus/string-width"
"string_decoder@1.1.1","MIT","https://github.com/nodejs/string_decoder"
"strip-ansi@6.0.1","MIT","https://github.com/chalk/strip-ansi"
"strip-ansi@7.1.0","MIT","https://github.com/chalk/strip-ansi"
"supports-color@7.2.0","MIT","https://github.com/chalk/supports-color"
"supports-color@8.1.1","MIT","https://github.com/chalk/supports-color"
"thenify-all@1.6.0","MIT","https://github.com/thenables/thenify-all"
"thenify@3.3.1","MIT","https://github.com/thenables/thenify"
"tiny-case@1.0.3","MIT","https://github.com/jquense/tiny-case"
"tmp@0.2.3","MIT","https://github.com/raszi/node-tmp"
"toposort@2.0.2","MIT","https://github.com/marcelklehr/toposort"
"tslib@2.8.1","0BSD","https://github.com/Microsoft/tslib"
"type-fest@2.19.0","(MIT OR CC0-1.0)","https://github.com/sindresorhus/type-fest"
"type-fest@4.41.0","(MIT OR CC0-1.0)","https://github.com/sindresorhus/type-fest"
"unicorn-magic@0.1.0","MIT","https://github.com/sindresorhus/unicorn-magic"
"upper-case-first@2.0.2","MIT","https://github.com/blakeembrey/change-case"
"util-arity@1.1.0","MIT","https://github.com/blakeembrey/arity"
"util-deprecate@1.0.2","MIT","https://github.com/TooTallNate/util-deprecate"
"uuid@10.0.0","MIT","https://github.com/uuidjs/uuid"
"uuid@11.0.5","MIT","https://github.com/uuidjs/uuid"
"validate-npm-package-license@3.0.4","Apache-2.0","https://github.com/kemitchell/validate-npm-package-license.js"
"wait-on@8.0.3","MIT","https://github.com/jeffbski/wait-on"
"which@2.0.2","ISC","https://github.com/isaacs/node-which"
"wrap-ansi@7.0.0","MIT","https://github.com/chalk/wrap-ansi"
"wrap-ansi@8.1.0","MIT","https://github.com/chalk/wrap-ansi"
"ws@8.18.2","MIT","https://github.com/websockets/ws"
"xmlbuilder@15.1.1","MIT","https://github.com/oozcitak/xmlbuilder-js"
"yaml@2.8.0","ISC","https://github.com/eemeli/yaml"
"yup@1.6.1","MIT","https://github.com/jquense/yup"
