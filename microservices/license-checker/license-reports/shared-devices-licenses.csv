bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/audit_logs,Unknown,Unknown
bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/basic,Unknown,Unknown
bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_comms,Unknown,Unknown
bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_config,Unknown,Unknown
bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_dfu,Unknown,Unknown
bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_logs,Unknown,Unknown
bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_realtime,Unknown,Unknown
bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_stats,Unknown,Unknown
bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/dfu,Unknown,Unknown
bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_faults,Unknown,Unknown
bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_logs,Unknown,Unknown
bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/realtime,Unknown,Unknown
bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/settings,Unknown,Unknown
bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/wrappers,Unknown,Unknown
cloud.google.com/go,https://github.com/googleapis/google-cloud-go/blob/v0.121.0/LICENSE,Apache-2.0
cloud.google.com/go/auth,https://github.com/googleapis/google-cloud-go/blob/auth/v0.16.1/auth/LICENSE,Apache-2.0
cloud.google.com/go/auth/oauth2adapt,https://github.com/googleapis/google-cloud-go/blob/auth/oauth2adapt/v0.2.8/auth/oauth2adapt/LICENSE,Apache-2.0
cloud.google.com/go/bigquery,https://github.com/googleapis/google-cloud-go/blob/bigquery/v1.68.0/bigquery/LICENSE,Apache-2.0
cloud.google.com/go/compute/metadata,https://github.com/googleapis/google-cloud-go/blob/compute/metadata/v0.6.0/compute/metadata/LICENSE,Apache-2.0
cloud.google.com/go/iam,https://github.com/googleapis/google-cloud-go/blob/iam/v1.5.2/iam/LICENSE,Apache-2.0
cloud.google.com/go/pubsub,https://github.com/googleapis/google-cloud-go/blob/pubsub/v1.49.0/pubsub/LICENSE,Apache-2.0
github.com/apache/arrow/go/v15,https://github.com/apache/arrow/blob/go/v15.0.2/go/LICENSE.txt,Apache-2.0
github.com/felixge/httpsnoop,https://github.com/felixge/httpsnoop/blob/v1.0.4/LICENSE.txt,MIT
github.com/go-logr/logr,https://github.com/go-logr/logr/blob/v1.4.2/LICENSE,Apache-2.0
github.com/go-logr/stdr,https://github.com/go-logr/stdr/blob/v1.2.2/LICENSE,Apache-2.0
github.com/goccy/go-json,https://github.com/goccy/go-json/blob/v0.10.2/LICENSE,MIT
github.com/google/flatbuffers/go,https://github.com/google/flatbuffers/blob/v23.5.26/LICENSE,Apache-2.0
github.com/google/s2a-go,https://github.com/google/s2a-go/blob/v0.1.9/LICENSE.md,Apache-2.0
github.com/google/uuid,https://github.com/google/uuid/blob/v1.6.0/LICENSE,BSD-3-Clause
github.com/googleapis/enterprise-certificate-proxy/client,https://github.com/googleapis/enterprise-certificate-proxy/blob/v0.3.6/LICENSE,Apache-2.0
github.com/googleapis/gax-go/v2,https://github.com/googleapis/gax-go/blob/v2.14.1/v2/LICENSE,BSD-3-Clause
github.com/klauspost/compress,https://github.com/klauspost/compress/blob/v1.16.7/LICENSE,Apache-2.0
github.com/klauspost/compress/internal/snapref,https://github.com/klauspost/compress/blob/v1.16.7/internal/snapref/LICENSE,BSD-3-Clause
github.com/klauspost/compress/zstd/internal/xxhash,https://github.com/klauspost/compress/blob/v1.16.7/zstd/internal/xxhash/LICENSE.txt,MIT
github.com/klauspost/cpuid/v2,https://github.com/klauspost/cpuid/blob/v2.2.5/LICENSE,MIT
github.com/pierrec/lz4/v4,https://github.com/pierrec/lz4/blob/v4.1.18/LICENSE,BSD-3-Clause
github.com/zeebo/xxh3,https://github.com/zeebo/xxh3/blob/v1.0.2/LICENSE,BSD-2-Clause
go.opencensus.io,https://github.com/census-instrumentation/opencensus-go/blob/v0.24.0/LICENSE,Apache-2.0
go.opentelemetry.io/auto/sdk,https://github.com/open-telemetry/opentelemetry-go-instrumentation/blob/sdk/v1.1.0/sdk/LICENSE,Apache-2.0
go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc,https://github.com/open-telemetry/opentelemetry-go-contrib/blob/instrumentation/google.golang.org/grpc/otelgrpc/v0.60.0/instrumentation/google.golang.org/grpc/otelgrpc/LICENSE,Apache-2.0
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp,https://github.com/open-telemetry/opentelemetry-go-contrib/blob/instrumentation/net/http/otelhttp/v0.60.0/instrumentation/net/http/otelhttp/LICENSE,Apache-2.0
go.opentelemetry.io/otel,https://github.com/open-telemetry/opentelemetry-go/blob/v1.35.0/LICENSE,Apache-2.0
go.opentelemetry.io/otel/metric,https://github.com/open-telemetry/opentelemetry-go/blob/metric/v1.35.0/metric/LICENSE,Apache-2.0
go.opentelemetry.io/otel/trace,https://github.com/open-telemetry/opentelemetry-go/blob/trace/v1.35.0/trace/LICENSE,Apache-2.0
go.uber.org/multierr,https://github.com/uber-go/multierr/blob/v1.10.0/LICENSE.txt,MIT
go.uber.org/zap,https://github.com/uber-go/zap/blob/v1.27.0/LICENSE,MIT
golang.org/x/crypto,https://cs.opensource.google/go/x/crypto/+/v0.37.0:LICENSE,BSD-3-Clause
golang.org/x/exp/constraints,https://cs.opensource.google/go/x/exp/+/8a7402ab:LICENSE,BSD-3-Clause
golang.org/x/net,https://cs.opensource.google/go/x/net/+/v0.39.0:LICENSE,BSD-3-Clause
golang.org/x/oauth2,https://cs.opensource.google/go/x/oauth2/+/v0.29.0:LICENSE,BSD-3-Clause
golang.org/x/sync,https://cs.opensource.google/go/x/sync/+/v0.14.0:LICENSE,BSD-3-Clause
golang.org/x/sys,https://cs.opensource.google/go/x/sys/+/v0.32.0:LICENSE,BSD-3-Clause
golang.org/x/text,https://cs.opensource.google/go/x/text/+/v0.24.0:LICENSE,BSD-3-Clause
golang.org/x/time/rate,https://cs.opensource.google/go/x/time/+/v0.11.0:LICENSE,BSD-3-Clause
golang.org/x/xerrors,https://cs.opensource.google/go/x/xerrors/+/7835f813:LICENSE,BSD-3-Clause
google.golang.org/api,https://github.com/googleapis/google-api-go-client/blob/v0.231.0/LICENSE,BSD-3-Clause
google.golang.org/api/internal/third_party/uritemplates,https://github.com/googleapis/google-api-go-client/blob/v0.231.0/internal/third_party/uritemplates/LICENSE,BSD-3-Clause
google.golang.org/genproto/googleapis/api,https://github.com/googleapis/go-genproto/blob/10db94c68c34/googleapis/api/LICENSE,Apache-2.0
google.golang.org/genproto/googleapis/rpc,https://github.com/googleapis/go-genproto/blob/10db94c68c34/googleapis/rpc/LICENSE,Apache-2.0
google.golang.org/genproto/googleapis/type/expr,https://github.com/googleapis/go-genproto/blob/a0af3efb3deb/LICENSE,Apache-2.0
google.golang.org/grpc,https://github.com/grpc/grpc-go/blob/v1.72.0/LICENSE,Apache-2.0
google.golang.org/protobuf,https://github.com/protocolbuffers/protobuf-go/blob/v1.36.6/LICENSE,BSD-3-Clause
synapse-its.com/shared/api/softwaregateway,Unknown,Unknown
synapse-its.com/shared/devices,Unknown,Unknown
synapse-its.com/shared/devices/edi/edicmu2212,Unknown,Unknown
synapse-its.com/shared/devices/edi/ediecl2010,Unknown,Unknown
synapse-its.com/shared/devices/edi/ediecl2018,Unknown,Unknown
synapse-its.com/shared/devices/edi/edikcl2018,Unknown,Unknown
synapse-its.com/shared/devices/edi/edimmu16le,Unknown,Unknown
synapse-its.com/shared/devices/edi/helper,Unknown,Unknown
synapse-its.com/shared/devices/helper,Unknown,Unknown
synapse-its.com/shared/logger,Unknown,Unknown
synapse-its.com/shared/pubsubdata,Unknown,Unknown
synapse-its.com/shared/schemas,Unknown,Unknown
