package app

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"golang.org/x/oauth2"
	"synapse-its.com/onramp/middlewares"
	"synapse-its.com/onramp/modules/auth"
	"synapse-its.com/shared/mocks"
	authDomain "synapse-its.com/shared/rest/domain/auth"
	domainMocks "synapse-its.com/shared/rest/domain/mocks"
)

// setupOIDCEnv sets up the OIDC environment variables for testing
func setupOIDCEnv(t *testing.T) {
	t.Setenv("SYNAPSE_OIDC_CLIENT_ID", "test-client-id")
	t.Setenv("SYNAPSE_OIDC_CLIENT_SECRET", "test-client-secret")
	t.Setenv("SYNAPSE_OIDC_CLIENT_CALLBACK_URL", "http://localhost:4200/callback")
	t.Setenv("SYNAPSE_OIDC_ISSUER_URL", "http://localhost:8091/auth/realms/test")
}

func newMockSessionStore() *domainMocks.MockSessionStore {
	return &domainMocks.MockSessionStore{}
}

func TestNewRouter_Routes(t *testing.T) {
	// TODO: This will need to be revisited as our routes become more complex.
	// Use the full app setup to ensure all routes are registered
	t.Setenv("WEB_CONTENT_DIR", "/microservices/onramp/web")
	app := NewApp(mocks.FakeConns(), &mocks.FakeBatcher{})
	router := app.Serve()

	tests := []struct {
		name      string
		method    string
		path      string
		wantCode  int
		checkBody func(t *testing.T, rr *httptest.ResponseRecorder)
	}{
		{
			name:     "unknown path -> 404",
			method:   http.MethodGet,
			path:     "/not-a-route",
			wantCode: http.StatusNotFound,
			checkBody: func(t *testing.T, rr *httptest.ResponseRecorder) {
				// No further body inspection needed for a 404.
			},
		},
		{
			name:     "/api/gateway handler",
			method:   http.MethodGet,
			path:     "/api/gateway",
			wantCode: 401,
		},
		{
			name:     "/api/gateway-config handler",
			method:   http.MethodGet,
			path:     "/api/gateway-config",
			wantCode: 401,
		},
		{
			name:     "/api/organizations handler",
			method:   http.MethodGet,
			path:     "/api/organizations",
			wantCode: 401,
		},
		{
			name:     "/api/devices handler",
			method:   http.MethodGet,
			path:     "/api/devices",
			wantCode: 401,
		},
		// Test REST API endpoints for organizations
		{
			name:     "POST /api/organizations",
			method:   http.MethodPost,
			path:     "/api/organizations",
			wantCode: 401, // Bad request due to missing request body
		},
		{
			name:     "GET /api/organizations",
			method:   http.MethodGet,
			path:     "/api/organizations",
			wantCode: 401,
		},
		{
			name:     "GET /api/organizations/{identifier}",
			method:   http.MethodGet,
			path:     "/api/organizations/test-org",
			wantCode: 401,
		},
		{
			name:     "PATCH /api/organizations/{identifier}",
			method:   http.MethodPatch,
			path:     "/api/organizations/test-org",
			wantCode: 401, // Bad request due to missing request body
		},
		{
			name:     "DELETE /api/organizations/{identifier}",
			method:   http.MethodDelete,
			path:     "/api/organizations/test-org",
			wantCode: 401,
		},
		// Test REST API endpoints for software gateways
		{
			name:     "POST /api/organization/{organizationId}/softwaregateway",
			method:   http.MethodPost,
			path:     "/api/organization/0d9cd286-a434-4400-a7aa-8d60cf849c61/softwaregateway",
			wantCode: 401, // Bad request due to missing request body
		},
		{
			name:     "GET /api/organization/{organizationId}/softwaregateway",
			method:   http.MethodGet,
			path:     "/api/organization/0d9cd286-a434-4400-a7aa-8d60cf849c61/softwaregateway",
			wantCode: 401,
		},
		{
			name:     "GET /api/organization/{organizationId}/softwaregateway/{identifier}",
			method:   http.MethodGet,
			path:     "/api/organization/0d9cd286-a434-4400-a7aa-8d60cf849c61/softwaregateway/f866ca63-7df8-432a-afe7-9f85252a0af3",
			wantCode: 401,
		},
		{
			name:     "PATCH /api/organization/{organizationId}/softwaregateway/{identifier}",
			method:   http.MethodPatch,
			path:     "/api/organization/0d9cd286-a434-4400-a7aa-8d60cf849c61/softwaregateway/f866ca63-7df8-432a-afe7-9f85252a0af3",
			wantCode: 401, // Bad request due to missing request body
		},
		{
			name:     "DELETE /api/organization/{organizationId}/softwaregateway/{identifier}",
			method:   http.MethodDelete,
			path:     "/api/organization/0d9cd286-a434-4400-a7aa-8d60cf849c61/softwaregateway/f866ca63-7df8-432a-afe7-9f85252a0af3",
			wantCode: 401,
		},
		// Test authentication endpoints
		{
			name:     "GET /login",
			method:   http.MethodGet,
			path:     "/login",
			wantCode: 302, // Redirect to OIDC provider
		},
		{
			name:     "GET /logout",
			method:   http.MethodGet,
			path:     "/logout",
			wantCode: 302, // Redirect after logout
		},
		{
			name:     "GET /callback",
			method:   http.MethodGet,
			path:     "/callback",
			wantCode: 400, // Bad request due to missing state
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest(tc.method, tc.path, nil)
			rr := httptest.NewRecorder()
			router.ServeHTTP(rr, req)

			assert.Equal(t, tc.wantCode, rr.Code, "status code for %s", tc.path)
			if tc.checkBody != nil {
				tc.checkBody(t, rr)
			}
		})
	}
}

func TestHandleLogin(t *testing.T) {
	// Setup test environment variables for OIDC
	setupOIDCEnv(t)

	sessionStore := newMockSessionStore()

	tests := []struct {
		name     string
		host     string
		wantCode int
	}{
		{
			name:     "localhost dev environment",
			host:     "localhost:4200",
			wantCode: http.StatusFound,
		},
		{
			name:     "production environment",
			host:     "example.com",
			wantCode: http.StatusFound,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/login", nil)
			req.Host = tc.host
			rr := httptest.NewRecorder()

			handler := auth.NewHandler(sessionStore, &domainMocks.MockAuthRepository{})
			handler.OAuth2Login(rr, req)

			assert.Equal(t, tc.wantCode, rr.Code)

			// Check that state cookie was set
			cookies := rr.Result().Cookies()
			var stateCookie *http.Cookie
			for _, cookie := range cookies {
				if cookie.Name == "oauth_state" {
					stateCookie = cookie
					break
				}
			}
			assert.NotNil(t, stateCookie, "oauth_state cookie should be set")
			assert.NotEmpty(t, stateCookie.Value, "oauth_state cookie should have a value")

			// Check redirect location contains expected parameters
			location := rr.Header().Get("Location")
			assert.Contains(t, location, "state=", "redirect should contain state parameter")
		})
	}
}

func TestHandleCallback(t *testing.T) {
	sessionStore := newMockSessionStore()

	tests := []struct {
		name     string
		host     string
		state    string
		code     string
		cookie   *http.Cookie
		wantCode int
	}{
		{
			name:     "missing state cookie",
			host:     "localhost:4200",
			state:    "test-state",
			code:     "test-code",
			cookie:   nil,
			wantCode: http.StatusBadRequest,
		},
		{
			name:     "invalid state",
			host:     "localhost:4200",
			state:    "wrong-state",
			code:     "test-code",
			cookie:   &http.Cookie{Name: "oauth_state", Value: "correct-state"},
			wantCode: http.StatusBadRequest,
		},
		{
			name:     "missing code parameter",
			host:     "localhost:4200",
			state:    "test-state",
			code:     "",
			cookie:   &http.Cookie{Name: "oauth_state", Value: "test-state"},
			wantCode: http.StatusInternalServerError, // OAuth2 exchange will fail
		},
		{
			name:     "production host with invalid code",
			host:     "example.com",
			state:    "test-state",
			code:     "invalid-code",
			cookie:   &http.Cookie{Name: "oauth_state", Value: "test-state"},
			wantCode: http.StatusInternalServerError, // OAuth2 exchange will fail
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/callback?state="+tc.state+"&code="+tc.code, nil)
			req.Host = tc.host
			if tc.cookie != nil {
				req.AddCookie(tc.cookie)
			}
			rr := httptest.NewRecorder()

			handler := auth.NewHandler(sessionStore, &domainMocks.MockAuthRepository{})
			handler.OAuth2Callback(rr, req)

			assert.Equal(t, tc.wantCode, rr.Code)
		})
	}
}

func TestHandleLogout(t *testing.T) {
	// Pre-populate session store
	sessionStore := newMockSessionStore()
	session := &authDomain.Session{
		UserID:          "test-user",
		OAuthToken:      authDomain.FromOAuth2Token(&oauth2.Token{}),
		UserPermissions: nil,
	}
	sessionStore.On("SetSession", "test-session-id", session).Return()
	sessionStore.On("GetSession", "test-session-id").Return(session, true)
	sessionStore.On("ClearSession", "test-session-id").Return()

	tests := []struct {
		name          string
		host          string
		sessionCookie *http.Cookie
		wantCode      int
	}{
		{
			name:          "logout with valid session - localhost",
			host:          "localhost:4200",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "test-session-id"},
			wantCode:      http.StatusFound,
		},
		{
			name:          "logout with valid session - production",
			host:          "example.com",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "test-session-id"},
			wantCode:      http.StatusFound,
		},
		{
			name:          "logout without session cookie",
			host:          "localhost:4200",
			sessionCookie: nil,
			wantCode:      http.StatusFound,
		},
		{
			name:          "logout with onramp host",
			host:          "onramp:4200",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "test-session-id"},
			wantCode:      http.StatusFound,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/logout", nil)
			req.Host = tc.host
			if tc.sessionCookie != nil {
				req.AddCookie(tc.sessionCookie)
			}
			rr := httptest.NewRecorder()

			handler := auth.NewHandler(sessionStore, &domainMocks.MockAuthRepository{})
			handler.OAuth2Logout(rr, req)

			assert.Equal(t, tc.wantCode, rr.Code)

			// Check that session cookie is cleared
			cookies := rr.Result().Cookies()
			var sessionCookie *http.Cookie
			for _, cookie := range cookies {
				if cookie.Name == "session_id" {
					sessionCookie = cookie
					break
				}
			}
			if sessionCookie != nil {
				assert.Empty(t, sessionCookie.Value, "session cookie should be cleared")
				assert.True(t, sessionCookie.MaxAge < 0, "session cookie should be expired")
			}
		})
	}
}

func TestAuthMiddleware(t *testing.T) {
	// Create a test handler that we'll protect
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		claims, ok := r.Context().Value(authDomain.UserKey{}).(map[string]interface{})
		if !ok {
			http.Error(w, "no claims in context", http.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(claims)
	})

	sessionStore := newMockSessionStore()

	// Set up mock expectations for all session IDs used in the tests
	session := &authDomain.Session{
		UserID: "test-user",
		OAuthToken: func() *authDomain.Token {
			token := &oauth2.Token{AccessToken: "test-access-token"}
			tokenWithExtra := token.WithExtra(map[string]interface{}{"id_token": "invalid-jwt-token"})
			return authDomain.FromOAuth2Token(tokenWithExtra)
		}(),
		UserPermissions: nil,
	}
	sessionStore.On("GetSession", "valid-session").Return(session, true)
	sessionStore.On("GetSession", "invalid-session").Return(nil, false)

	protectedHandler := middlewares.AuthMiddleware(sessionStore)(testHandler)

	tests := []struct {
		name          string
		host          string
		sessionCookie *http.Cookie
		wantCode      int
	}{
		{
			name:          "no session cookie",
			host:          "localhost:4200",
			sessionCookie: nil,
			wantCode:      http.StatusUnauthorized,
		},
		{
			name:          "invalid session id",
			host:          "localhost:4200",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "invalid-session"},
			wantCode:      http.StatusUnauthorized,
		},
		{
			name:          "valid session but invalid token",
			host:          "localhost:4200",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "valid-session"},
			wantCode:      http.StatusUnauthorized, // Token verification will fail
		},
		{
			name:          "production host with invalid session",
			host:          "example.com",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "invalid-session"},
			wantCode:      http.StatusUnauthorized,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/protected/test", nil)
			req.Host = tc.host
			if tc.sessionCookie != nil {
				req.AddCookie(tc.sessionCookie)
			}
			rr := httptest.NewRecorder()

			protectedHandler.ServeHTTP(rr, req)

			assert.Equal(t, tc.wantCode, rr.Code)
		})
	}
}

func TestSuccessfulAuthFlow(t *testing.T) {
	// Test the successful authentication middleware flow
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		claims, ok := r.Context().Value(authDomain.UserKey{}).(map[string]interface{})
		if !ok {
			http.Error(w, "no claims in context", http.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(claims)
	})

	// Create a mock middleware that simulates successful authentication
	mockAuthMiddleware := func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Simulate successful token verification and claims extraction
			claims := map[string]interface{}{
				"sub":   "user123",
				"name":  "Test User",
				"email": "<EMAIL>",
			}
			next.ServeHTTP(w, r.WithContext(context.WithValue(r.Context(), authDomain.UserKey{}, claims)))
		})
	}

	protectedHandler := mockAuthMiddleware(testHandler)

	req := httptest.NewRequest("GET", "/protected/test", nil)
	req.Host = "localhost:4200"
	rr := httptest.NewRecorder()

	protectedHandler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)
	assert.Equal(t, "application/json", rr.Header().Get("Content-Type"))

	var claims map[string]interface{}
	err := json.Unmarshal(rr.Body.Bytes(), &claims)
	assert.NoError(t, err)
	assert.Equal(t, "user123", claims["sub"])
	assert.Equal(t, "Test User", claims["name"])
	assert.Equal(t, "<EMAIL>", claims["email"])
}

func TestSuccessfulCallbackFlow(t *testing.T) {
	// Test the successful OAuth callback flow
	mockCallbackHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Simulate successful callback processing
		isDev := strings.HasPrefix(r.Host, "localhost:4200")

		// Verify state cookie (simulate success)
		st, err := r.Cookie("oauth_state")
		if err != nil || r.URL.Query().Get("state") != st.Value {
			http.Error(w, "invalid state", http.StatusBadRequest)
			return
		}

		// Delete the state cookie (simulate the actual callback behavior)
		http.SetCookie(w, &http.Cookie{
			Name:     "oauth_state",
			Value:    "",
			Path:     "/",
			HttpOnly: true,
			Secure:   !isDev,
			Expires:  time.Unix(0, 0),
			MaxAge:   -1,
			SameSite: http.SameSiteLaxMode,
		})

		// Simulate successful token exchange and session creation
		sessionID := "test-session-id"
		sessionStore := newMockSessionStore()
		session := &authDomain.Session{
			UserID:          "test-user",
			OAuthToken:      authDomain.FromOAuth2Token(&oauth2.Token{AccessToken: "test-token"}),
			UserPermissions: nil,
		}
		sessionStore.On("SetSession", sessionID, session).Return()
		sessionStore.On("GetSession", sessionID).Return(session, true)

		http.SetCookie(w, &http.Cookie{
			Name:     "session_id",
			Value:    sessionID,
			Path:     "/",
			HttpOnly: true,
			Secure:   !(isDev || strings.HasPrefix(r.Host, "onramp:4200")),
			SameSite: http.SameSiteStrictMode,
		})

		http.Redirect(w, r, "/", http.StatusFound)
	})

	tests := []struct {
		name     string
		host     string
		state    string
		code     string
		cookie   *http.Cookie
		wantCode int
	}{
		{
			name:     "successful callback flow - localhost",
			host:     "localhost:4200",
			state:    "test-state",
			code:     "valid-code",
			cookie:   &http.Cookie{Name: "oauth_state", Value: "test-state"},
			wantCode: http.StatusFound,
		},
		{
			name:     "successful callback flow - production",
			host:     "example.com",
			state:    "test-state",
			code:     "valid-code",
			cookie:   &http.Cookie{Name: "oauth_state", Value: "test-state"},
			wantCode: http.StatusFound,
		},
		{
			name:     "successful callback flow - onramp host",
			host:     "onramp:4200",
			state:    "test-state",
			code:     "valid-code",
			cookie:   &http.Cookie{Name: "oauth_state", Value: "test-state"},
			wantCode: http.StatusFound,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/callback?state="+tc.state+"&code="+tc.code, nil)
			req.Host = tc.host
			if tc.cookie != nil {
				req.AddCookie(tc.cookie)
			}
			rr := httptest.NewRecorder()

			mockCallbackHandler.ServeHTTP(rr, req)

			assert.Equal(t, tc.wantCode, rr.Code)

			if tc.wantCode == http.StatusFound {
				// Check that session cookie was set
				cookies := rr.Result().Cookies()
				var sessionCookie *http.Cookie
				for _, cookie := range cookies {
					if cookie.Name == "session_id" {
						sessionCookie = cookie
						break
					}
				}
				assert.NotNil(t, sessionCookie)
				assert.Equal(t, "test-session-id", sessionCookie.Value)

				// Check redirect location
				location := rr.Header().Get("Location")
				assert.Equal(t, "/", location, "should redirect to root")
			}
		})
	}
}

func TestNewRouter_FileServer(t *testing.T) {
	router := NewRouter(mocks.FakeConns(), &mocks.FakeBatcher{})

	tests := []struct {
		name     string
		path     string
		wantCode int
	}{
		{
			name:     "static file access",
			path:     "/static/test.txt",
			wantCode: http.StatusNotFound, // File doesn't exist in test
		},
		{
			name:     "root path serves index.html",
			path:     "/",
			wantCode: http.StatusOK, // Should serve index.html
		},
		{
			name:     "favicon request",
			path:     "/favicon.ico",
			wantCode: http.StatusNotFound, // File doesn't exist in test
		},
		{
			name:     "css file request",
			path:     "/styles.less",
			wantCode: http.StatusNotFound, // File doesn't exist in test
		},
		{
			name:     "js file request",
			path:     "/app.js",
			wantCode: http.StatusNotFound, // File doesn't exist in test
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", tc.path, nil)
			req.Host = "localhost:4200"
			rr := httptest.NewRecorder()

			router.ServeHTTP(rr, req)

			// We expect either OK (file found) or 404 (file not found)
			assert.True(t, rr.Code == http.StatusOK || rr.Code == http.StatusNotFound,
				"Expected status OK or NotFound, got %d", rr.Code)
		})
	}
}

func TestOIDCConfig_Struct(t *testing.T) {
	// Test the OIDCConfig struct to ensure it's properly defined
	config := auth.OIDCConfig{
		ClientID:     "test-client-id",
		ClientSecret: "test-client-secret",
		RedirectURL:  "http://localhost:4200/callback",
		IssuerURL:    "http://localhost:8091/auth/realms/test",
		Scope:        "openid profile email",
	}

	assert.Equal(t, "test-client-id", config.ClientID)
	assert.Equal(t, "test-client-secret", config.ClientSecret)
	assert.Equal(t, "http://localhost:4200/callback", config.RedirectURL)
	assert.Equal(t, "http://localhost:8091/auth/realms/test", config.IssuerURL)
	assert.Equal(t, "openid profile email", config.Scope)
}

func TestInitFunction_SuccessPath(t *testing.T) {
	// Test the success path of the init function by temporarily setting up
	// environment variables and testing OIDC configuration
	setupOIDCEnv(t)

	// Test that the OIDC configuration is properly initialized
	config := auth.OIDCConfig{
		ClientID:     os.Getenv("SYNAPSE_OIDC_CLIENT_ID"),
		ClientSecret: os.Getenv("SYNAPSE_OIDC_CLIENT_SECRET"),
		RedirectURL:  os.Getenv("SYNAPSE_OIDC_CLIENT_CALLBACK_URL"),
		IssuerURL:    os.Getenv("SYNAPSE_OIDC_ISSUER_URL"),
	}

	assert.Equal(t, "test-client-id", config.ClientID)
	assert.Equal(t, "test-client-secret", config.ClientSecret)
	assert.Equal(t, "http://localhost:4200/callback", config.RedirectURL)
	assert.Equal(t, "http://localhost:8091/auth/realms/test", config.IssuerURL)
}

func TestHandleCallback_SuccessfulFlow(t *testing.T) {
	// Create a mock handler that simulates the successful callback flow
	// without requiring a real OIDC provider
	mockSuccessfulCallback := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		isDev := strings.HasPrefix(r.Host, "localhost:4200")

		// Simulate state verification (success case)
		st, err := r.Cookie("oauth_state")
		if err != nil || r.URL.Query().Get("state") != st.Value {
			http.Error(w, "invalid state", http.StatusBadRequest)
			return
		}

		// Delete the state cookie
		http.SetCookie(w, &http.Cookie{
			Name:     "oauth_state",
			Value:    "",
			Path:     "/",
			HttpOnly: true,
			Secure:   !isDev,
			Expires:  time.Unix(0, 0),
			MaxAge:   -1,
			SameSite: http.SameSiteLaxMode,
		})

		// Simulate successful token exchange
		code := r.URL.Query().Get("code")
		if code == "" {
			http.Error(w, "missing code", http.StatusBadRequest)
			return
		}

		// Create a mock token with ID token
		token := &oauth2.Token{
			AccessToken: "mock-access-token",
			TokenType:   "Bearer",
		}
		token = token.WithExtra(map[string]interface{}{
			"id_token": "mock.jwt.token",
		})

		// Simulate successful session creation
		sessionID := "mock-session-id"
		sessionStore := newMockSessionStore()
		session := &authDomain.Session{
			UserID:          "test-user",
			OAuthToken:      authDomain.FromOAuth2Token(token),
			UserPermissions: nil,
		}
		sessionStore.On("SetSession", sessionID, session).Return()
		sessionStore.On("GetSession", sessionID).Return(session, true)

		http.SetCookie(w, &http.Cookie{
			Name:     "session_id",
			Value:    sessionID,
			Path:     "/",
			HttpOnly: true,
			Secure:   !(isDev || strings.HasPrefix(r.Host, "onramp:4200")),
			SameSite: http.SameSiteStrictMode,
		})

		http.Redirect(w, r, "/", http.StatusFound)
	})

	tests := []struct {
		name   string
		host   string
		state  string
		code   string
		cookie *http.Cookie
	}{
		{
			name:   "successful callback localhost",
			host:   "localhost:4200",
			state:  "valid-state",
			code:   "valid-code",
			cookie: &http.Cookie{Name: "oauth_state", Value: "valid-state"},
		},
		{
			name:   "successful callback production",
			host:   "example.com",
			state:  "valid-state",
			code:   "valid-code",
			cookie: &http.Cookie{Name: "oauth_state", Value: "valid-state"},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/callback?state="+tc.state+"&code="+tc.code, nil)
			req.Host = tc.host
			if tc.cookie != nil {
				req.AddCookie(tc.cookie)
			}
			rr := httptest.NewRecorder()

			mockSuccessfulCallback.ServeHTTP(rr, req)

			assert.Equal(t, http.StatusFound, rr.Code)
			assert.Equal(t, "/", rr.Header().Get("Location"))

			// Verify session cookie was set
			cookies := rr.Result().Cookies()
			var sessionCookie *http.Cookie
			for _, cookie := range cookies {
				if cookie.Name == "session_id" {
					sessionCookie = cookie
					break
				}
			}
			assert.NotNil(t, sessionCookie)
			assert.Equal(t, "mock-session-id", sessionCookie.Value)
		})
	}
}

func TestAuthMiddleware_SuccessfulFlow(t *testing.T) {
	// Create a mock successful authentication middleware
	sessionStore := newMockSessionStore()
	mockSuccessfulAuth := func(sessionStore *domainMocks.MockSessionStore) func(http.Handler) http.Handler {
		return func(next http.Handler) http.Handler {
			return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// Simulate successful session retrieval
				c, err := r.Cookie("session_id")
				if err != nil {
					http.Error(w, "unauthorized", http.StatusUnauthorized)
					return
				}

				// Check if session exists
				tok, ok := sessionStore.GetSession(c.Value)
				if !ok {
					http.Error(w, "invalid session", http.StatusUnauthorized)
					return
				}

				// Simulate successful token verification and claims extraction
				if tok != nil && tok.OAuthToken != nil && tok.OAuthToken.AccessToken == "mock-access-token" {
					claims := map[string]interface{}{
						"sub":   "user123",
						"name":  "Test User",
						"email": "<EMAIL>",
						"iss":   "http://localhost:8091/auth/realms/test",
						"aud":   "test-client-id",
						"exp":   time.Now().Add(time.Hour).Unix(),
						"iat":   time.Now().Unix(),
					}
					next.ServeHTTP(w, r.WithContext(context.WithValue(r.Context(), authDomain.UserKey{}, claims)))
				} else {
					http.Error(w, "invalid token", http.StatusUnauthorized)
				}
			})
		}
	}

	// Create a test handler that uses the claims
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		claims, ok := r.Context().Value(authDomain.UserKey{}).(map[string]interface{})
		if !ok {
			http.Error(w, "no claims", http.StatusUnauthorized)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(claims)
	})

	// Set up a mock session
	session := &authDomain.Session{
		UserID:          "test-user",
		OAuthToken:      authDomain.FromOAuth2Token(&oauth2.Token{AccessToken: "mock-access-token"}),
		UserPermissions: nil,
	}
	sessionStore.On("SetSession", "test-session", session).Return()
	sessionStore.On("GetSession", "test-session").Return(session, true)

	protectedHandler := mockSuccessfulAuth(sessionStore)(testHandler)

	req := httptest.NewRequest("GET", "/protected/test", nil)
	req.Host = "localhost:4200"
	req.AddCookie(&http.Cookie{Name: "session_id", Value: "test-session"})
	rr := httptest.NewRecorder()

	protectedHandler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)

	var claims map[string]interface{}
	err := json.Unmarshal(rr.Body.Bytes(), &claims)
	assert.NoError(t, err)
	assert.Equal(t, "user123", claims["sub"])
	assert.Equal(t, "Test User", claims["name"])
	assert.Equal(t, "<EMAIL>", claims["email"])
}

func TestApp_Integration_CompleteFlow(t *testing.T) {
	// Test the complete integration flow
	app := NewApp(mocks.FakeConns(), &mocks.FakeBatcher{})
	router := app.Serve()

	// Test that all components work together
	tests := []struct {
		name     string
		method   string
		path     string
		expected int
	}{
		{"api gateway", "GET", "/api/gateway", http.StatusUnauthorized},
		{"api gateway config", "GET", "/api/gateway-config", http.StatusUnauthorized},
		{"api devices", "GET", "/api/devices", http.StatusUnauthorized},
		{"auth login", "GET", "/login", http.StatusFound},
		{"auth logout", "GET", "/logout", http.StatusFound},
		{"org list", "GET", "/api/organizations", http.StatusUnauthorized},
		{"sgw list", "GET", "/api/softwaregateway", http.StatusUnauthorized},
		{"file server", "GET", "/", http.StatusUnauthorized},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest(tc.method, tc.path, nil)
			rr := httptest.NewRecorder()

			router.ServeHTTP(rr, req)

			assert.True(t, rr.Code == tc.expected || rr.Code == http.StatusNotFound || rr.Code == http.StatusMethodNotAllowed,
				"Expected status %d for %s %s, got %d",
				tc.expected, tc.method, tc.path, rr.Code)
		})
	}
}
