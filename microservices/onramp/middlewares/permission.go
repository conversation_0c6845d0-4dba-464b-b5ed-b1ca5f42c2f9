package middlewares

import (
	"net/http"

	"github.com/gorilla/mux"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/api/response"
	authDomain "synapse-its.com/shared/rest/domain/auth"
	onramphelper "synapse-its.com/shared/rest/onramp/helper"
)

type PermissionRule struct {
	Permissions []string
	ScopedToOrg bool
	requireAll  bool
}

var EndpointPermissions = map[string]map[string][]PermissionRule{
	// device/handler
	"/api/organizations/{organizationId}/device": {
		"GET": {
			{Permissions: []string{"org_view_devices", "device_group_view_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_view_organizations"}, ScopedToOrg: false},
		},
		"POST": {
			{Permissions: []string{"org_manage_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},
	"/api/organizations/{organizationId}/device/{deviceId}": {
		"PATCH": {
			{Permissions: []string{"org_manage_devices", "device_group_manage_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"DELETE": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	// dashboard/handler
	"/api/organizations/{organizationId}/dashboard/devices": {
		"GET": {
			{Permissions: []string{"org_view_devices", "device_group_view_devices", "location_group_view_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_view_organizations"}, ScopedToOrg: false},
		},
	},

	// organization/handler
	"/api/organizations": {
		"POST": {
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}": {
		"PATCH": {
			{Permissions: []string{"org_manage_settings"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"DELETE": {
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/roles": {
		"GET": {
			{Permissions: []string{"org_view_users"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_view_organization_users"}, ScopedToOrg: false},
		},
		"POST": {
			{Permissions: []string{"org_manage_users"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organization_users"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/roles/{roleId}": {
		"DELETE": {
			{Permissions: []string{"org_manage_users"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organization_users"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/role-templates": {
		"GET": {
			{Permissions: []string{"org_view_users "}, ScopedToOrg: true},
			{Permissions: []string{"synapse_view_organization_users"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/permissions": {
		"GET": {
			{Permissions: []string{"org_view_users"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_view_organization_users"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/permissions/{permissionId}/roles/{roleId}": {
		"POST": {
			{Permissions: []string{"org_manage_users"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organization_users"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/invites": {
		"GET": {
			{Permissions: []string{"org_view_users"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_view_organization_users"}, ScopedToOrg: false},
		},
		"POST": {
			{Permissions: []string{"org_manage_users"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organization_users"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/invites/{inviteId}": {
		"DELETE": {
			{Permissions: []string{"org_manage_users"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organization_users"}, ScopedToOrg: false},
		},
		"POST": {
			{Permissions: []string{"org_manage_users"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organization_users"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/devicegroups": {
		"GET": {
			{Permissions: []string{"org_manage_device_groups"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"POST": {
			{Permissions: []string{"org_manage_device_groups"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/devicegroups/{devicegroupId}": {
		"PATCH": {
			{Permissions: []string{"org_manage_device_groups"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"DELETE": {
			{Permissions: []string{"org_manage_device_groups"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/devicegroups/{deviceGroupId}/users": {
		"GET": {
			{Permissions: []string{"org_manage_device_groups", "org_manage_users"}, ScopedToOrg: true, requireAll: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"POST": {
			{Permissions: []string{"org_manage_device_groups", "org_manage_users"}, ScopedToOrg: true, requireAll: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},
	"/api/organizations/{organizationId}/devicegroups/{deviceGroupId}/users/{userId}": {
		"PATCH": {
			{Permissions: []string{"org_manage_device_groups", "org_manage_users"}, ScopedToOrg: true, requireAll: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"DELETE": {
			{Permissions: []string{"org_manage_device_groups", "org_manage_users"}, ScopedToOrg: true, requireAll: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/locations": {
		"GET": {
			{Permissions: []string{"org_view_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_view_organizations"}, ScopedToOrg: false},
		},
		"POST": {
			{Permissions: []string{"org_manage_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/locations/{locationId}": {
		"PATCH": {
			{Permissions: []string{"org_manage_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"DELETE": {
			{Permissions: []string{"org_manage_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/users": {
		"GET": {
			{Permissions: []string{"org_view_users"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_view_organization_users"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/users/{userId}": {
		"PATCH": {
			{Permissions: []string{"org_manage_users"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organization_users"}, ScopedToOrg: false},
		},
		"DELETE": {
			{Permissions: []string{"org_manage_users"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organization_users"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/aps-factory-reset": {
		"POST": {
			{Permissions: []string{"org_aps_factory_reset"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_aps_factory_reset"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/locationgroups": {
		"GET": {
			{Permissions: []string{"org_manage_device_groups"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"POST": {
			{Permissions: []string{"org_manage_device_groups"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/locationgroups/{locationgroupId}": {
		"PATCH": {
			{Permissions: []string{"org_manage_device_groups"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"DELETE": {
			{Permissions: []string{"org_manage_device_groups"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/locationgroups/{locationgroupId}/users": {
		"POST": {
			{Permissions: []string{"org_manage_device_groups", "org_manage_users"}, ScopedToOrg: true, requireAll: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"GET": {
			{Permissions: []string{"org_manage_device_groups", "org_manage_users"}, ScopedToOrg: true, requireAll: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organizations/{organizationId}/locationgroups/{locationgroupId}/users/{userId}": {
		"PATCH": {
			{Permissions: []string{"org_manage_device_groups", "org_manage_users"}, ScopedToOrg: true, requireAll: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"DELETE": {
			{Permissions: []string{"org_manage_device_groups", "org_manage_users"}, ScopedToOrg: true, requireAll: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	// softwaregateway/handler
	"/api/softwaregateway/{identifier}/config": {
		"GET": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"PATCH": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organization/{organizationId}/softwaregateway": {
		"GET": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"POST": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organization/{organizationId}/softwaregateway/{identifier}": {
		"GET": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"PATCH": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"DELETE": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organization/{organizationId}/softwaregateway/{identifier}/config": {
		"GET": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"PATCH": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organization/{organizationId}/softwaregatewayconfig/templates": {
		"GET": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"POST": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}": {
		"GET": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"PATCH": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"DELETE": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings": {
		"GET": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"POST": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"PUT": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings/{setting}": {
		"DELETE": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organization/{organizationId}/softwaregateway/{identifier}/config/overrides": {
		"GET": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"POST": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
		"PUT": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organization/{organizationId}/softwaregateway/{identifier}/config/overrides/bulk": {
		"POST": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	"/api/organization/{organizationId}/softwaregateway/{identifier}/config/overrides/{setting}": {
		"DELETE": {
			{Permissions: []string{"org_delete_devices"}, ScopedToOrg: true},
			{Permissions: []string{"synapse_manage_organizations"}, ScopedToOrg: false},
		},
	},

	// softwaregateway/handler basesettings with new permission synapse_manage_gateway_settings
	"/api/softwaregatewayconfig/basesettings": {
		"GET": {
			{Permissions: []string{"synapse_manage_gateway_settings"}, ScopedToOrg: false},
		},
		"POST": {
			{Permissions: []string{"synapse_manage_gateway_settings"}, ScopedToOrg: false},
		},
	},

	"/api/softwaregatewayconfig/basesettings/{setting}": {
		"GET": {
			{Permissions: []string{"synapse_manage_gateway_settings"}, ScopedToOrg: false},
		},
		"PATCH": {
			{Permissions: []string{"synapse_manage_gateway_settings"}, ScopedToOrg: false},
		},
		"DELETE": {
			{Permissions: []string{"synapse_manage_gateway_settings"}, ScopedToOrg: false},
		},
	},
}

func PermissionMiddleware(sessionStore authDomain.SessionStore) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			c, err := r.Cookie("session_id")
			if err != nil {
				response.CreateUnauthorizedResponse(w)
				return
			}

			sessionData, ok := sessionStore.GetSession(c.Value)

			if !ok {
				response.CreateUnauthorizedResponse(w)
				return
			}

			// Get route template
			route := mux.CurrentRoute(r)
			pathTemplate, _ := route.GetPathTemplate()
			method := r.Method

			// Find rules
			rules, ok := EndpointPermissions[pathTemplate][method]

			if !ok {
				// no rule defined, if don't require permission, allow access
				next.ServeHTTP(w, r)
				return
			}

			// vars := mux.Vars(r)
			// orgID := vars["organizationId"]

			organizationId, err := onramphelper.ValidateOrganizationID(r)

			authorized := false
			userPermissions := sessionData.UserPermissions

			// Handle nil UserPermissions
			if userPermissions == nil {
				response.CreateForbiddenResponse(w)
				return
			}

			authorizerPermissions := (*authorizer.UserPermissions)(userPermissions)

			for _, rule := range rules {
				if rule.ScopedToOrg {
					if rule.requireAll {
						authorized = true
						for _, perm := range rule.Permissions {
							if !authorizerPermissions.HasPermissionsByOrganizationID(organizationId.String(), perm) {
								authorized = false
								break
							}
						}
					} else {
						for _, perm := range rule.Permissions {
							if authorizerPermissions.HasPermissionsByOrganizationID(organizationId.String(), perm) {
								authorized = true
								break
							}
						}
					}
				} else {
					if authorizerPermissions.HasPermission(rule.Permissions[0]) {
						authorized = true
						break
					}
				}
			}

			if !authorized {
				response.CreateForbiddenResponse(w)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}
