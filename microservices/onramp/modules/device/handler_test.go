package device

import (
	"net/http"
	"testing"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
)

func TestNewHandler(t *testing.T) {
	t.<PERSON>()

	handler1 := NewHandler()
	handler2 := NewHandler()

	assert.NotNil(t, handler1)
	assert.IsType(t, &Hand<PERSON>{}, handler1)
	assert.NotSame(t, handler1, handler2)
}

func TestHandler_RegisterRoutes(t *testing.T) {
	t.Run("registration", func(t *testing.T) {
		t.<PERSON>llel()

		handler := NewHandler()
		router := mux.NewRouter()
		handler.RegisterRoutes(router)

		routeMethodMap := make(map[string][]string)
		totalRoutes := 0
		router.Walk(func(route *mux.Route, _ *mux.Router, _ []*mux.Route) error {
			template, _ := route.GetPathTemplate()
			methods, _ := route.GetMethods()
			if len(methods) > 0 {
				routeMethodMap[template] = append(routeMethodMap[template], methods[0])
			}
			totalRoutes++
			return nil
		})

		assert.Equal(t, 5, totalRoutes)
		assert.Len(t, routeMethodMap, 3)

		expected := map[string][]string{
			"/organizations/{organizationId}/device":            {http.MethodPost, http.MethodGet},
			"/organizations/{organizationId}/device/{deviceId}": {http.MethodPatch, http.MethodDelete},
		}

		for path, methods := range expected {
			for _, method := range methods {
				assert.Contains(t, routeMethodMap[path], method)
			}
		}
	})

	t.Run("nil_router", func(t *testing.T) {
		t.Parallel()
		assert.Panics(t, func() { NewHandler().RegisterRoutes(nil) })
	})
}
