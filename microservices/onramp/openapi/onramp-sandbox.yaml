components: {}
definitions:
    app.UserProfile:
        properties:
            email:
                type: string
            name:
                type: string
        type: object
    config.GatewayConfigTemplate:
        properties:
            description:
                type: string
            id:
                type: string
            isDeleted:
                type: boolean
            name:
                type: string
            organizationId:
                type: string
        type: object
    config.GatewayConfigTemplateBaseSetting:
        properties:
            defaultValue:
                type: string
            description:
                type: string
            format:
                description: JSONB stored as string
                type: string
            isDeleted:
                type: boolean
            name:
                type: string
            setting:
                type: string
        type: object
    config.GatewayConfigTemplateSetting:
        properties:
            gatewayConfigTemplateId:
                type: string
            setting:
                type: string
            value:
                type: string
        type: object
    config.GatewayConfigTemplateSettingOverride:
        properties:
            setting:
                type: string
            softwareGatewayId:
                type: string
            value:
                type: string
        type: object
    config.SoftwareGateway:
        properties:
            config:
                type: string
            description:
                type: string
            id:
                type: string
            name:
                type: string
            templateId:
                type: string
        type: object
    dashboard.DashboardResponse:
        properties:
            locations:
                description: List of locations in the organization
                items:
                    $ref: '#/definitions/dashboard.LocationInfo'
                type: array
            organizationId:
                description: Organization ID
                type: string
        type: object
    dashboard.DeviceState:
        enum:
            - nofault
            - faulted
            - error
            - nevercomm
        type: string
        x-enum-varnames:
            - DeviceStateNoFault
            - DeviceStateFaulted
            - DeviceStateError
            - DeviceStateNeverComm
    dashboard.LocationInfo:
        properties:
            devices:
                description: List of devices with their status
                items:
                    $ref: '#/definitions/dashboard.dataPayload'
                type: array
            id:
                description: Location UUID
                example: 550e8400-e29b-41d4-a716-************
                type: string
            lat:
                description: Location latitude
                example: "40.7128"
                type: string
            long:
                description: Location longitude
                example: "-74.0060"
                type: string
            name:
                description: Location name
                example: Main Street Intersection
                type: string
        type: object
    dashboard.dataPayload:
        properties:
            device_identifier:
                description: Device UUID or identifier string
                example: device-uuid-123
                type: string
            status:
                allOf:
                    - $ref: '#/definitions/dashboard.status'
                description: Current device status and fault information
        type: object
    dashboard.status:
        properties:
            state:
                allOf:
                    - $ref: '#/definitions/dashboard.DeviceState'
                description: Current device state
                example: nofault
        type: object
    device.DeviceResponse:
        properties:
            createdAt:
                type: string
            description:
                type: string
            enableRealtime:
                type: boolean
            flushConnectionMs:
                type: integer
            id:
                type: string
            ipAddress:
                type: string
            isEnabled:
                type: boolean
            locationId:
                type: string
            name:
                type: string
            organizationId:
                type: string
            origId:
                type: integer
            port:
                type: integer
            serialNumber:
                type: string
            softwareGatewayId:
                type: string
            type:
                type: string
            updatedAt:
                type: string
        type: object
    device.DevicesResponse:
        properties:
            devices:
                items:
                    $ref: '#/definitions/device.DeviceResponse'
                type: array
        type: object
    devicegroups.DeviceGroups:
        properties:
            createdat:
                type: string
            id:
                type: string
            name:
                type: string
            organizationid:
                type: string
            updatedat:
                type: string
        type: object
    devicegroupusers.CreateDeviceGroupRoleAssignmentRequest:
        properties:
            roleIds:
                items:
                    type: string
                minItems: 1
                type: array
            userId:
                type: string
        required:
            - roleIds
            - userId
        type: object
    devicegroupusers.DeviceGroupRoleAssignment:
        properties:
            createdAt:
                type: string
            deviceGroupId:
                type: string
            membershipId:
                type: string
            roleId:
                type: string
            updatedAt:
                type: string
        type: object
    devicegroupusers.DeviceGroupRoleAssignmentResponse:
        properties:
            deviceGroupRoleAssignment:
                items:
                    $ref: '#/definitions/devicegroupusers.DeviceGroupRoleAssignment'
                type: array
        type: object
    devicegroupusers.DeviceGroupUser:
        properties:
            email:
                type: string
            firstName:
                type: string
            lastName:
                type: string
            roleIds:
                items:
                    type: string
                type: array
            userId:
                type: string
            userName:
                type: string
        type: object
    devicegroupusers.DeviceGroupUsersResponse:
        properties:
            users:
                items:
                    $ref: '#/definitions/devicegroupusers.DeviceGroupUser'
                type: array
        type: object
    devicegroupusers.UpdateDeviceGroupRoleAssignmentRequest:
        properties:
            roleIds:
                items:
                    type: string
                minItems: 1
                type: array
        required:
            - roleIds
        type: object
    emailverification.RequestVerificationRequest:
        properties:
            expiryMinutes:
                type: integer
        type: object
    emailverification.VerificationResponse:
        properties:
            authMethodId:
                type: string
            created:
                type: string
            expired:
                type: string
            id:
                type: string
            retried:
                type: string
            retryCount:
                type: integer
            sent:
                type: string
            status:
                type: string
            updated:
                type: string
        type: object
    forgotpassword.ForgotPasswordRedeem:
        properties:
            confirmNewPassword:
                maxLength: 128
                minLength: 8
                type: string
            newPassword:
                maxLength: 128
                minLength: 8
                type: string
            token:
                maxLength: 64
                minLength: 32
                type: string
        required:
            - confirmNewPassword
            - newPassword
            - token
        type: object
    forgotpassword.ForgotPasswordRequest:
        properties:
            username:
                maxLength: 100
                minLength: 3
                type: string
        required:
            - username
        type: object
    invites.InviteResponse:
        properties:
            created:
                type: string
            customroleid:
                type: string
            email:
                type: string
            expired:
                type: string
            id:
                type: string
            inviterid:
                type: string
            message:
                type: string
            organizationidentifier:
                type: string
            requiresso:
                type: boolean
            retried:
                type: string
            retrycount:
                type: integer
            sent:
                type: string
            status:
                type: string
            updated:
                type: string
        type: object
    locationgrouproleassignments.LocationGroupRoleAssignment:
        properties:
            createdAt:
                type: string
            locationGroupId:
                type: string
            membershipId:
                type: string
            roleId:
                type: string
            updatedAt:
                type: string
        type: object
    locationgrouproleassignments.LocationGroupRoleAssignmentResponse:
        properties:
            locationGroupRoleAssignment:
                items:
                    $ref: '#/definitions/locationgrouproleassignments.LocationGroupRoleAssignment'
                type: array
        type: object
    locationgrouproleassignments.LocationGroupUser:
        properties:
            email:
                type: string
            firstName:
                type: string
            lastName:
                type: string
            roleIds:
                items:
                    type: string
                type: array
            userId:
                type: string
            userName:
                type: string
        type: object
    locationgrouproleassignments.LocationGroupUsersResponse:
        properties:
            users:
                items:
                    $ref: '#/definitions/locationgrouproleassignments.LocationGroupUser'
                type: array
        type: object
    locationgroups.LocationGroupResponse:
        properties:
            createdat:
                type: string
            id:
                type: string
            name:
                type: string
            organizationid:
                type: string
            updatedat:
                type: string
        type: object
    locationgroups.LocationGroupsResponse:
        properties:
            locationgroups:
                items:
                    $ref: '#/definitions/locationgroups.LocationGroupResponse'
                type: array
        type: object
    locations.Location:
        properties:
            createdat:
                type: string
            description:
                type: string
            id:
                type: string
            latitude:
                type: number
            longitude:
                type: number
            name:
                type: string
            organizationid:
                type: string
            updatedat:
                type: string
        type: object
    organization.OrganizationResponse:
        properties:
            createdat:
                type: string
            description:
                type: string
            id:
                type: string
            name:
                type: string
            orgtypeidentifier:
                type: string
            updatedat:
                type: string
        type: object
    permissions.PermissionGroupResponse:
        properties:
            description:
                type: string
            name:
                type: string
            roles:
                items:
                    $ref: '#/definitions/permissions.RolePermissions'
                type: array
            scope:
                type: string
            weight:
                type: number
        type: object
    permissions.PermissionValue:
        properties:
            default_value:
                type: boolean
            inherited:
                type: boolean
            machine_name:
                type: string
            name:
                type: string
            value:
                type: boolean
            weight:
                type: number
        type: object
    permissions.PermissionsResponse:
        properties:
            permissions:
                items:
                    $ref: '#/definitions/permissions.PermissionGroupResponse'
                type: array
        type: object
    permissions.RolePermissions:
        properties:
            description:
                type: string
            name:
                type: string
            permissions:
                items:
                    $ref: '#/definitions/permissions.PermissionValue'
                type: array
        type: object
    profile.updateProfileRequest:
        properties:
            description:
                example: Updated description
                type: string
            firstName:
                example: John
                type: string
            lastName:
                example: Doe
                type: string
            mobile:
                example: "+1234567890"
                type: string
            notificationSmsEnabled:
                example: true
                type: boolean
        type: object
    response.BadRequestResponse:
        description: Standard bad request response structure matching the actual implementation
        properties:
            code:
                example: 400
                type: integer
            data: {}
            message:
                example: Bad Request
                type: string
            status:
                example: error
                type: string
        type: object
    response.ForbiddenResponse:
        description: Standard forbidden response structure matching the actual implementation
        properties:
            code:
                example: 403
                type: integer
            data: {}
            message:
                example: Forbidden
                type: string
            status:
                example: forbidden
                type: string
        type: object
    response.InternalErrorResponse:
        description: Standard internal server error response structure matching the actual implementation
        properties:
            code:
                example: 500
                type: integer
            data: {}
            message:
                example: Internal Server Error
                type: string
            status:
                example: error
                type: string
        type: object
    response.MethodNotAllowedResponse:
        description: Standard method not allowed response structure matching the actual implementation
        properties:
            code:
                example: 405
                type: integer
            data: {}
            message:
                example: Method Not Allowed
                type: string
            status:
                example: error
                type: string
        type: object
    response.NotFoundResponse:
        description: Standard not found response structure matching the actual implementation
        properties:
            code:
                example: 404
                type: integer
            data: {}
            message:
                example: Not Found
                type: string
            status:
                example: error
                type: string
        type: object
    response.SuccessResponse:
        description: Standard success response structure matching the actual implementation
        properties:
            code:
                example: 200
                type: integer
            data: {}
            message:
                example: Request Succeeded
                type: string
            status:
                example: success
                type: string
        type: object
    response.UnauthorizedResponse:
        description: Standard unauthorized response structure matching the actual implementation
        properties:
            code:
                example: 401
                type: integer
            data: {}
            message:
                example: Unauthorized
                type: string
            status:
                example: error
                type: string
        type: object
    roles.CustomRole:
        properties:
            createdAt:
                type: string
            description:
                type: string
            id:
                type: string
            isDeletable:
                type: boolean
            name:
                type: string
            orgTypeIdentifier:
                type: string
            organizationId:
                type: string
            templateRoleIdentifier:
                type: string
            updatedAt:
                type: string
        type: object
    softwaregateway.SoftwareGatewayResponse:
        properties:
            apikey:
                type: string
            config:
                type: string
            createdat:
                type: string
            datelastcheckedinutc:
                type: string
            description:
                type: string
            gatewayversion:
                type: string
            id:
                type: string
            isenabled:
                type: boolean
            machinekey:
                type: string
            name:
                type: string
            organizationid:
                type: string
            pushconfigonnextcheck:
                type: boolean
            templateid:
                type: string
            token:
                type: string
            updatedat:
                type: string
        type: object
    user.User:
        properties:
            createdAt:
                type: string
            description:
                type: string
            firstName:
                type: string
            id:
                type: string
            isDeleted:
                type: boolean
            lastLogin:
                type: string
            lastName:
                type: string
            mobile:
                type: string
            notificationSmsEnabled:
                type: boolean
            updatedAt:
                type: string
        type: object
info:
    contact:
        email: <EMAIL>
        name: API Support
        url: http://www.synapse-its.com/support
    description: Comprehensive API documentation for the Onramp microservice
    title: Onramp Service API
    version: 1.0.0
openapi: ""
paths:
    /api/organizations/{organizationId}/dashboard/devices:
        get:
            description: 'Retrieves all locations and devices with their status for dashboard visualization. The endpoint returns locations and devices separately. Users can only view devices they have permission to access. Status defaults: "nevercomm" for disabled devices, "error" for enabled devices with no status data.'
            parameters:
                - description: Organization ID to scope the request
                  in: path
                  name: organizationId
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Dashboard data retrieved successfully
                    schema:
                        allOf:
                            - $ref: '#/definitions/response.SuccessResponse'
                            - properties:
                                data:
                                    $ref: '#/definitions/dashboard.DashboardResponse'
                              type: object
                "400":
                    description: Bad Request - Invalid parameters
                    schema:
                        $ref: '#/definitions/response.BadRequestResponse'
                "401":
                    description: Unauthorized
                    schema:
                        $ref: '#/definitions/response.UnauthorizedResponse'
                "500":
                    description: Internal Server Error
                    schema:
                        $ref: '#/definitions/response.InternalErrorResponse'
            summary: Get dashboard devices and locations
            tags:
                - dashboard
schemes:
    - http
    - https
swagger: "2.0"
