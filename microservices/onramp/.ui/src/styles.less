@import '../node_modules/ng-zorro-antd/ng-zorro-antd.less';

// ==========================================
// 2. Global Colors & Typography
// ==========================================
@primary-color: #000047;
@primary-color-hover: #0303a0;
@primary-color-text: #0E73F6;
@primary-color-outline: #D7EDFF;

@error-color: #ff4d4f;
@error-color-hover: #f17b7c;

@text-color: #6C7278;
@text-color-secondary: #8c8c8c;
@text-color-disabled: #bfbfbf;
@text-color-selected: #FF0066;
@text-color-modal: #000047;

@background-color-base: #f5f5f5;
@background-color-light: #fafafa;
@background-color-new-design: #FFE6F0;

@font-family: 'Arial', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;

// ==========================================
// 3. Border & Radius
// ==========================================
@border-color-base: #d9d9d9;
@border-color-split: #f0f0f0;

@border-radius-sm: 4px;
@border-radius-base: 6px;
@border-radius-lg: 12px;


// ==========================================
// 4. Common Component Sizes
// ==========================================
@input-height-sm: 36px;
@input-height-base: 44px;
@input-height-lg: 52px;

@height-sm: @input-height-sm;
@height-base: @input-height-base;
@height-lg: @input-height-lg;

@btn-height-sm: 36px;
@btn-height-base: 44px;
@btn-height-lg: 52px;

// ==========================================
// 5. Extended Color Palette (Tag / Badge / Status)
// ==========================================
@magenta-base: #FF0066;
@pink-base: #FF0066;
@red-base: #f5222d;
@orange-base: #fa8c16;
@yellow-base: #fadb14;
@volcano-base: #fa541c;
@geekblue-base: #2f54eb;
@lime-base: #a0d911;
@gold-base: #faad14;

// ==========================================
// 6. Input / Textarea
// ==========================================
@input-bg: #ffffff;
@input-color: @text-color;
@input-placeholder-color: @text-color-secondary;
@input-border-color: @border-color-base;
@input-border-color-hover: @primary-color;
@input-border-color-focus: @primary-color;
@input-disabled-bg: @background-color-base;
@input-border-radius: @border-radius-base;

// ==========================================
// 7. Select
// ==========================================
@select-border-radius: @border-radius-base;
@select-dropdown-bg: #fff;

@select-item-selected-bg: fade(@primary-color, 10%);
@select-item-active-bg: fade(@primary-color, 6%);
@select-item-selected-color: @primary-color;
@select-item-active-color: @primary-color-hover;

@select-selection-item-bg: fade(@primary-color, 10%);
@select-selection-item-border-color: fade(@primary-color, 30%);
@select-selection-item-color: @primary-color;

@select-placeholder-color: @text-color-secondary;
@select-arrow-color: @text-color-disabled;
@select-clear-color: @text-color-secondary;

@select-border-color: @border-color-base;
@select-border-color-hover: @primary-color;
@select-border-color-active: @primary-color;

// ==========================================
// 8. Button
// ==========================================
@btn-border-radius-sm: @border-radius-sm;
@btn-border-radius-base: @border-radius-base;
@btn-border-radius-lg: @border-radius-lg;

@btn-default-color: @text-color;
@btn-default-bg: #fff;
@btn-default-border: @border-color-base;

@btn-primary-color: #fff;
@btn-primary-bg: @primary-color;
@btn-primary-border: @primary-color;

@btn-danger-color: #fff;
@btn-danger-bg: @error-color;
@btn-danger-border: @error-color;

@btn-primary-hover-bg: @primary-color-hover;
@btn-primary-hover-border: @primary-color-hover;
@btn-primary-hover-color: #fff;

// ==========================================
// 9. Radio / Checkbox
// ==========================================
@radio-dot-color: @text-color-selected;
@radio-size: 18px;
@radio-button-hover-color: @primary-color-hover;
@radio-button-active-color: @primary-color;

@checkbox-border-radius: 4px;
@checkbox-color: @primary-color;
@checkbox-border-color: @border-color-base;
@checkbox-checked-bg: @primary-color;

// ==========================================
// 10. Tag / Badge
// ==========================================
@tag-default-bg: fade(@primary-color, 5%);
@tag-default-color: @primary-color;
@tag-default-border-color: fade(@primary-color, 30%);
@tag-font-size: @font-size-base;
@tag-line-height: 22px;
@tag-border-radius: @border-radius-sm;

@badge-color: @primary-color;

@tag-pink-bg: fade(@pink-base, 8%);
@tag-pink-color: @pink-base;
@tag-red-bg: fade(@red-base, 8%);
@tag-red-color: @red-base;
@tag-orange-bg: fade(@orange-base, 8%);
@tag-orange-color: @orange-base;
@tag-green-bg: fade(@lime-base, 8%);
@tag-green-color: @lime-base;
@tag-blue-bg: fade(@geekblue-base, 8%);
@tag-blue-color: @geekblue-base;
@tag-magenta-bg: fade(@magenta-base, 8%);
@tag-magenta-color: @magenta-base;

// ==========================================
// 11. Table
// ==========================================
// @table-header-bg: fade(@primary-color, 5%);
@table-header-color: @text-color;
@table-header-sort-bg: fade(@primary-color, 10%);
@table-row-hover-bg: fade(@primary-color, 4%);
@table-selected-row-bg: fade(@primary-color, 8%);
@table-border-color: @border-color-split;
@table-radius: @border-radius-base;

// ==========================================
// 12. Card / Modal / Drawer
// ==========================================
@card-background: #fff;
@card-head-color: @text-color;
@card-head-font-size: 16px;
@card-head-padding: 16px;
@card-padding-base: 24px;
@card-radius: @border-radius-lg;

@modal-header-bg: #fff;
@modal-content-bg: #fff;
@modal-heading-color: @text-color-modal;
@modal-close-color: @text-color-secondary;
@modal-footer-bg: #fff;
@modal-header-border-color-split: @border-color-split;
@modal-border-radius: @border-radius-lg;

@drawer-bg: #fff;
@drawer-header-bg: #fff;
@drawer-header-border-color-split: @border-color-split;
@drawer-border-radius: @border-radius-lg;

// ==========================================
// 13. Menu / Dropdown
// ==========================================
@menu-item-color: @text-color;
@menu-item-bg: transparent;
@menu-item-height: 44px;
@menu-item-hover-color: @primary-color;
@menu-item-hover-bg: fade(@primary-color, 8%);
@menu-item-active-bg: fade(@primary-color, 10%);
@menu-item-selected-bg: fade(@primary-color, 12%);
@menu-item-selected-color: @primary-color;
@menu-highlight-color: @primary-color;

@dropdown-menu-bg: #fff;
@dropdown-menu-item-hover-bg: fade(@primary-color, 8%);
@dropdown-menu-item-active-bg: fade(@primary-color, 15%);

// ==========================================
// 14. Alert / Message / Notification
// ==========================================
@alert-success-bg-color: fade(@lime-base, 10%);
@alert-success-border-color: fade(@lime-base, 30%);
@alert-success-color: @lime-base;

@alert-warning-bg-color: fade(@gold-base, 10%);
@alert-warning-border-color: fade(@gold-base, 30%);
@alert-warning-color: @gold-base;

@alert-error-bg-color: fade(@red-base, 10%);
@alert-error-border-color: fade(@red-base, 30%);
@alert-error-color: @red-base;

@alert-info-bg-color: fade(@geekblue-base, 10%);
@alert-info-border-color: fade(@geekblue-base, 30%);
@alert-info-color: @geekblue-base;

// ==========================================
// 15. Link & Focus
// ==========================================
@link-color: @primary-color;
@link-hover-color: @primary-color-hover;
@outline-color: fade(@primary-color, 30%);