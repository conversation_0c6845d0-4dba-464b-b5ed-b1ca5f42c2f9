<div class="forgot-password-container">
  <h2>Forgot Password</h2>
  <p class="forgot-password-subtitle">
    Provide the email address associated with your account to recover your password.
  </p>
  <form nz-form class="form-forgot-password" [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()">
    <nz-form-item>
      <nz-form-control nzErrorTip="Please enter a valid email address">
        <span>Email<span class="text-danger">*</span></span>
        <nz-input-group nzPrefixIcon="mail" class="br-8">
          <input nz-input formControlName="email" placeholder="Email" type="email" autocomplete="email" />
        </nz-input-group>
      </nz-form-control>
    </nz-form-item>
    <div class="button-group">
      <button nz-button nzType="primary" [nzLoading]="isLoading" [disabled]="forgotPasswordForm.invalid" type="submit">
        Send Reset Link
      </button>
    </div>
    <div class="btn-action">
      <span (click)="backToLogin()">Login</span>
      <span (click)="backToRegister()">Register</span>
    </div>
  </form>
</div>