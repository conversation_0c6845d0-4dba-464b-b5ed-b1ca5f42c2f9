::ng-deep body {
  background: linear-gradient(180deg, rgba(253, 39, 125, 0.25) 0%, rgba(255, 121, 30, 0.5) 100%), url('/images/wallpaper.jpg');
  background-size: cover;
  background-blend-mode: overlay;
}

.login-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 155px;
}

.login-container {
  width: 540px;
  min-height: 595px;
  margin: 0 auto;
  padding: 40px;
  background: #fff;
  border-radius: 16px;
  border: 1px solid #D9D9D9;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  .label-form {
    color: #6C7278;
    line-height: 22px;
    font-weight: 400;
  }

  .login-subtitle {
    text-align: center;
    color: #31394da6;
  }

  h2 {
    font-size: 24px;
    margin-bottom: 8px;
    font-weight: 500;
    text-align: center;
    color: #1f2937;
    line-height: 36px;
  }

  .divider {
    display: flex;
    align-items: center;
    text-align: center;
    color: #6C7278;
    font-size: 14px;
    margin: 16px 0;

    &::before,
    &::after {
      content: "";
      flex: 1;
      border-bottom: 1px solid #d9d9d9;
    }

    &:not(:empty)::before {
      margin-right: 8px;
    }

    &:not(:empty)::after {
      margin-left: 8px;
    }
  }

  .form-login {
    nz-form-control {
      ::ng-deep .ant-form-item-control-input-content {
        display: grid;
        gap: 5px;
      }
    }

    .form-input {
      border-radius: 6px;
    }
  }

  .button-group {
    .btn-login {
      width: 100%;
      font-size: 16px;
      font-weight: 700;
      padding: 0;
      margin-bottom: 16px;
    }
  }

  a {
    color: #FF0066;
    text-decoration: none;
  }

  h3 {
    font-size: 18px;
    margin-bottom: 4px;
    font-weight: bold;
    line-height: 29px;
  }
}

.btn-signin-synapse-its {
  button {
    width: 100%;
    font-size: 16px;
    font-weight: 700;
    padding: 0;
    margin-bottom: 16px;
  }
}

.login-subtitle,
.sso-subtitle,
.create-account-subtitle {
  color: #757575;
  font-size: 14px;
  margin-bottom: 24px;
}

nz-form-item {
  margin-bottom: 16px;
}

nz-form-label {
  font-weight: bold;
}

.button-group {
  display: flex;
  flex-direction: column;
  align-items: stretch;

  .btn-forgot-pass {
    align-self: flex-end;
    font-size: 14px;
  }
}

.create-account-section {
  text-align: center;
}

nz-button {
  width: 100%;
  margin-top: 10px;
}

nz-input-group {
  display: flex;
  align-items: center;
}

[nz-icon] {
  color: rgba(0, 0, 0, 0.45);
}

.modal-title-center {
  text-align: center;
  border-bottom: 1px solid #18181B0F;
  padding-bottom: 20px;
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 46px;
  height: 46px;
  margin: 0 auto 10px;
  border-radius: 8px;
  background-color: #FFCFD773;

  nz-icon {
    color: #FF294E;
    font-size: 24px;
  }
}

.tick-success {
  color: #52c41a;
}

.tick-gap {
  margin-right: 6px;
}

::ng-deep .try-login-modal {
  .ant-modal-confirm-btns {
    display: flex;
    justify-content: center;
    width: 100%;
    display: block;
    padding: 0;
    margin: 16px 0 0 0;
    text-align: unset;

    button {
      width: 100%;
      display: block;
      height: 52px;
      font-weight: 700;
      border-radius: 8px;
    }
  }
}

::ng-deep .ant-modal-confirm-body {
  &>.anticon+.ant-modal-confirm-title+.ant-modal-confirm-content {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .login-page {
    gap: 32px;
    padding: 16px;

    img {
      width: 180px;
      max-width: 70%;
    }
  }

  .login-container {
    width: 100%;
    padding: 24px 16px;
    min-height: unset;
    border-radius: 8px;

    h2 {
      font-size: 20px;
      line-height: 28px;
    }

    .login-subtitle {
      font-size: 13px;
    }

    .divider {
      margin: 12px 0;
      font-size: 13px;
    }
  }

  .button-group {
    .btn-forgot-pass {
      font-size: 13px;
    }
  }
}

.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  pointer-events: all;
}

.global-loading-spinner {
  display: block;
  width: 56px;
  height: 56px;
  animation: global-spinner-rotate 0.9s linear infinite;
}

@keyframes global-spinner-rotate {
  to {
    transform: rotate(1turn);
  }
}