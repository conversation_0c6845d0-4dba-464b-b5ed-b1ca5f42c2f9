<div class="login-page">
  <img src="/images/synapse-bg.png" alt="Synapse Logo" />
  <div class="login-container">
    <div class="login-titlte-header">
      <h2>Get Started</h2>
      <p class="login-subtitle">
        Access your account to start
      </p>
    </div>

    <form id="form-login" nz-form class="form-login" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
      <nz-form-item>
        <nz-form-control [nzErrorTip]="getUsernameErrorTip()">
          <span class="label-form">Username</span>
          <nz-input-group nzSize="large">
            <input id="username" class="form-input br-8" nz-input nzSize="large" formControlName="username"
              placeholder="Enter your username" />
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-control [nzErrorTip]="passwordErrorTpl">
          <span class="label-form">Password</span>
          <nz-input-group [nzSuffix]="suffixTemplate" nzSize="large" class="br-8">
            <input id="password" class="no-radius" nz-input nzSize="large" formControlName="password"
              placeholder="Enter your password" [type]="passwordVisible ? 'text' : 'password'"
              autocomplete="new-password" />
          </nz-input-group>
          <ng-template #suffixTemplate>
            <span *ngIf="isControlValid('password')" nz-icon nzType="check-circle" nzTheme="fill"
              class="tick-success tick-gap"></span>
            <span nz-icon [nzType]="passwordVisible ? 'eye' : 'eye-invisible'" style="cursor: pointer;"
              (click)="togglePasswordVisibility()"></span>
          </ng-template>
          <ng-template #passwordErrorTpl let-control>
            <ng-container *ngIf="control.hasError('required')">
              Please enter a password
            </ng-container>
            <ng-container
              *ngIf="!control.hasError('required') && (control.hasError('minlength') || control.hasError('weakPassword'))">
              Password does not meet the required policy
            </ng-container>
          </ng-template>
        </nz-form-control>
      </nz-form-item>
      <div class="button-group">
        <button id="kc-login" class="btn-login" nz-button nzType="primary" type="submit" [nzLoading]="isLoading" nzBlock
          nzSize="large">Login</button>
        <a class="btn-forgot-pass" nzType="default" (click)="forgotPassword()">Forgot Password?</a>
      </div>
    </form>

    <div class="divider">
      <span>Or</span>
    </div>

    <div class="btn-signin-synapse-its">
      <button id="btn-keycloak" nz-button nzType="default" nzBlock nzSize="large" (click)="loginWithITS()">
        Sign in with Synapse ITS
      </button>
    </div>

    <div class="create-account-section">
      <span>Don’t have an account?</span><a id="register-btn" nzType="default" nzBlock (click)="createNewAccount()">
        Create Account</a>
    </div>
  </div>
</div>

<ng-template #successTpl>
  <div class="modal-title-center">
    <span nz-icon nzType="check-circle" nzTheme="fill"
      style="color:#52c41a;font-size:24px;display:block;margin:0 auto 8px;"></span>
    <div style="font-weight:600;">{{ successTitle }}</div>
    <div style="margin-top:4px;">{{ successMessage }}</div>
  </div>
</ng-template>

<!-- Full-screen loading overlay -->
<div class="global-loading-overlay" *ngIf="isLoading">
  <svg class="global-loading-spinner" xmlns="http://www.w3.org/2000/svg" width="56" height="56" viewBox="0 0 56 56"
    fill="none" aria-hidden="true" focusable="false">
    <path
      d="M36.6385 5.60895C39.579 6.7434 42.2672 8.44592 44.5497 10.6193C46.8322 12.7927 48.6643 15.3943 49.9414 18.2757C51.2184 21.1571 51.9154 24.2619 51.9926 27.4126C52.0698 30.5634 51.5256 33.6985 50.3911 36.639C49.2567 39.5795 47.5542 42.2677 45.3808 44.5502C43.2074 46.8327 40.6058 48.6648 37.7244 49.9419C34.843 51.2189 31.7382 51.9159 28.5875 51.9931C25.4367 52.0703 22.3015 51.5261 19.3611 50.3916C16.4206 49.2572 13.7324 47.5547 11.4499 45.3813C9.16735 43.2079 7.33527 40.6063 6.05822 37.7249C4.78117 34.8435 4.08416 31.7387 4.00699 28.588C3.92982 25.4372 4.474 22.3021 5.60845 19.3616C6.7429 16.4211 8.44542 13.7329 10.6188 11.4504C12.7922 9.16786 15.3938 7.33578 18.2752 6.05873C21.1566 4.78168 24.2614 4.08467 27.4121 4.0075C30.5629 3.93032 33.698 4.4745 36.6385 5.60895L36.6385 5.60895Z"
      stroke="#D9D9D9" stroke-width="8" />
    <path
      d="M36.6385 5.60895C39.579 6.7434 42.2672 8.44592 44.5497 10.6193C46.8322 12.7927 48.6643 15.3943 49.9414 18.2757C51.2184 21.1571 51.9154 24.2619 51.9926 27.4126C52.0698 30.5634 51.5256 33.6985 50.3911 36.639C49.2567 39.5795 47.5542 42.2677 45.3808 44.5502C43.2074 46.8327 40.6058 48.6648 37.7244 49.9419C34.843 51.2189 31.7382 51.9159 28.5875 51.9931C25.4367 52.0703 22.3015 51.5261 19.3611 50.3916C16.4206 49.2572 13.7324 47.5547 11.4499 45.3813C9.16735 43.2079 7.33527 40.6063 6.05822 37.7249C4.78117 34.8435 4.08416 31.7387 4.00699 28.588C3.92982 25.4372 4.474 22.3021 5.60845 19.3616C6.7429 16.4211 8.44542 13.7329 10.6188 11.4504C12.7922 9.16786 15.3938 7.33578 18.2752 6.05873C21.1566 4.78168 24.2614 4.08467 27.4121 4.0075C30.5629 3.93032 33.698 4.4745 36.6385 5.60895L36.6385 5.60895Z"
      stroke="url(#paint0_linear_5_1830)" stroke-width="8" stroke-linecap="round" />
    <defs>
      <linearGradient id="paint0_linear_5_1830" x1="5.60845" y1="19.3616" x2="50.3911" y2="36.639"
        gradientUnits="userSpaceOnUse">
        <stop stop-color="white" />
        <stop offset="1" stop-color="#FF0066" />
      </linearGradient>
    </defs>
  </svg>
</div>

<ng-template #errorTpl>
  <div class="modal-title-center">
    <div class="icon-wrapper">
      <nz-icon nzType="exclamation-circle" nzTheme="fill" />
    </div>
    <div style="font-weight:600; margin-bottom: 9px;">{{ errorTitle }}</div>
    <div style="color: #616161;">{{ errorMessage }}</div>
  </div>
</ng-template>