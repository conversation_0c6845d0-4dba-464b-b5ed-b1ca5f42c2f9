import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, SimpleChanges, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../core/services/auth.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-login',
  standalone: false,
  templateUrl: './login.component.html',
  styleUrl: './login.component.less'
})
export class LoginComponent {
  @ViewChild('successTpl', { static: true }) successTpl!: TemplateRef<any>;
  @ViewChild('errorTpl', { static: true }) errorTpl!: TemplateRef<any>;
  loginForm!: FormGroup;
  showRegister = false;
  passwordVisible = false;
  credentials = { username: '', password: '' };
  // errorMessage: string | null = null;
  isLoading = false;
  successTitle = 'Account has been created!';
  successMessage = 'Your account has been created successfully. You may now proceed to log in.';
  errorTitle = 'Registration failed';
  errorMessage = 'There was a problem creating your account. Please try again.';

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private message: NzMessageService,
    private auth: AuthService,
    private modal: NzModalService
  ) { }
  get username() { return this.loginForm.get('username'); }
  get password() { return this.loginForm.get('password'); }

  ngOnInit() {
    this.loginForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  isControlValid(controlName: string): boolean {
    const control = this.loginForm.get(controlName);
    return !!control && control.valid && (control.dirty || control.touched);
  }
  getUsernameErrorTip(): string {
    if (this.username?.errors?.['notExist']) return 'Username does not exist, please try again.';
    if (this.username?.errors?.['minlength']) return 'Username must be at least 3 characters.';
    if (this.username?.errors?.['required']) return 'Username is required.';
    return '';
  }
  togglePasswordVisibility() {
    this.passwordVisible = !this.passwordVisible;
  }
  onSubmit() {
    if (this.loginForm.valid) {
      this.isLoading = true;
      const formLogin = this.loginForm.value;
      this.auth.loginApi(formLogin).subscribe({
        next: () => this.isLoading = false,
        error: (err) => {
          this.isLoading = false;
          this.errorTitle = 'Issue with login';
          this.errorMessage = 'Something went wrong. Please try again.';
          this.modal.error({
            nzContent: this.errorTpl,
            nzOkText: 'Try Again',
            nzOkType: 'default',
            nzCentered: true,
            nzMaskClosable: false,
            nzClosable: false,
            nzIconType: '' as any,
            nzClassName: 'try-login-modal',
            nzStyle: { heght: '40px' }
          });
        }
      });
    } else {
      Object.values(this.loginForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  handleClear() {
    this.loginForm.get('password')?.setValue('');
  }

  isClearValue(): boolean {
    return this.loginForm.get('password')?.value !== '';
  }

  forgotPassword() {
    this.router.navigate(['/forgot-password']);
  }

  loginWithITS() {
    this.auth.login();
  }

  createNewAccount() {
    this.router.navigate(['/account/register']);
  }
}
