import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';

@Component({
  selector: 'app-change-password',
  standalone: false,
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.less']
})
export class ChangePasswordComponent {
  @Input() isVisible = false;
  @Input() data = new EventEmitter<any>();
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<{ formChangePassword: any; currentPassword: any }>();
  form: FormGroup;
  currentPasswordVisible = false;
  newPasswordVisible = false;
  confirmPasswordVisible = false;

  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({
      current_password: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]],
      new_password: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.maxLength(255),
        this.passwordStrengthValidator()
      ]],
      confirm_password: ['', [
        Validators.required,
        Validators.maxLength(255),
        this.passwordMatchValidator()
      ]]
    }, { updateOn: 'change' });
  }

  // Custom validator for password strength
  passwordStrengthValidator() {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value || '';
      const hasUpperCase = /[A-Z]/.test(value);
      const hasLowerCase = /[a-z]/.test(value);
      const hasNumber = /[0-9]/.test(value);
      const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
      const valid = hasUpperCase && hasLowerCase && hasNumber && hasSpecialChar;
      return valid ? null : { passwordStrength: true };
    };
  }

  // Custom validator for password matching on confirmPassword
  passwordMatchValidator() {
    return (control: AbstractControl): ValidationErrors | null => {
      const confirmPassword = control.value;
      const newPassword = this.form?.get('new_password')?.value;
      return newPassword && confirmPassword && newPassword === confirmPassword
        ? null
        : { passwordMismatch: true };
    };
  }

  // Explicit method to check if passwords match
  checkPasswordsMatch(): boolean {
    const newPassword = this.form.get('new_password')?.value;
    const confirmPassword = this.form.get('confirm_password')?.value;
    return newPassword && confirmPassword && newPassword === confirmPassword;
  }

  togglePasswordVisibility(field: 'current_password' | 'new_password' | 'confirm_password'): void {
    if (field === 'current_password') {
      this.currentPasswordVisible = !this.currentPasswordVisible;
    } else if (field === 'new_password') {
      this.newPasswordVisible = !this.newPasswordVisible;
    } else if (field === 'confirm_password') {
      this.confirmPasswordVisible = !this.confirmPasswordVisible;
    }
  }

  isClearValue(field: 'current_password' | 'new_password' | 'confirm_password'): boolean {
    return !!this.form.get(field)?.value;
  }

  handleClear(field: 'current_password' | 'new_password' | 'confirm_password'): void {
    this.form.get(field)?.setValue('');
    this.form.get('confirm_password')?.updateValueAndValidity();
  }

  handleSavePassword() {
    if (this.form.valid) {
      const formValue = this.form.value;
      const formChangePassword = {
        current_password: formValue.current_password.trim(),
        new_password: formValue.new_password.trim(),
        confirm_password: formValue.confirm_password.trim()
      };
      const currentPassword = this.data;
      this.confirm.emit({ formChangePassword, currentPassword });
      this.handleCancel();
    } else {
      Object.values(this.form.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  handleCancel() {
    this.form.reset({
      current_password: '',
      new_password: '',
      confirm_password: ''
    });
    this.currentPasswordVisible = false;
    this.newPasswordVisible = false;
    this.confirmPasswordVisible = false;
    this.close.emit();
  }

  // Helper to get error message for a specific control
  getErrorMessage(controlName: string): string {
    const control = this.form.get(controlName);
    if (control?.errors) {
      if (control.errors['required']) {
        return `Please input ${controlName === 'current_password' ? 'current' : controlName === 'new_password' ? 'new' : 'confirm'} password.`;
      }
      if (control.errors['maxlength']) {
        return 'Password cannot exceed 255 characters.';
      }
      if (controlName === 'new_password' && control.errors['minlength']) {
        return 'New password must be at least 8 characters long.';
      }
      if (controlName === 'new_password' && control.errors['passwordStrength']) {
        return 'New password must contain uppercase, lowercase, number, and special character.';
      }
      if (controlName === 'confirm_password' && control.errors['passwordMismatch']) {
        return 'Confirm password must match new password.';
      }
    }
    return '';
  }
}