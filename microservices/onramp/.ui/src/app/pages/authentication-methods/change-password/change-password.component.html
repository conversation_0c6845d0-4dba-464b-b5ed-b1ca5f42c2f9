<nz-modal class="modal-create-change" [(nzVisible)]="isVisible" [nzTitle]="modalTitle" (nzOnCancel)="handleCancel()"
  [nzFooter]="modalFooter" nzWidth="440px">
  <ng-template #modalTitle>
    <div class="content-header">
      <h2 style="margin-bottom: 0;">Change Password</h2>
    </div>
  </ng-template>
  <ng-container *nzModalContent>
    <nz-form [formGroup]="form" class="modal-add-change">
      <nz-form-item>
        <nz-form-label nzRequired>Current Password</nz-form-label>
        <nz-form-control class="mb-10" [nzErrorTip]="getErrorMessage('current_password')">
          <nz-input-group class="br-8" [nzSuffix]="currentPasswordSuffix">
            <input id="input-current-password" formControlName="current_password" nz-input
              placeholder="Enter Current Password" [type]="currentPasswordVisible ? 'text' : 'password'"
              autocomplete="current-password" />
          </nz-input-group>
        </nz-form-control>
        <ng-template #currentPasswordSuffix>
          <nz-icon *ngIf="isClearValue('current_password')" (click)="handleClear('current_password')"
            class="ant-input-clear-icon mr-5" nzTheme="fill" nzType="close-circle" />
          <span nz-icon [nzType]="currentPasswordVisible ? 'eye-invisible' : 'eye'" style="cursor: pointer;"
            (click)="togglePasswordVisibility('current_password')"></span>
        </ng-template>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label nzRequired>New Password</nz-form-label>
        <nz-form-control class="mb-10" [nzErrorTip]="getErrorMessage('new_password')">
          <nz-input-group class="br-8" [nzSuffix]="newPasswordSuffix">
            <input id="input-new-password" formControlName="new_password" nz-input placeholder="Enter New Password"
              [type]="newPasswordVisible ? 'text' : 'password'" autocomplete="new-password"
              (input)="form.updateValueAndValidity()" />
          </nz-input-group>
        </nz-form-control>
        <ng-template #newPasswordSuffix>
          <nz-icon *ngIf="isClearValue('new_password')" (click)="handleClear('new_password')"
            class="ant-input-clear-icon mr-5" nzTheme="fill" nzType="close-circle" />
          <span nz-icon [nzType]="newPasswordVisible ? 'eye-invisible' : 'eye'" style="cursor: pointer;"
            (click)="togglePasswordVisibility('new_password')"></span>
        </ng-template>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label nzRequired>Confirm Password</nz-form-label>
        <nz-form-control class="mb-10" [nzErrorTip]="getErrorMessage('confirm_password')">
          <nz-input-group class="br-8" [nzSuffix]="confirmPasswordSuffix">
            <input id="input-confirm-password" formControlName="confirm_password" nz-input
              placeholder="Enter Confirm Password" [type]="confirmPasswordVisible ? 'text' : 'password'"
              autocomplete="new-password" (input)="form.updateValueAndValidity()" />
          </nz-input-group>
        </nz-form-control>
        <ng-template #confirmPasswordSuffix>
          <nz-icon *ngIf="isClearValue('confirm_password')" (click)="handleClear('confirm_password')"
            class="ant-input-clear-icon mr-5" nzTheme="fill" nzType="close-circle" />
          <span nz-icon [nzType]="confirmPasswordVisible ? 'eye-invisible' : 'eye'" style="cursor: pointer;"
            (click)="togglePasswordVisibility('confirm_password')"></span>
        </ng-template>
      </nz-form-item>
    </nz-form>
  </ng-container>
  <ng-template #modalFooter>
    <button id="btn-create-change" class="btn-submit-change br-8" nz-button nzType="primary"
      (click)="handleSavePassword()">
      Change Password
    </button>
  </ng-template>
</nz-modal>