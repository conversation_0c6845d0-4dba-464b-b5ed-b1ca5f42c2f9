import { Component } from '@angular/core';
import { Authentication } from '../../core/models/account-authen.model';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AccountService } from '../../core/services/account.service';

@Component({
  selector: 'app-authentication-methods',
  standalone: false,
  templateUrl: './authentication-methods.component.html',
  styleUrl: './authentication-methods.component.less'
})
export class AuthenticationMethodsComponent {
  isTableLoading = false;
  highlightedRowId: string | null = null;
  isModalVisible = false;
  confirmModal?: NzModalRef;
  listDataAuthentication: Authentication[] = []
  currentPass: any;
  userId: any = localStorage.getItem('permissions');
  constructor(
    private modalService: NzModalService,
    private accountService: AccountService,
    private message: NzMessageService,
  ) { }
  getOrgName(org: any): string {
    return org ?? '';
  }
  isArray(value: any): value is any[] {
    return Array.isArray(value);
  }
  isObject(value: any): value is object {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
  }

  ngOnInit() {
    this.getAuthMethodList(this.userId);
  }
  getAuthMethodList(userId: any) {
    this.isTableLoading = true;
    this.accountService.getAuthMethods(userId).subscribe({
      next: (res: any) => {
        this.listDataAuthentication = res.data;
        this.isTableLoading = false;
      },
      error: (error) => {
        this.isTableLoading = false;
        this.listDataAuthentication = [];
      }
    });
  }
  handleChangePassword(value: any) {
    this.currentPass = value;
    this.isModalVisible = true;
  }
  handleSavePassword(value: { formChangePassword: any; currentPassword: string }) {
    this.accountService.changePassword(this.userId, value).subscribe({
      next: (res: any) => {
        this.message.success('Password changed successfully');
        this.isModalVisible = false;
        this.getAuthMethodList(this.userId);
      },
      error: (error) => {
        this.message.error('Failed to change password');
      }
    });
  }
  handleDelete(value: any) {
    this.confirmModal = this.modalService.confirm({
      nzTitle: `<b>Delete Confirmation</b>`,
      nzContent: `Are you sure you want to delete the account <b>${value.userName ? value.userName : value.email}</b>?`,
      nzClassName: 'custom-confirm-modal modal-delete-account',
      nzWidth: '500px',
      nzOkText: 'Confirm',
      nzOkDanger: true,
      nzOnOk: () => {
        this.confirmDelete(value);
      }
    });
  }
  confirmDelete(value: any) {
    this.accountService.deleteAuthMethod(this.userId, value.id).subscribe({
      next: (res: any) => {
        this.message.success('Account deleted successfully');
        this.getAuthMethodList(this.userId);
      },
      error: (error) => {
        this.message.error('Failed to delete account');
      }
    });
  }

  onCloseModal() {
    this.isModalVisible = false;
  }
}
