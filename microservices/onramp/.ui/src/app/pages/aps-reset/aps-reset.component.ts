import { Component, ElementRef, ViewChild } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { ApsResetService } from '../../core/services/aps_reset.service';
import { AuthService } from '../../core/services/auth.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ActivatedRoute } from '@angular/router';
interface Permission {
  organizationId: string;
  scope: string;
  permissions?: string[];
}
interface ApsResetResponse {
  data: string;
  message?: string;
}
@Component({
  selector: 'app-aps-reset',
  standalone: false,
  templateUrl: './aps-reset.component.html',
  styleUrl: './aps-reset.component.less'
})
export class ApsResetComponent {
  @ViewChild('input1') input1!: ElementRef<HTMLInputElement>;
  @ViewChild('input2') input2!: ElementRef<HTMLInputElement>;
  @ViewChild('input3') input3!: ElementRef<HTMLInputElement>;
  @ViewChild('input4') input4!: ElementRef<HTMLInputElement>;

  codes: string[] = ['', '', '', ''];
  responseCode: string | null = null;
  hasApsResetPermission: boolean = false;
  orgId: string | null = null;
  isVerified: boolean = false;

  private destroy$ = new Subject<void>();
  private readonly validFirstChar = /^[0-9A-HJKLMNP-X]$/i;
  private readonly validOtherChar = /^[0-9A-F]$/i;

  constructor(
    private apsResetService: ApsResetService,
    private message: NzMessageService,
    private route: ActivatedRoute,
  ) { }

  ngOnInit(): void {
    this.route.params.subscribe((params: any) => {
      const state = window.history.state;
      const newOrgId = state.orgData.id;
      if (newOrgId && newOrgId !== this.orgId) {
        this.handleClearFormInput();
      }
      this.orgId = params.orgId;
      if (!this.orgId) {
        this.message.error('Organization ID is missing.');
        this.hasApsResetPermission = false;
        return;
      } else {
        this.hasApsResetPermission = true;
      }

    });
  }

  onInput(event: Event, inputIndex: number): void {
    const input = event.target as HTMLInputElement;
    let value = input.value.toUpperCase();

    if (inputIndex === 1 && value.length > 0) {
      if (!this.validFirstChar.test(value[0])) {
        input.value = value.slice(1);
        value = input.value;
      }
      if (value.length > 1 && !this.validOtherChar.test(value[1])) {
        input.value = value[0];
        value = input.value;
      }
    } else if (inputIndex > 1 && value.length > 0) {
      value = value.split('').filter(char => this.validOtherChar.test(char)).join('');
      input.value = value;
    }

    this.codes[inputIndex - 1] = value;
    input.value = value;

    if (value.length === 2) {
      this.focusNextInput(inputIndex);
    }
  }

  onKeyDown(event: KeyboardEvent, inputIndex: number): void {
    const input = event.target as HTMLInputElement;
    const isAllSelected = input.selectionStart === 0 && input.selectionEnd === input.value.length;
    if (event.key === 'Backspace') {
      if (isAllSelected && input.value.length > 0) {
        input.value = '';
        this.codes[inputIndex - 1] = '';
        event.preventDefault();
        return;
      } else if (this.codes[inputIndex - 1].length === 0 && inputIndex > 1) {
        this.focusPreviousInput(inputIndex);
        event.preventDefault();
      } else if (!isAllSelected) {
        input.value = '';
        this.codes[inputIndex - 1] = '';
      }
    }
  }

  private focusNextInput(currentIndex: number): void {
    if (currentIndex < 4) {
      const inputRef = (this as any)[`input${currentIndex + 1}`] as ElementRef<HTMLInputElement>;
      if (inputRef && inputRef.nativeElement) {
        inputRef.nativeElement.focus();
        inputRef.nativeElement.select();
      }
    }
  }

  private focusPreviousInput(currentIndex: number): void {
    if (currentIndex > 1) {
      const prevInputRef = (this as any)[`input${currentIndex - 1}`] as ElementRef<HTMLInputElement>;
      if (prevInputRef && prevInputRef.nativeElement) {
        prevInputRef.nativeElement.focus();
        prevInputRef.nativeElement.select();
      }
    }
  }

  isSubmitEnabled(): boolean {
    return (
      this.codes.every(code => code.length === 2) &&
      this.validFirstChar.test(this.codes[0][0]) &&
      this.validOtherChar.test(this.codes[0][1]) &&
      this.codes.slice(1).every(code => code.split('').every(char => this.validOtherChar.test(char)))
    );
  }

  handleClearFormInput(): void {
    this.codes = ['', '', '', ''];
    this.responseCode = null;
    [this.input1, this.input2, this.input3, this.input4].forEach((inputRef, index) => {
      if (inputRef && inputRef.nativeElement) {
        inputRef.nativeElement.value = '';
      }
    });
    if (this.input1 && this.input1.nativeElement) {
      this.input1.nativeElement.focus();
    }
    this.isVerified = false;
  }

  formattedChallengeCodeForConfirm() {
    const code = this.codes;
    let formatted = '';
    for (let i = 0; i < code.length; i++) {
      formatted += code[i] + ' ';
    }
    return formatted.trim();
  }

  handleSubmitCode(): void {
    const fullCode = this.codes.join('');
    if (!this.orgId) {
      this.message.error('Organization ID is missing.');
      return;
    }
    const formChallenge = {
      challengeCode: fullCode
    };
    this.apsResetService.resetAps(this.orgId, formChallenge).pipe(takeUntil(this.destroy$)).subscribe({
      next: (response: ApsResetResponse) => {
        this.responseCode = response.data;
        // this.message.success(response.message || 'APS reset successful. Password will be set to ' + response.data + '.');
        this.isVerified = false;
      },
      error: (err: any) => {
        this.isVerified = true;
        this.message.error(err.error?.message || 'Failed to reset APS. Please try again.');
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}