.aps-reset-container {
  padding: 40px;
  max-width: 540px;
  min-height: 381px;
  margin: 0 auto;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
  border-radius: 8px;
  text-align: center;

  .form-header {
    h2 {
      color: #000047;
      line-height: 36px;
      margin-bottom: 5px;
    }

    p {
      color: #31394D;
      margin-bottom: 24px;
    }
  }

  button {
    width: 100%;
    font-size: 16px;
    font-weight: 700;
    padding: 0;
    margin-bottom: 16px;
  }
}

.input-group {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.input-wrapper {
  position: relative;
}

.code-input {
  width: 103px;
  text-transform: uppercase;
  font-size: 20px;
  line-height: 28px;
  border-radius: 8px;
}

.border-success {
  border: 1px solid #059600;
}

.border-error {
  border: 1px solid #E70000;
}

.content-verified {
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
  color: #059600;
}

.content-verified-error {
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
  color: #E70000;
}

.response-code {
  .btn-reset-aps {
    width: 172px;
    height: 40px;
    font-size: 14px;
    padding: 0 16px;
    font-weight: 400;
    background-color: #242424;
    margin-bottom: 0;
  }

  .divider {
    display: flex;
    align-items: center;
    text-align: center;
    color: #6C7278;
    font-size: 14px;
    margin: 16px 0;

    &::before,
    &::after {
      content: "";
      flex: 1;
      border-bottom: 1px solid #d9d9d9;
    }

    &:not(:empty)::before {
      margin-right: 16px;
    }

    &:not(:empty)::after {
      margin-left: 16px;
    }
  }
}

.response-group {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 40px;
}

.code-box {
  display: grid;
  width: 64px;
  height: 64px;
  align-items: center;
  border: 1px solid #d9d9d9;
  border-radius: 15px;
  font-size: 24px;
  line-height: 22px;
  font-weight: 400;
  color: #000000;
}

.message-response {
  display: grid;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 16px;

  b {
    color: #000000;
  }
}

::ng-deep .ant-modal {
  .ant-modal-content {
    border-radius: 8px;
  }

  .ant-modal-footer {
    border-top: unset;
  }

  .ant-modal-body {
    span {
      display: flex;
      justify-content: center;
      font-size: 20px;
      font-weight: 600;
    }
  }

  .ant-modal-header {
    border-bottom: unset;
    border-radius: 8px 8px 0 0;
    padding: 24px;

    .content-header {
      span {
        margin-top: 4px;
        font-size: 16px;
        color: #757575;
      }
    }
  }
}