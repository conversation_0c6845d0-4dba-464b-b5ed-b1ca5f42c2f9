::ng-deep .ant-modal {
  .ant-modal-content {
    border-radius: 8px;
  }

  .ant-modal-header {
    border-bottom: unset;
    border-radius: 8px 8px 0 0;
    padding: 24px;

    .content-header {
      h2 {
        font-weight: bold;
        line-height: 29px;
      }

      span {
        margin-top: 4px;
        font-size: 16px;
        color: #757575;
      }
    }
  }

  .ant-modal-footer {
    border-top: unset;
    border-radius: 0 0 8px 8px;
    padding: 0px 24px 24px 24px;
  }
}

.modal-create-invite {
  .modal-add-invite {
    ::ng-deep .ant-form-item {
      .ant-form-item-control-input-content {
        display: grid;
        gap: 8px;
      }
    }

    .select-template {
      ::ng-deep .ant-select-selector {
        min-height: 40px;
        border-radius: 8px;

        .ant-select-selection-item,
        .ant-select-selection-search-input {
          margin: auto;
        }
      }
    }
  }
}

.modal-add-invite {
  label {
    font-size: 16px;
    color: #1E1E1E;
  }

  .form-item {
    ::ng-deep .ant-form-item-control-input-content {
      display: grid;
      gap: 8px;
    }

    ::ng-deep nz-select-top-control.ant-select-selector {
      height: 40px;
      border-radius: 8px;
      align-items: center;
    }
  }

  .select-template {
    ::ng-deep .ant-select-selector {
      min-height: 40px;
      border-radius: 8px;
    }
  }
}

.btn-submit-invite {
  width: 100%;
}