import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-add-edit-user',
  standalone: false,
  templateUrl: './add-edit-user.component.html',
  styleUrl: './add-edit-user.component.less'
})
export class AddEditUserComponent {
  @Input() isVisible = false;
  @Input() isEditMode = false;
  @Input() isDetail = false;
  @Input() orgId: any;
  @Input() listOrgRole: any[] = [];
  @Input() data: any;
  @Input() nameOrg: any;
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<any>();
  form: FormGroup;

  constructor(
    private fb: FormBuilder,
    private message: NzMessageService
  ) {
    this.form = this.fb.group({
      email: ['', this.isEditMode ? [Validators.maxLength(255)] : [Validators.required, Validators.email, Validators.maxLength(255)]],
      organizationrole: [[], [Validators.required]],
      message: ['', [Validators.maxLength(2000)]]
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!Array.isArray(this.listOrgRole)) {
      this.listOrgRole = [];
    }

    if (changes['isEditMode'] || changes['data'] || changes['listOrgRole']) {
      if (this.isEditMode) {
        this.form.get('email')?.disable();
        this.form.get('message')?.disable();
        this.form.get('email')?.clearValidators();
        this.form.get('message')?.clearValidators();
        this.form.get('email')?.setValidators([Validators.maxLength(255)]);
        this.form.get('message')?.setValidators([Validators.maxLength(2000)]);
      } else {
        this.form.get('email')?.enable();
        this.form.get('message')?.enable();
        this.form.get('email')?.setValidators([Validators.required, Validators.email, Validators.maxLength(255)]);
        this.form.get('message')?.setValidators([Validators.maxLength(2000)]);
        if (this.listOrgRole.length > 0) {
          this.form.get('organizationrole')?.setValue([this.listOrgRole[0].value]);
        }
      }
      this.form.get('organizationrole')?.setValidators([Validators.required]);
      this.form.get('email')?.updateValueAndValidity();
      this.form.get('message')?.updateValueAndValidity();
      this.form.get('organizationrole')?.updateValueAndValidity();

      if (this.data && this.isEditMode) {
        if (Array.isArray(this.listOrgRole)) {
          const selectedRole = this.listOrgRole.find(role => role.label === this.data?.orgRole);
          this.form.reset({
            email: this.data?.email,
            organizationrole: selectedRole ? [selectedRole.value] : [],
            message: this.data?.username
          });
        } else {
          console.error('Cannot find role, listOrgRole is not an array:', this.listOrgRole);
          this.form.reset({
            email: this.data?.email,
            organizationrole: [],
            message: this.data?.username
          });
        }
      }
    } else {
      this.form.reset({
        email: '',
        organizationrole: this.listOrgRole[0].value,
        message: ''
      });
    }
  }

  handleSave() {
    if (this.form.valid) {
      const formValue = this.form.value;
      let selectedRole = null;
      if (Array.isArray(this.listOrgRole)) {
        selectedRole = this.listOrgRole.find(role => role.id === formValue.organizationrole[0]);
      }

      const updateUser = {
        email: formValue.email,
        organizationroleId: formValue.organizationrole,
        organizationroleValue: selectedRole ? selectedRole.value : '',
        message: formValue.message,
        roleId: selectedRole ? selectedRole.id : ''
      };
      const createInvitation = {
        email: formValue.email,
        orgId: this.orgId,
        message: formValue.message,
        roleIds: formValue.organizationrole,
        userId: this.data?.userId
      };
      this.confirm.emit(this.isEditMode ? updateUser : createInvitation);
      this.form.reset({
        email: '',
        organizationrole: this.listOrgRole.length > 0 ? [this.listOrgRole[0].value] : [],
        message: ''
      }, { emitEvent: false });
    } else {
      Object.values(this.form.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
      this.message.error('Please fill all required fields');
    }
  }

  handleCancel() {
    this.form.reset({
      email: '',
      organizationrole: this.listOrgRole.length > 0 ? [this.listOrgRole[0].value] : [],
      message: ''
    });
    this.close.emit();
  }
}