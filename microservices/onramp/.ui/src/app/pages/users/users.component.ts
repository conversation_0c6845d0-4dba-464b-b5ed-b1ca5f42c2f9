import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { Users } from '../../core/models/users.model';
import { AccountService } from '../../core/services/account.service';
import { User } from '../../core/services/auth.service';
import { UsersService } from '../../core/services/users.service';

@Component({
  selector: 'app-users',
  standalone: false,
  templateUrl: './users.component.html',
  styleUrl: './users.component.less'
})
export class UsersComponent {
  isTableLoading = false;
  highlightedRowId: string | null = null;
  isModalVisible = false;
  isEditMode = false;
  listDataUsers: Users[] = [];
  listDataInvitations: any[] = [];
  orgRoleOptions: any[] = [];
  orgIdUsers: any;
  confirmModal?: NzModalRef;
  currentUser: Users | null = null;
  hasSynapseManagerUser = false;
  hasSynapseDeleteUser = false;
  getUserId: any;
  nameOrganization: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private modalService: NzModalService,
    private message: NzMessageService,
    private users: UsersService,
    private router: Router,
  ) { }

  async ngOnInit() {
    this.getUserId = localStorage.getItem('permissions');
    this.route.params.subscribe((params: any) => {
      const state = window.history.state;
      this.getUsersList(params.orgId);
      this.getInvitationsList(params.orgId);
      this.getRoleUsers(params.orgId);
      this.orgIdUsers = params.orgId;
      this.nameOrganization = state.orgData?.name || null;
    })
    const storedAllOrganizationsStr = await this.getItemWithDelay('userPermissionsSynapse');
    const storedAllOrganizations = storedAllOrganizationsStr ? JSON.parse(storedAllOrganizationsStr) : [];
    this.hasSynapseManagerUser = Array.isArray(storedAllOrganizations) &&
      (storedAllOrganizations.includes('synapse_manage_synapse_users') || storedAllOrganizations.includes('org_manage_users') || storedAllOrganizations.includes('org_manage_users'));
    this.hasSynapseDeleteUser = Array.isArray(storedAllOrganizations) && storedAllOrganizations.includes('synapse_delete_synapse_users') || storedAllOrganizations.includes('org_delete_users');

  }
  private async getItemWithDelay(key: any): Promise<any | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(localStorage.getItem(key));
      }, 50);
    });
  }

  getOrgName(org: any): string {
    if (typeof org === 'string') {
      return org;
    }
    if (org && typeof org === 'object' && org.name) {
      return org.name;
    }
    return org?.toString() ?? '';
  }
  isArray(value: any): value is any[] {
    return Array.isArray(value);
  }
  onCreateUser() {
    this.isModalVisible = true;
  }

  handleModalSave(value: any) {
    if (!this.isEditMode) {
      const userPermissions = this.getUserId;
      const formValue = {
        email: value.email,
        organizationrole: value.roleIds,
        message: value.message,
        inviterid: userPermissions
      }
      this.users.createInvaitions(this.orgIdUsers, formValue).subscribe((res: any) => {
        this.message.success('Invitation created successfully!');
        this.isModalVisible = false;
        this.isEditMode = false;
        this.getInvitationsList(this.orgIdUsers);
      }, (error) => {
        this.message.error('Failed to create invitation', error);
        this.isModalVisible = false;
        this.isEditMode = false;
      });
    } else {
      const formUser = {
        email: value.email,
        orgId: value.organizationrole,
        message: value.message,
        userId: this.currentUser?.userId,
        roleId: value.organizationroleId
      }
      this.users.updateUser(this.orgIdUsers, formUser).subscribe((res: any) => {
        this.message.success('User updated successfully!');
        this.isModalVisible = false;
        this.isEditMode = false;
        this.getUsersList(this.orgIdUsers);
      }, (error) => {
        this.message.error('Failed to update user', error);
        this.isModalVisible = false;
        this.isEditMode = false;
      });
    }
  }
  getInvitationsList(orgId: any) {
    this.isTableLoading = true;
    this.users.getInvitations(orgId).subscribe((res: any) => {
      this.listDataInvitations = res.data;
      this.isTableLoading = false;
    }, (error) => {
      this.message.error('Failed to get invitation list', error);
      this.isTableLoading = false;
    });
  }
  getUsersList(orgId: any) {
    this.isTableLoading = true;
    this.users.getUsersList(orgId).subscribe((res: any) => {
      this.listDataUsers = res.data;
      this.isTableLoading = false;
    }, (error) => {
      this.message.error('Failed to get user list', error);
      this.isTableLoading = false;
    });
  }
  getRoleUsers(orgId: any) {
    this.users.getRoleUsers(orgId).subscribe((res: any) => {
      this.orgRoleOptions = res.data.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
    });
  }
  handleRevokeInvite(value: any) {
    this.confirmModal = this.modalService.confirm({
      nzTitle: `<b>Delete Confirmation</b>`,
      nzContent: `Are you sure you want to delete this invitation?`,
      nzClassName: 'custom-confirm-modal delete-modal-invitations',
      nzWidth: '500px',
      nzOnOk: () => {
        const valueActor = {
          actor: this.getUserId,
        }
        this.users.deleteInvite(this.orgIdUsers, value, valueActor).subscribe((res: any) => {
          this.message.success('Invitation deleted successfully');
          this.getInvitationsList(this.orgIdUsers);
        }, (error) => {
          this.message.error('Failed to delete invitation', error);
        });
      }
    });
  }
  handleResendInvite(value: any) {
    const formResendInvite = {
      ...value,
      actor: this.getUserId,
    }
    this.users.resendInvite(this.orgIdUsers, formResendInvite).subscribe((res: any) => {
      this.message.success('Invitation resent successfully!');
      this.getInvitationsList(this.orgIdUsers);
    }, (error) => {
      this.message.error('Failed to resend invitation', error);
    });
  }
  handleEdit(value: any) {
    this.isEditMode = true;
    this.isModalVisible = true;
    this.currentUser = value;
  }
  onCloseModal() {
    this.isModalVisible = false;
    this.isEditMode = false;
    this.currentUser = null;
  }
  handleDelete(value: any) {
    this.confirmModal = this.modalService.confirm({
      nzTitle: `<b>Delete Confirmation</b>`,
      nzContent: `Are you sure you want to remove <b>${value.userName}</b> from this Organization?`,
      nzClassName: 'custom-confirm-modal',
      nzWidth: '500px',
      nzOnOk: () => {
        this.users.deleteUser(this.orgIdUsers, value.userId).subscribe((res: any) => {
          this.message.success('User deleted successfully');
          this.getUsersList(this.orgIdUsers);
        }, (error) => {
          this.message.error('Failed to delete user', error);
        });
      }
    });
  }
  handleBackOrganization() {
    const value = history.state.orgData;
    this.router.navigate(['/organizations']);
  }
}
