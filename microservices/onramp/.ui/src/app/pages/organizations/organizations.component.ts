import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OrganizationsService } from '../../core/services/organization.service';
import { Organization } from '../../core/models/organizations.model';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { AuthService } from '../../core/services/auth.service';

@Component({
  selector: 'app-organizations',
  templateUrl: './organizations.component.html',
  standalone: false,
  styleUrl: './organizations.component.less'
})
export class OrganizationsComponent {
  isModalVisible = false;
  isEditMode = false;
  isDetailOrg = false;
  selectedData = null;
  listOfData: Organization[] = [];
  filteredList: Organization[] = [];
  searchTerm: string = '';
  currentOEM: Organization | null = null;
  editIndex: number | null = null;
  isTableLoading = false;
  organizationFilter: string | null = null;
  highlightedRowId: string | null = null;
  confirmModal?: NzModalRef;
  hasSynapseManagerAllOrganizations = false;
  hasSynapseDeleteAllOrganizations = false;

  constructor(
    private organizationsService: OrganizationsService,
    private router: Router,
    private route: ActivatedRoute,
    private message: NzMessageService,
    private modal: NzModalService,
    private authService: AuthService,
  ) { }

  async ngOnInit() {
    this.getListOrganization();
    // demo call permissions group, but not used right now
    // this.getPermissionsGroup('692d620e-7dbf-5e7b-bb66-0167c3498f51');
    this.route.queryParams.subscribe(params => {
      if (params) {
        this.searchTerm = params['organizationId'];
        this.organizationFilter = params['organizationId'] || null;
      }
    });
    const storedAllOrganizationsStr = await this.getItemWithDelay('organizationPermissionsSynapse');
    const cachedOrgPermissions = storedAllOrganizationsStr ? JSON.parse(storedAllOrganizationsStr) : [];
    this.hasSynapseManagerAllOrganizations = cachedOrgPermissions.includes('synapse_manage_organizations');
    this.hasSynapseDeleteAllOrganizations = cachedOrgPermissions.includes('synapse_delete_organizations');
  }
  private async getItemWithDelay(key: any): Promise<any | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(localStorage.getItem(key));
      }, 50);
    });
  }

  getPermissionsGroup(id: string) {
    this.isTableLoading = true;
    this.organizationsService.getOrganizationsGroup(id).subscribe((data: any) => {
      console.log('Permissions Group Data:', data);
    },
      (error) => {
        console.error('Error fetching Organizations group:', error);
        this.isTableLoading = false;
        this.listOfData = [];
        this.filteredList = [];
      }
    );
  }

  getListOrganization() {
    this.isTableLoading = true;
    this.organizationsService.getOrganizations().subscribe((data: any) => {
      this.listOfData = data.data;
      this.filteredList = [...this.listOfData];
      this.isTableLoading = false;
      this.searchTable();
    },
      (error) => {
        console.error('Error fetching Organizations list:', error);
        this.isTableLoading = false;
        this.listOfData = [];
        this.filteredList = [];
      }
    );
  }
  searchTable() {
    this.filteredList = [...this.listOfData];
    if (this.searchTerm) {
      this.filteredList = this.listOfData.filter(item =>
        item.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        item.id.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }
    if (this.organizationFilter) {
      this.filteredList = this.filteredList.filter(org =>
        org.id === this.organizationFilter,
      );
    }
  }

  openAddPopup() {
    this.isModalVisible = true;
    this.selectedData = null;
    this.isEditMode = false;
  }

  openEditPopup(index: number) {
    this.isModalVisible = true;
    this.isEditMode = true;
    this.editIndex = index;
    this.currentOEM = { ...this.listOfData[index] };
  }
  selectOrganization(value: any) {
    this.authService.permissions$.subscribe({
      next: (response: any) => {
        // You can try orgPermissions to be able to add temporary menu when condition is not met
        const orgPermissions = response?.data?.permissions?.filter((perm: any) => perm.organizationId) || [];
        // const orgPermissions = response?.data?.permissions?.filter((perm: any) => perm.organizationId === value.id) || [];
        const hasAccess = orgPermissions.some((perm: any) => {
          perm.permissions.includes('synapse_view_organizations') || perm.permissions.includes('synapse_manage_organizations')
        }
        );
        // And this
        if (hasAccess) {
          // if (!hasAccess) {
          this.message.create('error', 'You do not have access to this organization.', { nzDuration: 5000 });
          return;
        }
        this.organizationsService.selectOrganization(value);
        this.router.navigate(['/overview'], {
          queryParams: { organizationId: value.id },
          state: { orgData: value }
        });
      },
      error: () => {
        this.message.create('error', 'Failed to check permissions.', { nzDuration: 5000 });
      }
    });
  }

  closeModal() {
    this.isModalVisible = false;
    this.selectedData = null;
    this.isEditMode = false;
    this.isDetailOrg = false;
    this.currentOEM = null;
  }

  handleModalSave(data: any) {
    if (this.isEditMode && this.editIndex !== null && this.currentOEM) {
      this.handleUpdateOrganization(data);
    } else {
      this.handleCreateOrganization(data);
    }
    this.filteredList = [...this.listOfData];
    this.isModalVisible = false;
    this.editIndex = null;
    this.currentOEM = null;
  }

  handleCreateOrganization(data: any) {
    this.isTableLoading = true;
    this.organizationsService.createOrganization(data).subscribe((item: any) => {
      this.message.create('success', 'Record created successfully!');
      this.getListOrganization();
      this.highlightedRowId = item.data.id;
      this.isTableLoading = false;
      this.listOfData = [...this.listOfData, item.data];
      setTimeout(() => {
        this.highlightedRowId = null;
      }, 3000);
    }, (error) => {
      console.error('Error creating Organization:', error);
      this.message.create('error', 'Failed to create record. Please try again.');
      this.highlightedRowId = null;
      this.isTableLoading = false;
    })
  }

  handleUpdateOrganization(data: any) {
    this.isTableLoading = true;
    const orgId = this.currentOEM?.id;
    if (!orgId) {
      this.message.create('error', 'Organization ID not found.');
      this.isTableLoading = false;
      return;
    }

    this.organizationsService.updateOrganization(orgId, data).subscribe(() => {
      this.message.create('success', 'Record updated successfully!');
      this.getListOrganization();
      this.isTableLoading = false;
    }, (error) => {
      console.error('Error updating Organization:', error);
      this.message.create('error', 'Failed to update record. Please try again.');
      this.isTableLoading = false;
    })
  }
  handleDeleteOnModal(data: any) {
    this.handleDeleteOrg(data);
    this.isModalVisible = false;
  }

  handleDeleteOrg(data: any) {
    this.confirmModal = this.modal.confirm({
      nzTitle: `<b>Delete Confirmation</b>`,
      nzContent: `Are you sure you want to delete the <b>${data.name}</b>?`,
      nzClassName: 'custom-confirm-modal',
      nzWidth: '500px',
      nzOnOk: () =>
        new Promise<void>((resolve, reject) => {
          this.isTableLoading = true;
          this.organizationsService.deleteOrganization(data.id).subscribe({
            next: () => {
              this.listOfData = this.listOfData.filter(item => item.id !== data.id);
              this.message.success('Record deleted successfully!');
              this.getListOrganization();
              this.isTableLoading = false;
              resolve();
            },
            error: (error) => {
              this.message.error('Failed to delete record. Please try again.');
              console.error('Delete failed:', error);
              this.isTableLoading = false;
              reject();
            }
          })
        }).catch(() => {
          this.message.error('Failed to delete record. Please try again.');
          this.isTableLoading = false;
        })
    });
  }

  onClickPermissions(value: any) {
    this.router.navigate([`/organization/${value.id}/permissions`], {
      state: { orgData: value }
    });
  }

  onClickUsers(value: any) {
    this.router.navigate([`/organization/${value.id}/users`], {
      state: { orgData: value }
    });
  }
}
