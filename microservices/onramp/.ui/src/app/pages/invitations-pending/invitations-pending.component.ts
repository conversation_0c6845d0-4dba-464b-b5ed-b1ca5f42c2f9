import { Component } from '@angular/core';
import { InvitationsPending } from '../../core/models/account-authen.model';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AccountService } from '../../core/services/account.service';
import { OrganizationsService } from '../../core/services/organization.service';

@Component({
  selector: 'app-invitations-pending',
  standalone: false,
  templateUrl: './invitations-pending.component.html',
  styleUrl: './invitations-pending.component.less'
})
export class InvitationsPendingComponent {
  isTableLoading = false;
  listDataInvitations: InvitationsPending[] = []
  highlightedRowId: string | null = null;
  userId: string | null = null;
  listOrganizations: any[] = [];
  confirmModal?: NzModalRef;

  constructor(
    private message: NzMessageService,
    private account: AccountService,
    private modalService: NzModalService,
    private organizationsService: OrganizationsService,
  ) { }
  ngOnInit() {
    this.getListOrganizations();
    this.userId = localStorage.getItem('permissions');
  }
  getListOrganizations() {
    this.organizationsService.getOrganizations().subscribe((res: any) => {
      this.listOrganizations = res.data;
      this.getListInvitations(this.userId);
    }, (error) => {
      this.message.error('Failed to load organizations', error);
    });
  }
  getListInvitations(orgId: any) {
    this.isTableLoading = true;
    this.account.getInvitations(orgId).subscribe((res: any) => {
      this.listDataInvitations = res.data.map((invite: InvitationsPending) => {
        const org = this.listOrganizations.find(org => org.id === invite.organizationidentifier);
        return {
          ...invite,
          nameOrg: org ? org.name : 'Unknown Organization'
        };
      });
      this.isTableLoading = false;
    }, (error) => {
      this.isTableLoading = false;
      this.message.error('Failed to load invitations', error);
    });
  }
  capitalize(value: string): string {
    if (!value) return value;
    return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
  }
  handleAccept(value: any) {
    this.confirmModal = this.modalService.confirm({
      nzTitle: '<b>Confirm Accept Invitation</b>',
      nzContent: `Are you sure you want to accept the invitation for <b>${value.email}</b> to join <b>${value.nameOrg}</b>?`,
      nzClassName: 'confirm-modal',
      nzOkText: 'Confirm',
      nzWidth: '500px',
      nzOnOk: () => {
        this.confirmAccept(value);
      }
    });
  }
  confirmAccept(value: any) {
    this.isTableLoading = true;
    this.account.acceptInvitations(this.userId, value.id, value).subscribe((res: any) => {
      this.message.success('Invitation Accept successfully');
      this.getListInvitations(this.userId);
      this.isTableLoading = false;
    }, (error) => {
      this.message.error('Failed to Accept invitation', error);
      this.isTableLoading = false;
    });
  }
  handleDelete(value: any) {
    this.confirmModal = this.modalService.confirm({
      nzTitle: `<b>Delete Confirmation</b>`,
      nzContent: `Are you sure you want to delete the <b>${value.email}</b>?`,
      nzClassName: 'custom-confirm-modal',
      nzWidth: '500px',
      nzOnOk: () => {
        this.confirmDelete(value);
      }
    });
  }
  confirmDelete(value: any) {
    this.isTableLoading = true;
    this.account.deleteInvitations(this.userId, value.id).subscribe((res: any) => {
      this.message.success('Invitation deleted successfully');
      this.getListInvitations(this.userId);
      this.isTableLoading = false;
    }, (error) => {
      this.isTableLoading = false;
      this.message.error('Failed to delete invitation', error);
    });
  }
}
