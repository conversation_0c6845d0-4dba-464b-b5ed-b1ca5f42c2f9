.modal-create-role {
  .content-header {
    display: grid;
    gap: 4px;
  }

  .modal-add {
    label {
      font-size: 16px;
      color: #1E1E1E;
    }

    ::ng-deep .ant-form-item {
      .ant-form-item-control-input-content {
        display: grid;
        gap: 8px;
      }
    }
  }
}

.modal-add .select-template ::ng-deep .ant-select-selector {
  height: 40px;
  border-radius: 8px;
}

::ng-deep .ant-modal {
  .ant-modal-content {
    border-radius: 8px;
  }

  .ant-modal-header {
    border-bottom: unset;
    border-radius: 8px 8px 0 0;
    padding: 24px;

    .content-header {
      h2 {
        font-weight: bold;
        line-height: 29px;
      }

      span {
        margin-top: 4px;
        font-size: 16px;
        color: #757575;
      }
    }
  }

  .ant-modal-footer {
    border-top: unset;
    border-radius: 0 0 8px 8px;
    padding: 0px 24px 24px 24px;

    .btn-submit-role {
      width: 100%;
    }
  }
}