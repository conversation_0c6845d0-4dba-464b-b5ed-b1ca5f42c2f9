import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Devices } from '../../../core/models/devices.model';
import * as Papa from 'papaparse';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzUploadChangeParam, NzUploadFile } from 'ng-zorro-antd/upload';

@Component({
  selector: 'app-import-device',
  standalone: false,
  templateUrl: './import-device.component.html',
  styleUrl: './import-device.component.less'
})
export class ImportDeviceComponent {
  @Input() isVisible = false;
  @Input() isEditMode = false;
  @Input() data: Devices | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<any>();
  fileName: string = '';
  errorMessage: string = '';
  isAllValid: boolean = false;
  fileList: NzUploadFile[] = [];
  parsedData: any[] = [];
  isLoading: boolean = false;

  constructor(
    private message: NzMessageService
  ) { }
  getTagColor(status: string): string {
    return status === '1' ? 'green' : 'red';
  }
  getTagText(status: string): string {
    return status === '1' ? 'Enabled' : 'Disabled';
  }

  ngOnInit() {
  }
  formatDecimal(coord: string): string {
    return parseFloat(coord).toFixed(6);
  }
  // Convert from decimal to degrees-minutes-seconds
  convertToDMS(decimal: any): any {
    const absDeg = Math.floor(Math.abs(decimal));
    const minutes = Math.floor((Math.abs(decimal) - absDeg) * 60);
    const seconds = ((Math.abs(decimal) - absDeg - minutes / 60) * 3600).toFixed(2);
    const direction = decimal >= 0 ? (decimal <= 90 ? 'N' : 'E') : (decimal >= -90 ? 'S' : 'W');
    return `${absDeg}°${minutes}'${seconds}"${direction}`;
  }
  beforeUpload = (file: NzUploadFile, fileList: NzUploadFile[]): boolean => {
    if (!file || !file.name.endsWith('.csv')) {
      this.message.error('Please upload a valid .csv file!');
      return false;
    }
    const nzFile: NzUploadFile = {
      uid: file.uid || Date.now().toString(),
      name: file.name,
      status: file.status || 'uploading',
      response: file.response || 'Pending',
      size: file.size,
      type: file.type,
      originFileObj: file as any
    };
    this.fileList = [nzFile];
    if (file instanceof File) {
      this.importCsvFile(file as File);
    } else {
      this.message.error('File object is not accessible!');
    }
    return false;
  };
  handleChange({ file, fileList }: NzUploadChangeParam) {
    const status = file.status;
    if (status === 'done') {
      this.message.success(`${file.name} uploaded successfully.`);
      if (file && file.name.endsWith('.csv')) {
        if (file.originFileObj) {
          this.importCsvFile(file.originFileObj as File);
        } else if (file instanceof File) {
          this.importCsvFile(file as File);
        }
      }
    } else if (status === 'removed') {
      this.handleRemove();
    } else if (status === 'error') {
      this.message.error(`${file.name} upload failed.`);
    }
  }
  importCsvFile(file: File) {
    this.isLoading = true;
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (result) => {
        this.parsedData = result.data as any[];
        this.parsedData = this.parsedData.map(row => ({
          devicesId: row.devicesId || '',
          gatewayId: row.gatewayId || '',
          latitude: row.latitude || '0.0',
          longitude: row.longitude || '0.0',
          iPAddress: row.iPAddress || '',
          port: row.port || '',
          flushConnectionMs: row.flushConnectionMs || '0',
          enableRealtime: row.enableRealtime,
          isEnabled: row.isEnabled || '0',
          name: row.name || '',
          type: row.type || ''
        }));
        this.isAllValid = true;
        this.isLoading = false;
        this.message.success(`Imported ${this.parsedData.length} records successfully!`);
      },
      error: (error) => {
        this.isLoading = false;
        this.isAllValid = false;
        this.message.error('Failed to parse CSV file!');
      }
    });
  }
  handleRemove() {
    this.fileList = [];
    this.parsedData = [];
    this.isAllValid = false;
    this.message.info('File removed');
  };

  hanldeSave() {
    if (this.isAllValid) {
      this.confirm.emit(this.parsedData);
      this.clearData();
    }
  }
  clearData() {
    this.fileList = [];
    this.parsedData = [];
    this.isAllValid = false;
  }
  handleCancel() {
    this.close.emit();
    this.clearData();
  }
}
