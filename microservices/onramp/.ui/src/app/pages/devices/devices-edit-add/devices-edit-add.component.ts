import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Devices } from '../../../core/models/devices.model';

@Component({
  selector: 'app-devices-edit-add',
  standalone: false,
  templateUrl: './devices-edit-add.component.html',
  styleUrl: './devices-edit-add.component.less'
})
export class DevicesEditAddComponent {
  @Input() isVisible = false;
  @Input() isEditMode = false;
  @Input() data: Devices | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<any>();
  @Output() loadingChange = new EventEmitter<boolean>();
  form: FormGroup;
  listOrganizations: any[] = [];

  private ipPattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  private portPattern = /^([0-9]{1,5})$/;

  constructor(
    private fb: FormBuilder
  ) {
    this.form = this.fb.group({
      devicesId: [{ value: '', disabled: true }],
      name: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]],
      latitude: ['', [
        Validators.required,
        Validators.maxLength(20)
      ]],
      longitude: ['', [
        Validators.required,
        Validators.maxLength(20)
      ]],
      iPAddress: ['', [
        Validators.required,
        Validators.pattern(this.ipPattern),
        Validators.maxLength(20)
      ]],
      port: ['', [
        Validators.required,
        Validators.pattern(this.portPattern),
        Validators.min(0),
        Validators.max(65535)
      ]],
      flushConnectionMs: ['', [
        Validators.required,
        Validators.pattern(/^\d+$/),
        Validators.maxLength(5)
      ]],
      enableRealtime: ['', [
        Validators.required,
        Validators.maxLength(5)
      ]],
      isEnabled: [true],
    });
  }
  ngOnInit() {
  }

  ngOnChanges() {
    if (this.isEditMode && this.data) {
      this.form.patchValue({
        devicesId: this.data.devicesId || '',
        name: this.data.name || '',
        latitude: this.data.latitude || '',
        longitude: this.data.longitude || '',
        iPAddress: this.data.iPAddress || '',
        port: this.data.port || '',
        flushConnectionMs: this.data.flushConnectionMs || '',
        enableRealtime: this.data.enableRealtime,
        isEnabled: this.data.isEnabled === '1' ? true : false,
      }, { emitEvent: false });
      this.form.updateValueAndValidity();
    } else {
      this.form.reset({
        devicesId: '',
        name: '',
        latitude: '',
        longitude: '',
        iPAddress: '',
        port: '',
        flushConnectionMs: '',
        enableRealtime: true,
        isEnabled: true,
      }, { emitEvent: false });
      this.form.updateValueAndValidity();
    }
  }
  handleSave() {
    if (this.form.valid) {
      const formValue = this.form.value;
      const statusValue = this.form.value.isEnabled ? '1' : '0';
      const saveData = this.isEditMode ? { ...formValue, isEnabled: statusValue } :
        { ...formValue, isEnabled: statusValue };
      this.loadingChange.emit(true);
      this.confirm.emit(saveData);
      this.form.reset();
      setTimeout(() => {
        this.loadingChange.emit(false);
      }, 500);
    } else {
      Object.values(this.form.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  handleCancel() {
    this.form.reset();
    this.close.emit();
  }
}
