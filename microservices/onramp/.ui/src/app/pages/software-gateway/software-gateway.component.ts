import { Component, OnInit, SimpleChanges } from '@angular/core';
import { GatewayService } from '../../core/services/gateway.service';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { SoftwareGateway } from '../../core/models/software-gateway.model';
import { NzTableSortFn, NzTableSortOrder } from 'ng-zorro-antd/table';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { OrganizationsService } from '../../core/services/organization.service';

@Component({
  selector: 'app-software-gateway',
  standalone: false,
  templateUrl: './software-gateway.component.html',
  styleUrl: './software-gateway.component.less'
})
export class SoftwareGatewayComponent implements OnInit {
  listOfData: SoftwareGateway[] = [];
  filteredList: SoftwareGateway[] = [];
  searchTerm: string = '';
  isVisible = false;
  isEditMode = false;
  editIndex: number | null = null;
  currentGateway: SoftwareGateway | null = null;
  isTableLoading = false;
  currentSortKey: keyof SoftwareGateway | null = null;
  sortOrder: NzTableSortOrder | null = null;
  organizationFilter: string | null = null;
  nameOrganization: string | null = null;
  organizationId: string | null = null;
  confirmModal?: NzModalRef;
  showBtnReset = false;

  constructor(
    private gatewayService: GatewayService,
    private router: Router,
    private route: ActivatedRoute,
    private message: NzMessageService,
    private modalService: NzModalService
  ) { }

  ngOnInit() {
    this.route.params.subscribe((params: any) => {
      const state = window.history.state;
      if (state.orgData) {
        this.loadGatewayList(params.orgId);
        this.nameOrganization = state.orgData?.name || null;
        this.organizationId = state.orgData?.id || null;
      }
    })
    const storedShowBtnReset = localStorage.getItem('gatewayPermissions');
    const storedAllOrganizations = storedShowBtnReset ? JSON.parse(storedShowBtnReset) : [];
    this.showBtnReset = Array.isArray(storedAllOrganizations) && storedAllOrganizations.includes('synapse_manage_organizations');
  }
  getTagColor(status: any): string {
    return status === true ? 'green' : 'red';
  }
  getTagText(status: any): string {
    return status === true ? 'Yes' : 'No';
  }
  loadGatewayList(orgId: any) {
    this.isTableLoading = true;
    this.gatewayService.getGateway(orgId).subscribe(
      (res: any) => {
        this.listOfData = res.data;
        this.isTableLoading = false;
      },
      (error) => {
        console.error('Error fetching Gateway list:', error);
        this.isTableLoading = false;
      }
    );
  }
  handleBackOverviewGateway() {
    const value = window.history.state.orgData;
    this.router.navigate([`/organization/${value.id}/overview`], {
      state: { orgData: value }
    });
  }

  sortData(): void {
    if (!this.currentSortKey || !this.sortOrder) return;
    if (!this.currentSortKey) return;
    this.filteredList.sort((a, b) => {
      const valueA = a[this.currentSortKey as keyof SoftwareGateway];
      const valueB = b[this.currentSortKey as keyof SoftwareGateway];
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return this.sortOrder === 'ascend'
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA);
      } else if (typeof valueA === 'number' && typeof valueB === 'number') {
        return this.sortOrder === 'ascend' ? valueA - valueB : valueB - valueA;
      }
      return 0;
    });
  }

  onColumnClick(key: keyof SoftwareGateway): void {
    this.currentSortKey = key;
    if (this.sortOrder === null) {
      this.sortOrder = 'ascend';
    } else {
      this.sortOrder = this.sortOrder === 'ascend' ? 'descend' : null;
    }
  }

  showModal(): void {
    this.isEditMode = false;
    this.currentGateway = null;
    this.isVisible = true;
  }

  editGateway(index: number): void {
    this.isEditMode = true;
    this.editIndex = index;
    this.currentGateway = { ...this.listOfData[index] };
    this.isVisible = true;
  }

  handleModalSave(data: SoftwareGateway): void {
    if (this.isEditMode && this.editIndex !== null && this.currentGateway) {
      const updatedGateway = { ...this.currentGateway, ...data };
      // this.listOfData[this.editIndex] = updatedGateway;
      this.gatewayService.updateGateway(updatedGateway).subscribe((res: any) => {
        this.message.success('Gateway updated successfully!');
        this.loadGatewayList(this.organizationId);
      }, (error: any) => {
        this.message.error('Failed to updating gateway', error);
        console.error('Error updating gateway:', error);
      });
    } else {
      const formData = {
        name: data.name,
        description: data.description,
        machinekey: data.machinekey,
      };
      this.gatewayService.createGateway(formData, this.organizationId).subscribe((res: any) => {
        this.message.success('Gateway created successfully!');
        this.loadGatewayList(this.organizationId);
      }, (error: any) => {
        this.message.error('Failed to creating gateway', error);
        console.error('Error creating gateway:', error);
      });
    }
    this.filteredList = [...this.listOfData];
    this.isVisible = false;
    this.editIndex = null;
    this.currentGateway = null;
  }
  handleModalDelete(value: any) {
    this.confirmModal = this.modalService.confirm({
      nzTitle: `<b>Delete Confirmation</b>`,
      nzContent: `Are you sure you want to delete this Gateway?`,
      nzClassName: 'custom-confirm-modal delete-modal-gateway',
      nzWidth: '500px',
      nzOnOk: () => {
        this.gatewayService.deleteGateway(this.organizationId, value.id).subscribe((res: any) => {
          this.message.success('Gateway deleted successfully');
          this.loadGatewayList(this.organizationId);
          this.handleModalClose();
        }, (error: any) => {
          this.message.error('Failed to delete Gateway', error);
        });
      }
    });
  }
  handleGenerate(value: any) {
    this.gatewayService.resetToken(this.organizationId, value.id).subscribe((res: any) => {
      this.message.success('Token reset successfully');
      this.loadGatewayList(this.organizationId);
      this.handleModalClose();
    }, (error: any) => {
      this.message.error('Failed to reset token', error);
    });

  }

  handleModalClose(): void {
    this.isVisible = false;
    this.editIndex = null;
    this.currentGateway = null;
  }

  handleLoadingChange(isLoading: boolean): void {
    this.isTableLoading = isLoading;
  }

  onClickOrganizations(value: any): void {
    this.router.navigate(['/organizations'], {
      queryParams: { organizationIdentifier: value }
    });
  }
  handleConfig(value: any) {
    this.router.navigate(['/software-gateway-config'], {
      queryParams: { gateWayId: value.orgId, name: value.name }
    });
  }
  // handleDevices(value: any) {
  //   this.router.navigate(['/devices'], {
  //     queryParams: { gatewayId: value.machinekey, name: value.name }
  //   });
  // }
}
