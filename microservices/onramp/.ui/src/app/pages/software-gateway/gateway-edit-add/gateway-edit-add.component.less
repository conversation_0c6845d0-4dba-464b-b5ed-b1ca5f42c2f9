.modal-create-gateway {
  .modal-add-gateway .select-item ::ng-deep .ant-select-selector {
    height: 40px;
    border-radius: 8px;
  }

  .modal-add-gateway label {
    font-weight: 400;
    font-size: 16px;
  }
}

.btn-isCreate {
  padding-bottom: 10px;
}

.btn-submit-gateway {
  width: 100%;
}

.btn-footer {
  border-top: 1px solid #E8E8E8;
  padding-top: 10px;
  display: flex;
  justify-content: space-between;
}

.btn-footer .btn-delete,
.btn-footer .btn-generate {
  width: 100%;
}

::ng-deep .ant-modal .ant-modal-header .content-header span {
  margin-top: 4px;
  font-size: 16px;
  color: #757575;
}

::ng-deep .ant-modal .ant-modal-header {
  border-bottom: unset;
  border-radius: 8px 8px 0 0;
  padding: 24px;
}

::ng-deep .ant-modal .ant-modal-header .content-header,
.modal-add-gateway .form-item ::ng-deep .ant-form-item-control-input-content {
  display: grid;
  gap: 8px;
}

.form-item ::ng-deep nz-select-top-control.ant-select-selector {
  height: 40px;
  border-radius: 8px;
  align-items: center;
}

::ng-deep .ant-modal-content {
  border-radius: 8px;
}

::ng-deep .ant-modal .ant-modal-footer {
  border-top: unset;
  border-radius: 0 0 8px 8px;
  padding: 0px 24px 24px 24px;
}