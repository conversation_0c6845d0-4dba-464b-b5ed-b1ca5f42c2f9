import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Organization, SoftwareGateway } from '../../../core/models/software-gateway.model';
import { GatewayService } from '../../../core/services/gateway.service';

@Component({
  selector: 'app-gateway-edit-add',
  standalone: false,
  templateUrl: './gateway-edit-add.component.html',
  styleUrl: './gateway-edit-add.component.less'
})
export class GatewayEditAddComponent {
  @Input() isVisible = false;
  @Input() isEditMode = false;
  @Input() data: SoftwareGateway | null = null;
  @Input() nameOrg: any;
  @Input() showBtnReset = false;
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<any>();
  @Output() delete = new EventEmitter<any>();
  @Output() generateAPIkey = new EventEmitter<any>();
  @Output() loadingChange = new EventEmitter<boolean>();
  form: FormGroup;
  listOrganizations: Organization[] = [];

  constructor(
    private gatewayService: GatewayService,
    private fb: FormBuilder
  ) {
    this.form = this.fb.group({
      name: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]],
      isenabled: [true],
      description: ['', [Validators.maxLength(255)]],
      machinekey: [''],
    });
  }
  ngOnInit() {
  }

  ngOnChanges() {
    if (this.isEditMode && this.data) {
      this.form.patchValue({
        machinekey: this.data.machinekey || '',
        description: this.data.description || '',
        name: this.data.name || '',
        isenabled: this.data.isenabled,
        orgId: this.data.orgId,
      }, { emitEvent: false });
    } else {
      this.form.reset({
        gatewayId: '',
        apiKey: '',
        name: '',
        isenabled: true,
        machinekey: ''
      });
    }
  }
  getOrganizations() {
    this.gatewayService.getOrganizations().subscribe(
      (data) => {
        this.listOrganizations = data;
      },
      (error) => {
        console.error('Error fetching Organizations list:', error);
      }
    );
  }

  handleSave() {
    if (this.form.valid) {
      const formValue = this.form.value;
      const saveData = { ...formValue }
      this.loadingChange.emit(true);
      this.confirm.emit(saveData);
      this.form.reset();
      setTimeout(() => {
        this.loadingChange.emit(false);
      }, 500);
    } else {
      Object.values(this.form.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  handleResetToken() {
    this.generateAPIkey.emit(this.data);
  }
  handleDelete() {
    this.delete.emit(this.data)
  }

  handleCancel() {
    this.form.reset();
    this.close.emit();
  }
}
