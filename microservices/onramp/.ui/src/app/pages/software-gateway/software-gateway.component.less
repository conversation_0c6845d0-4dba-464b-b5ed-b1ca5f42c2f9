.software-container {
  width: 100%;
  display: contents;
  margin: auto;
  margin: 0 20px;

  .title-software {
    h1 {
      margin-bottom: unset;
    }
  }

  .form-breadcrumb {
    ::ng-deep .ant-breadcrumb {
      line-height: 26px;
      margin-bottom: 16px;
      font-size: 16px;
    }
  }

  .gr-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .page-description-software {
    display: flex;
    height: 42px;
    border-top: 1px solid gray;
    border-bottom: 1px solid gray;
    align-items: center;
    padding: 10px 0;
  }

  .truncate-input {
    width: 200px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    display: inline-block;
  }

  .title-oem {
    h1 {
      text-align: center;
      margin-bottom: 20px;
    }
  }

  .form-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    gap: 10px;
  }
}