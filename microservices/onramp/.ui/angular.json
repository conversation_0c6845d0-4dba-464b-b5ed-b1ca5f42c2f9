{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"onramp-ui": {"projectType": "application", "schematics": {"@schematics/angular:component": {"standalone": false}, "@schematics/angular:directive": {"standalone": false}, "@schematics/angular:pipe": {"standalone": false}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/onramp", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "public", "output": "/"}], "styles": ["src/styles.less"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "bundle", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "8kB", "maximumError": "12kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "onramp-ui:build:production"}, "development": {"buildTarget": "onramp-ui:build:development"}}, "defaultConfiguration": "development", "options": {"proxyConfig": "proxy.conf.json"}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.less"], "scripts": []}}}}}, "cli": {"analytics": false}}