{"name": "onramp-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build:prod": "ng build --configuration production", "build": "npm run build:prod", "watch": "ng build --watch", "test": "ng test"}, "private": true, "dependencies": {"@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "ng-zorro-antd": "^19.3.0", "papaparse": "^5.5.3", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.11", "@angular/cli": "^19.2.11", "@angular/compiler-cli": "^19.2.0", "@types/jasmine": "~5.1.0", "@types/papaparse": "^5.3.16", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "less": "^4.4.2", "less-loader": "^12.3.0", "typescript": "~5.7.2"}}