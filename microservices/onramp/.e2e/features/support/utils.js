const { By, until, Key } = require('selenium-webdriver');

async function waitForPageLoad(driver, timeout = 15000) {
  await driver.wait(async () => {
    const readyState = await driver.executeScript('return document.readyState');
    return readyState === 'complete';
  }, timeout, 'Page did not finish loading');
}

async function retry(action, attempts = 3, delay = 500) {
  let lastErr;
  for (let i = 0; i < attempts; i++) {
    try {
      return await action();
    } catch (err) {
      lastErr = err;
      if (i < attempts - 1) await new Promise(r => setTimeout(r, delay));
    }
  }
  throw lastErr;
}

async function retryOnStaleElement(driver, action, maxAttempts = 3, delay = 500) {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await action();
    } catch (error) {
      if (error.name === 'StaleElementReferenceError' && attempt < maxAttempts) {
        await driver.sleep(delay);
      } else {
        throw error;
      }
    }
  }
}

async function closeAllModals(driver) {
  const modals = await driver.findElements(By.css('.ant-modal-close'));
  for (const modal of modals) {
    try {
      await modal.click();
      await driver.sleep(200);
    } catch (e) {
      if (e.name !== 'StaleElementReferenceError') {
        console.warn('⚠️ Error closing modal:', e.message);
      }
    }
  }
}

async function safeClick(driver, locatorOrElement, timeout = 15000) {
  if (locatorOrElement.constructor && locatorOrElement.constructor.name === 'WebElement') {
    const el = locatorOrElement;
    await driver.wait(until.elementIsVisible(el), 5000);
    await driver.wait(until.elementIsEnabled(el), 5000);
    try {
      await el.click();
      return;
    } catch (e) {
      await driver.executeScript('arguments[0].click();', el);
      return;
    }
  } else {
    const locator = locatorOrElement;
    await retryOnStaleElement(driver, async () => {
      const el = await driver.wait(until.elementLocated(locator), timeout);
      await driver.wait(until.elementIsVisible(el), 5000);
      await driver.wait(until.elementIsEnabled(el), 5000);
      await driver.executeScript('arguments[0].scrollIntoView({block:"center"})', el);
      try { await el.click(); } catch (e) { await driver.executeScript('arguments[0].click();', el); }
    }, 3);
  }
}
async function waitForModal(driver, timeout = 15000) {
  const modal = await driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), timeout);
  await driver.wait(until.elementIsVisible(modal), timeout);
  return modal;
}

module.exports = { waitForPageLoad, closeAllModals, retry, retryOnStaleElement, safeClick, waitForModal };
