const { Given, When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');
const { waitForPageLoad, closeAllModals, retryOnStaleElement } = require('../support/utils');

Given('I am on the home page', async function () {
  const baseUrl = process.env.BASE_URL || 'http://localhost:4200';
  await this.driver.get(`${baseUrl}/`);
  await waitForPageLoad(this.driver, 15000, 'div.ant-modal-confirm.custom-login-modal');
});

Then('I should see the login page with the title {string} in an h2 tag', async function (expectedTitle) {
  await waitForPageLoad(this.driver, 15000);
  const titleElement = await this.driver.wait(
    until.elementLocated(By.css('h2')),
    15000,
    `Timeout waiting for login page title "${expectedTitle}"`
  );
  await this.driver.wait(until.elementIsVisible(titleElement), 3000);
});

When('I click the {string} button with id {string}', async function (buttonText, id) {
  try {
    await waitForPageLoad(this.driver, 15000);
    await closeAllModals(this.driver);
    await retryOnStaleElement(this.driver, async () => {
      const btn = await this.driver.wait(until.elementLocated(By.id(id)), 20000, `Timeout waiting for button "${buttonText}" with id "${id}"`);

      await this.driver.wait(until.elementIsVisible(btn), 5000);
      await this.driver.wait(until.elementIsEnabled(btn), 5000);
      await this.driver.executeScript('arguments[0].scrollIntoView(true);', btn);

      await this.driver.sleep(500);

      const btnText = await btn.getText();
      if (!btnText.toLowerCase().includes(buttonText.toLowerCase())) {
        throw new Error(`Expected button text "${buttonText}", but found "${btnText}"`);
      }

      try {
        await btn.click();
      } catch (clickErr) {
        if (clickErr.name === 'ElementClickInterceptedError') {
          await this.driver.executeScript('arguments[0].click();', btn);
        } else {
          throw clickErr;
        }
      }
    }, 5);
  } catch (error) {
    throw new Error(`Error clicking "${buttonText}" button with id "${id}": ${error.message}`);
  }
});

When('I complete the Keycloak login form with username {string} and password {string}', async function (username, password) {
  await waitForPageLoad(this.driver, 20000);

  let usernameField = await this.driver.wait(until.elementLocated(By.id("username")), 10000)
  let userNameEl = await this.driver.wait(until.elementIsVisible(usernameField), 3000);
  await userNameEl.clear();
  await userNameEl.sendKeys(username);
  let passwordField = await this.driver.wait(until.elementLocated(By.id("password")), 10000)
  let passwordEl = await this.driver.wait(until.elementIsVisible(passwordField), 3000);

  await passwordEl.clear();
  await passwordEl.sendKeys(password);

  let submitButton = await this.driver.wait(until.elementLocated(By.id("kc-login")), 10000)
  await this.driver.wait(until.elementIsVisible(submitButton), 3000);
  await this.driver.wait(until.elementIsEnabled(submitButton), 5000);
  await this.driver.actions()
    .move({ origin: submitButton })
    .click()
    .perform();

  let logoutElm = await this.driver.wait(until.elementLocated(By.id("btn-logout")), 20000)
  await this.driver.wait(until.elementIsVisible(logoutElm), 10000);

});

Then('I should be redirected back to the application', async function () {
  await waitForPageLoad(this.driver, 30000);
  const currentUrl = await this.driver.getCurrentUrl();
  const baseUrl = process.env.BASE_URL || 'http://localhost:4200';
  if (!currentUrl.startsWith(baseUrl)) {
    throw new Error(`Expected to be redirected to ${baseUrl}, but found ${currentUrl}`);
  }

  await closeAllModals(this.driver);
  await this.driver.sleep(1000);
});