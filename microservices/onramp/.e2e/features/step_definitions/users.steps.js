const { When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');
const assert = require('assert');
const { waitForPageLoad, retryOnStaleElement } = require('../support/utils');
const fs = require('fs').promises;

When('I click the {string} button in the first row of the organizations table', async function (buttonId) {
  await waitForPageLoad(this.driver, 20000);
  await retryOnStaleElement(this.driver, async () => {
    const table = await this.driver.wait(
      until.elementLocated(By.id('organization-list')),
      30000,
      'Timeout waiting for organization list table'
    );
    await this.driver.wait(until.elementIsVisible(table), 15000);

    const firstRow = await table.findElement(By.css('tbody tr:first-child'));
    await this.driver.wait(until.elementIsVisible(firstRow), 10000);

    const button = await firstRow.findElement(By.id(buttonId));
    await this.driver.wait(until.elementIsVisible(button), 10000);
    await this.driver.wait(until.elementIsEnabled(button), 10000);

    await this.driver.executeScript('arguments[0].scrollIntoView({block: "center"});', button);
    await this.driver.sleep(300);
    try {
      await button.click();
    } catch (err) {
      console.warn(`⚠️ Direct click failed for "${buttonId}", using JS click.`);
      await this.driver.executeScript('arguments[0].click();', button);
    }
  }, 3);
});

When('I find the organization named {string} in the table {string} and click the {string} button with id {string}',
  async function (orgName, tableId, buttonText, buttonId) {
    await waitForPageLoad(this.driver, 20000);

    const tableSelector = `#${tableId}`;
    const paginationSelector = '.ant-pagination-item';
    let orgFound = false;

    const paginationItems = await this.driver.findElements(By.css(paginationSelector));
    const totalPages = paginationItems.length || 1;

    for (let page = 1; page <= totalPages; page++) {
      await this.driver.wait(until.elementLocated(By.css(tableSelector)), 10000);
      await this.driver.sleep(500);

      const rows = await this.driver.findElements(By.css(`${tableSelector} tbody tr`));
      for (const row of rows) {
        const rowText = (await row.getText()).toLowerCase();
        if (rowText.includes(orgName.toLowerCase())) {
          orgFound = true;
          await this.driver.executeScript('arguments[0].scrollIntoView({block: "center"});', row);
          await this.driver.sleep(300);

          const buttons = await row.findElements(By.id(buttonId));
          if (buttons.length > 0) {
            const btn = buttons[0];
            await this.driver.wait(until.elementIsVisible(btn), 10000);
            await this.driver.wait(until.elementIsEnabled(btn), 10000);
            await this.driver.executeScript('arguments[0].click();', btn);
          } else {
            throw new Error(`Button with id "${buttonId}" not found in row for "${orgName}"`);
          }
          break;
        }
      }
      if (orgFound) break;
      if (page < totalPages) {
        const nextPage = paginationItems[page];
        try {
          await this.driver.executeScript('arguments[0].scrollIntoView({block: "center"});', nextPage);
          await this.driver.sleep(200);
          await nextPage.click();
          await waitForPageLoad(this.driver, 5000);
          await this.driver.sleep(800);
        } catch (err) {
          console.warn(`⚠️ Could not move to page ${page + 1}: ${err.message}`);
        }
      }
    }
    if (!orgFound) {
      throw new Error(`❌ Organization "${orgName}" not found in any pagination pages`);
    }
  });

When('I click the Edit button with id {string} in that row', async function (buttonId) {
  const table = await this.driver.wait(
    until.elementLocated(By.css('#users-list')), 10000, '❌ Could not locate users-list table');

  const rows = await table.findElements(By.css('tbody tr'));
  if (rows.length === 0) {
    throw new Error('❌ No rows found in users-list table.');
  }

  const firstRow = rows[0];
  const editButtons = await firstRow.findElements(By.css(`#${buttonId}`));

  if (editButtons.length === 0) {
    throw new Error(`❌ No Edit button with id "${buttonId}" found in the first row.`);
  }

  const editBtn = editButtons[0];
  await this.driver.wait(until.elementIsVisible(editBtn), 10000);
  await this.driver.wait(until.elementIsEnabled(editBtn), 10000);
  await this.driver.executeScript('arguments[0].scrollIntoView({block: "center"});', editBtn);
  await this.driver.sleep(300);
  await this.driver.executeScript('arguments[0].click();', editBtn);
});


Then('The Users In Organization table contains at least one record', async function () {
  await waitForPageLoad(this.driver, 15000);
  await retryOnStaleElement(this.driver, async () => {
    await this.driver.wait(until.elementLocated(By.css('#users-list')), 10000);
    const rows = await this.driver.findElements(By.css('#users-list tr'));
    if (rows.length <= 1) {
      throw new Error('No records found in the permissions table');
    }
  });
});

When('I fill in the email field with {string}', async function (name) {
  await waitForPageLoad(this.driver, 15000);
  await retryOnStaleElement(this.driver, async () => {
    const nameField = await this.driver.wait(until.elementLocated(By.id('input-user-email')), 10000);
    const nameEl = await this.driver.wait(until.elementIsVisible(nameField), 5000);
    await nameEl.clear();
    await nameEl.sendKeys(name);
  });
  await this.driver.switchTo().defaultContent();
});

When('I fill in the message field with {string}', async function (description) {
  await waitForPageLoad(this.driver, 15000);
  await retryOnStaleElement(this.driver, async () => {
    const descriptionField = await this.driver.wait(until.elementLocated(By.id('input-user-message')), 10000);
    const descriptionEl = await this.driver.wait(until.elementIsVisible(descriptionField), 5000);
    await descriptionEl.clear();
    await descriptionEl.sendKeys(description);
  });
  await this.driver.switchTo().defaultContent();
});

When('I click the first Users button in the table', async function () {
  const tableId = 'organization-list';
  try {
    await waitForPageLoad(this.driver, 15000);

    await retryOnStaleElement(this.driver, async () => {
      const table = await this.driver.wait(until.elementLocated(By.id(tableId)), 20000);

      let usersButton;
      try {
        usersButton = await table.findElement(By.id('btn-users'));
      } catch (error) {
        const buttons = await table.findElements(By.css('.ant-btn'));
        for (const btn of buttons) {
          const text = await btn.getText();
          if (text.toLowerCase().includes('users')) {
            usersButton = btn;
            break;
          }
        }
        if (!usersButton) {
          throw new Error('No Users button found in table with id organization-list');
        }
      }

      await this.driver.wait(async () => {
        const isDisplayed = await usersButton.isDisplayed();
        const isEnabled = await usersButton.isEnabled();
        const classAttribute = await usersButton.getAttribute('class') || '';
        const isVisibleAndReady = isDisplayed && isEnabled && !classAttribute.includes('ant-btn-hidden');
        if (!isVisibleAndReady) {
          throw new Error(`Users button not ready. Displayed: ${isDisplayed}, Enabled: ${isEnabled}, Class: ${classAttribute}`);
        }
        return isVisibleAndReady;
      }, 10000, 'Users button in table organization-list is not fully visible or enabled');
      await this.driver.executeScript('arguments[0].scrollIntoView(true);', usersButton);
      try {
        await usersButton.click();
      } catch (clickError) {
        await this.driver.executeScript('arguments[0].click();', usersButton);
      }
    }, 5);
  } catch (error) {
    throw new Error(`Error clicking the first Users button in table with id ${tableId}:\n${error.message}\n${debugInfo}`);
  }
});

When('I confirm the deletion', async function () {
  const okButton = await this.driver.findElement(By.xpath("//button[contains(@class, 'ant-btn-primary') and contains(., 'OK')]"));
  await okButton.click();
});

Then('the first invitations name is not empty', async function () {
  await this.driver.wait(until.elementLocated(By.id('invitations-list')), 30000);
  const invitationRows = await this.driver.findElements(By.css('#invitations-list .ant-table-row'));
  if (invitationRows.length === 0) {
    throw new Error('No invitations found in the list. Table shows "No Data".');
  }
  const firstOrgRoleElement = await this.driver.wait(
    until.elementLocated(By.css('#invitations-list .ant-table-row:first-child td:nth-child(1)')),
    15000
  );
  await this.driver.wait(until.elementIsVisible(firstOrgRoleElement), 15000);
  const firstOrgRoleText = await firstOrgRoleElement.getText();
  if (!firstOrgRoleText.trim()) {
    throw new Error('First invitations orgRole is empty');
  }
});

When('I click the delete button with id {string}', async function (buttonId) {
  const locator = By.css(`.ant-table-row:first-child #${buttonId}`);

  let deleteButton;

  await this.driver.wait(until.elementLocated(locator), 20000, `Delete button with ID ${buttonId} not found`);

  for (let attempt = 0; attempt < 3; attempt++) {
    try {
      deleteButton = await this.driver.findElement(locator);

      await this.driver.wait(until.elementIsVisible(deleteButton), 5000);
      await this.driver.wait(until.elementIsEnabled(deleteButton), 5000);

      await this.driver.executeScript('arguments[0].scrollIntoView(true);', deleteButton);
      await deleteButton.click();

      return;
    } catch (err) {
      if (err.name === 'StaleElementReferenceError' && attempt < 2) {
        console.warn(`Stale element, retrying... (${attempt + 1})`);
        await this.driver.sleep(500);
        continue;
      }
      throw err;
    }
  }
});

Then('I click the delete user button in modal', async function () {
  const modal = await this.driver.wait(
    until.elementLocated(By.css('.ant-modal')),
    15000,
    'Timeout waiting for delete confirmation modal'
  );
  await this.driver.wait(until.elementIsVisible(modal), 5000);
  const okButton = await this.driver.wait(
    until.elementLocated(By.css('.ant-modal-confirm .ant-btn-primary')),
    10000,
    'Timeout waiting for OK button in modal'
  );
  await this.driver.wait(until.elementIsVisible(okButton), 5000);
  await this.driver.executeScript('arguments[0].click();', okButton);
});

Then('the confirmation modal should be displayed', async function () {
  const modal = await this.driver.wait(
    until.elementLocated(By.css('.ant-modal')),
    15000,
    'Confirmation modal not found'
  );
  await this.driver.wait(until.elementIsVisible(modal), 10000, 'Confirmation modal not visible');
  const isDisplayed = await modal.isDisplayed();
  assert.strictEqual(isDisplayed, true, 'Confirmation modal is not displayed');
});
