const { Given, When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');
const { waitForPageLoad, retry } = require('../support/utils');

Then('I am redirected to the {string} page', async function (pageName) {
  const expectedUrlPart = pageName.toLowerCase().replace(/\s+/g, '');
  await this.driver.wait(
    until.urlContains(expectedUrlPart),
    60000,
    `Timeout waiting for redirection to ${pageName} page`
  );
});

Then('I see the page title {string} with id {string}', async function (title, titleId) {
  let permisionTitle = await this.driver.wait(until.elementLocated(By.id(titleId)), 60000);
  await this.driver.wait(until.elementIsVisible(permisionTitle), 15000);

});

Then('I see a table with id {string}', async function (tableId) {
  let permisionList = await this.driver.wait(until.elementLocated(By.id("permissions-list")), 60000);
  await this.driver.wait(until.elementIsVisible(permisionList), 15000);

  let permisionContainer = await this.driver.wait(until.elementLocated(By.id("permission-container")), 60000);
  await this.driver.wait(until.elementIsVisible(permisionContainer), 15000);
});

Then('the table contains at least one record', async function () {
  const rows = await this.driver.findElements(By.css('#permissions-list tr'));
  if (rows.length <= 1) {
    throw new Error('No records found in the permissions table');
  }
});

When('I click the button with id {string}', async function (buttonId) {
  const button = await this.driver.wait(until.elementLocated(By.id(buttonId)), 60000);
  await this.driver.wait(until.elementIsEnabled(button), 15000);
  await this.driver.executeScript('arguments[0].click();', button);
  await waitForPageLoad(this.driver);
});

Then('I see a modal with class {string}', async function (modalClass) {
  const selectors = [`.${modalClass}`, '.ant-modal', '.modal', '[role="dialog"]'];
  let found = false;
  let lastError;
  for (const selector of selectors) {
    try {
      const modal = await this.driver.wait(until.elementLocated(By.css(selector)), 60000);
      await this.driver.wait(until.elementIsVisible(modal), 15000);
      found = true;
      break;
    } catch (err) {
      lastError = err;
    }
  }
  if (!found) {
    throw new Error(`Modal with class "${modalClass}" not found: ${lastError.message}`);
  }
});

Then('I see the modal title {string}', async function (expectedTitle) {
  const modal = await this.driver.wait(
    until.elementLocated(By.css('.modal-create-role, .ant-modal, .modal, [role="dialog"]')),
    60000
  );
  const titleElements = await modal.findElements(By.css('.ant-modal-title, .modal-title, h1, h2, h3, .modal-header'));
  let actualTitle = '';
  for (const el of titleElements) {
    actualTitle = await el.getText();
    if (actualTitle.trim()) break;
  }
});

When('I fill in the name field with {string}', async function (name) {
  let nameField = await this.driver.wait(until.elementLocated(By.id("new-organization-name-role")), 10000)
  let nameEl = await this.driver.wait(until.elementIsVisible(nameField), 3000);
  await nameEl.clear();
  await nameEl.sendKeys(name);
});

When('I fill in the description field with {string}', async function (description) {
  let descriptionField = await this.driver.wait(until.elementLocated(By.id("new-permission-role")), 10000)
  let descriptionEl = await this.driver.wait(until.elementIsVisible(descriptionField), 3000);
  await descriptionEl.clear();
  await descriptionEl.sendKeys(description);
});

Then('I see the message {string}', async function (expectedMessage) {
  try {
    const selector = '.ant-message-notice-content .ant-message-custom-content span:last-child';
    const messageElement = await this.driver.wait(
      until.elementLocated(By.css(selector)),
      10000,
      `Did not find Ant Design message with text "${expectedMessage}"`
    );
    await this.driver.wait(until.elementIsVisible(messageElement), 2000);
    let actualText = '';
    for (let i = 0; i < 10; i++) {
      actualText = (await messageElement.getText()).trim();
      if (actualText.includes(expectedMessage)) break;
      await this.driver.sleep(200);
    }

    if (!actualText.includes(expectedMessage)) {
      const all = await this.driver.executeScript(`
        return Array.from(document.querySelectorAll('${selector}')).map(e => e.textContent.trim());
      `);
      throw new Error(`Expected "${expectedMessage}" but got: ${actualText || '(empty)'}\nAll messages: ${all.join(', ')}`);
    }

    try {
      await this.driver.wait(until.stalenessOf(messageElement), 5000);
    } catch (_) {
      await this.driver.executeScript(`
        document.querySelectorAll('.ant-message-notice-content').forEach(e => e.remove());
      `);
    }
  } catch (error) {
    throw new Error(`Error verifying message "${expectedMessage}": ${error.message}`);
  }
});

Then('the table with id {string} contains {string}', async function (tableId, roleName) {
  await this.driver.wait(until.elementLocated(By.id(tableId)), 60000);
  let found = false;
  await retry(async () => {
    const roleLinks = await this.driver.findElements(By.css(`#${tableId} tr td a, #${tableId} tr td`));
    for (let link of roleLinks) {
      const text = await link.getText();
      if (text.trim().toLowerCase() === roleName.toLowerCase()) {
        found = true;
        break;
      }
    }
    if (!found) {
      throw new Error(`"${roleName}" not found in table "${tableId}"`);
    }
  }, 5, 3000);
});