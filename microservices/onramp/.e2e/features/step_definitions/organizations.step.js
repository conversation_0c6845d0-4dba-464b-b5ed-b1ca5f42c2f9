const { Given, When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');
const {
  waitForPageLoad,
  closeAllModals,
  safeClick,
  retryOnStaleElement,
  waitForModal
} = require('../support/utils');

Given('I am on the organizations page', async function () {
  await waitForPageLoad(this.driver, 20000);
  await closeAllModals(this.driver);

  try {
    await safeClick(this.driver, By.id('select-organizations'), '', 5);
  } catch {
    await closeAllModals(this.driver);
    const selectBtn = await this.driver.wait(until.elementLocated(By.id('select-organizations')), 15000);
    await this.driver.executeScript('arguments[0].click();', selectBtn);
  }

  await this.driver.wait(until.elementLocated(By.id('organization-list')), 20000);
  if (typeof this.waitForAngularStable === 'function') await this.waitForAngularStable();
  await waitForPageLoad(this.driver, 20000);
});

Then('I see the page title {string}', async function (expectedTitle) {
  await waitForPageLoad(this.driver, 20000);
  const titleEl = await this.driver.wait(until.elementLocated(By.id('title-organizations')), 15000);
  await this.driver.wait(until.elementIsVisible(titleEl), 15000);
  const actual = await titleEl.getText();
  if (actual.trim() !== expectedTitle)
    throw new Error(`Expected title "${expectedTitle}" but got "${actual}"`);
});

Then('I see the organization list with at least one record', async function () {
  const rows = await this.driver.findElements(
    By.css('#organization-list .default-row, #organization-list .highlight-row')
  );
  if (rows.length === 0) throw new Error('No organizations found in the list');
});

Then("the first organization's name is not empty", async function () {
  const name = await this.driver.findElement(
    By.css('#organization-list .default-row:first-child a, #organization-list .highlight-row:first-child a')
  ).getText();
  if (!name.trim()) throw new Error('First organization name is empty');
});

Then('I see the organization list only contains organizations named {string}', async function (expectedName) {
  const orgLinks = await this.driver.findElements(
    By.css('#organization-list .default-row a, #organization-list .highlight-row a')
  );
  if (orgLinks.length === 0) throw new Error('Organization list is empty');
  for (const link of orgLinks) {
    const text = (await link.getText()).trim();
    if (text !== expectedName)
      throw new Error(`Found unexpected organization name "${text}", expected "${expectedName}"`);
  }
});

When('I click the add organization button', async function () {
  const addBtn = await this.driver.findElement(By.css('.add-btn'));
  await addBtn.click();
  await waitForModal(this.driver);
});

When('I fill in the organization name {string} and description {string}', async function (orgName, orgDescription) {
  const modal = await waitForModal(this.driver);

  const nameInput = await this.driver.wait(
    until.elementLocated(By.css('.ant-modal input[id="new-organization-name"], .ant-modal input[name="name"]')),
    10000
  );
  await nameInput.clear();
  await nameInput.sendKeys(orgName);

  const descInput = await this.driver.wait(
    until.elementLocated(By.css('.ant-modal textarea[id="new-organization-description"], .ant-modal textarea[name="description"]')),
    10000
  );
  await descInput.clear();
  await descInput.sendKeys(orgDescription);
});

When('I submit the new organization form', async function () {
  try {
    const submitBtn = await retryOnStaleElement(this.driver, async () => {
      return this.driver.wait(until.elementLocated(By.css('.btn-submit.ant-btn-primary')), 20000);
    });

    await this.driver.wait(until.elementIsVisible(submitBtn), 10000);
    await this.driver.wait(until.elementIsEnabled(submitBtn), 10000);
    await submitBtn.click();

    await waitForPageLoad(this.driver, 30000);
    await this.driver.wait(until.elementLocated(By.id('organization-list')), 30000);
  } catch (err) {
    throw new Error(`Error submitting new organization form: ${err.message}`);
  }
});

Then('I see {string} in the organization list', async function (orgName) {
  try {
    const list = await this.driver.wait(until.elementLocated(By.id('organization-list')), 45000);
    await this.driver.wait(until.elementIsVisible(list), 10000);

    let found = false;
    for (let i = 0; i < 5; i++) {
      const links = await this.driver.findElements(
        By.css('#organization-list .default-row a, #organization-list .highlight-row a')
      );
      const allNames = [];
      for (const link of links) {
        const text = (await link.getText()).trim();
        if (text) allNames.push(text);
        if (text === orgName) {
          found = true;
          break;
        }
      }
      if (found) return;
      await this.driver.sleep(3000);
    }
    throw new Error(`Expected to see "${orgName}" in list`);
  } catch (e) {
    throw new Error(`Error verifying organization list: ${e.message}`);
  }
});

When('I click the edit button of the first organization', async function () {
  const editBtn = await this.driver.findElement(
    By.css('#organization-list .default-row:first-child .edit-btn, #organization-list .highlight-row:first-child .edit-btn')
  );
  await editBtn.click();
  await waitForModal(this.driver);
});

When('I change the organization name to {string}', async function (newName) {
  const modal = await waitForModal(this.driver);
  const nameInput = await this.driver.wait(
    until.elementLocated(By.css('.ant-modal input[id="new-organization-name"], .ant-modal input[name="name"]')),
    10000
  );
  await nameInput.clear();
  await nameInput.sendKeys(newName);
});

When('I submit the edit form', async function () {
  const modal = await waitForModal(this.driver);
  const submitBtn = await this.driver.wait(
    until.elementLocated(By.css('.ant-modal button[nzType="primary"]:not([nzType="default"])')),
    15000
  );
  await this.driver.wait(until.elementIsEnabled(submitBtn), 5000);
  await submitBtn.click();

  await this.driver.wait(until.stalenessOf(modal), 10000);
  await this.driver.wait(until.elementLocated(By.id('organization-list')), 20000);
});

When('I check the database for {string}', async function (orgName) {
  const baseUrl = process.env.BASE_URL || 'http://onramp:4200';
  let response = null;

  for (let i = 0; i < 3; i++) {
    try {
      response = await this.driver.executeAsyncScript(`
        const callback = arguments[arguments.length - 1];
        fetch('${baseUrl}/api/organizations?name=${encodeURIComponent(arguments[0])}')
          .then(r => r.ok ? r.json() : Promise.reject('HTTP ' + r.status))
          .then(data => callback(data))
          .catch(e => callback({ error: e.toString() }));
      `, orgName);
      if (response && !response.error) break;
    } catch { }
    await this.driver.sleep(2000);
  }

  if (!response || response.error)
    throw new Error(`API fetch failed: ${response?.error || 'no response'}`);

  this.lastDbCheck = Array.isArray(response.data)
    && response.data.some(org => org.name?.trim() === orgName.trim());

  if (!this.lastDbCheck)
    throw new Error(`Organization "${orgName}" not found in response`);
});

Then('the database contains {string}', async function (orgName) {
  if (!this.lastDbCheck)
    throw new Error(`Database does not contain "${orgName}"`);
});

When('I refresh the organization list', async function () {
  await this.driver.navigate().refresh();
  await this.driver.wait(until.elementLocated(By.id('organization-list')), 20000);
});
