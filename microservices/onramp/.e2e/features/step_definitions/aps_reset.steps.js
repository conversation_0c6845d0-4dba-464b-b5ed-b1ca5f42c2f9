const { Given, When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');
const assert = require('assert');
const { waitForPageLoad } = require('../support/utils');

Given('I am on the aps reset page', async function () {
  await waitForPageLoad(this.driver, 20000);

  let submenuItem = await this.driver.wait(until.elementLocated(By.css(".submenu-item")), 15000);
  await this.driver.wait(until.elementIsVisible(submenuItem), 15000);
  await submenuItem.click();

  let apsResetMenuItem = await this.driver.wait(until.elementLocated(By.css("#select-apsreset0")), 15000);
  await this.driver.wait(until.elementIsVisible(apsResetMenuItem), 15000);
  await apsResetMenuItem.click();

  if (typeof this.waitForAngularStable === 'function') {
    await this.waitForAngularStable();
  }

  await waitForPageLoad(this.driver, 20000);

  const inputs = await this.driver.findElements(By.css(".code-input"));
  assert.strictEqual(inputs.length, 4, 'Expected 4 input fields on APS Reset page');
  const submitButton = await this.driver.findElement(By.css(`button[nzType="primary"]`));
  assert(await submitButton.isDisplayed(), 'Expected Submit button to be displayed');
  let titleApsPage = await this.driver.wait(until.elementLocated(By.id('title-aps')));
  await this.driver.wait(until.elementIsVisible(titleApsPage), 15000);
});

When('the user enters a valid challenge code {string}', async function (code) {
  const inputs = [
    await this.driver.findElement(By.id('input_0')),
    await this.driver.findElement(By.id('input_1')),
    await this.driver.findElement(By.id('input_2')),
    await this.driver.findElement(By.id('input_3'))
  ];

  const part1 = code.substring(0, 2);
  const part2 = code.substring(2, 4);
  const part3 = code.substring(4, 6);
  const part4 = code.substring(6, 8);

  await inputs[0].sendKeys(part1);
  await inputs[1].sendKeys(part2);
  await inputs[2].sendKeys(part3);
  await inputs[3].sendKeys(part4);

  const confirmBtn = await this.driver.wait(until.elementLocated(By.id('btn-submit-aps')), 10000);
  await this.driver.wait(until.elementIsVisible(confirmBtn), 5000);
  await this.driver.wait(until.elementIsEnabled(confirmBtn), 5000);
  await confirmBtn.click();
  await this.driver.sleep(500);
});

Then('the user will see the response code {string}', async function (expectedResponseCode) {
  const responseElement = await this.driver.wait(until.elementLocated(By.id('responseCode')), 10000);
  await this.driver.wait(until.elementIsVisible(responseElement), 5000);

  const codeBoxElements = await responseElement.findElements(By.css('.response-group .code-box'));

  let actualResponseCode = '';
  for (const codeBox of codeBoxElements) {
    const text = await codeBox.getText();
    actualResponseCode += text;
  }

  actualResponseCode = actualResponseCode.replace(/\s+/g, '');

  assert.strictEqual(
    actualResponseCode,
    expectedResponseCode,
    `Expected response code to be "${expectedResponseCode}", but got "${actualResponseCode}"`
  );
});