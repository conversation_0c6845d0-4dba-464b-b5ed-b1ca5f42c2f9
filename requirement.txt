Title: As a backend developer, I need to update Change Password endpoint to enforce password policy

Descriptions:
update change-password API to enforce password policy

1. Validate Current Password
→ Returns a status indicating whether the current password is correct.

2. Get Password Policy
→ Returns the regex and related conditions used by both the backend (BE) and frontend (FE) to validate passwords.

3. Update Change-Password API
→ Update validate password in BE side (prevent invalid format when call API without FE) 