-- CAT-720: Flatten GatewayLogMessage table structure
-- Restructure from nested ARRAY<STRUCT<logentries>> to flat individual log entry rows
-- This enables better query performance and simpler analytics
-- Note: Existing data will be lost due to incompatible schema change

-- Step 1: Drop the existing table (nested structure is incompatible with new flat structure)
DROP TABLE IF EXISTS {{GatewayLogMessage}};

-- Step 2: Recreate table with flattened schema
-- Each log entry is now a separate row instead of nested in an array
CREATE TABLE IF NOT EXISTS {{GatewayLogMessage}} (
  organizationidentifier       STRING     {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid            STRING     {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                           STRING     {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                        STRING     {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp              TIMESTAMP  {% OPTIONS(description="The timestamp the pubsub message timestamp") %},
  pubsubid                     STRING     {% OPTIONS(description="The pubsub message identifier") %},
  messagetime                  TIMESTAMP  {% OPTIONS(description="The timestamp of the message creation by the gateway") %},
  bootid                       STRING     {% OPTIONS(description="Boot session identifier from gateway logs") %},
  eventtime                    TIMESTAMP  {% OPTIONS(description="Timestamp when the log event occurred (from event_time)") %},
  level                        STRING     {% OPTIONS(description="Log level (debug, info, warn, error)") %},
  logger                       STRING     {% OPTIONS(description="Logger name (if provided)") %},
  caller                       STRING     {% OPTIONS(description="Short caller (file:line) if provided") %},
  message                      STRING     {% OPTIONS(description="Log message content (from msg)") %},
  logdeviceid                  STRING     {% OPTIONS(description="Device ID associated with this log entry, if applicable") %},
  version                      STRING     {% OPTIONS(description="Application version if provided") %},
  worker_id                    STRING     {% OPTIONS(description="Worker/goroutine identifier if provided") %},
  stacktrace                   STRING     {% OPTIONS(description="Stack trace if the log includes error details") %},
  attributes                   JSON       {% OPTIONS(description="Additional structured attributes for the log entry") %},
  rawmessage                   BYTES      {% OPTIONS(description="Raw byte message from gateway") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid, bootid %};

