-- CAT-667: Create LogbookEntry table for device and location logbook entries
-- This migration creates a new table to store logbook entries for devices and locations

CREATE TABLE IF NOT EXISTS {{LogbookEntry}} (
  uuid                       STRING    {% OPTIONS(description="Unique identifier for the logbook entry") %},
  timestamp                  TIMESTAMP {% OPTIONS(description="Timestamp when the logbook entry was created") %},
  device_uuid                STRING    {% OPTIONS(description="UUID of the device associated with this entry") %},
  location_uuid              STRING    {% OPTIONS(description="UUID of the location associated with this entry") %},
  user_uuid                  STRING    {% OPTIONS(description="UUID of the user who created this entry") %},
  organization_uuid          STRING    {% OPTIONS(description="UUID of the organization this entry belongs to") %},
  automatic_fault_reason     STRING    {% OPTIONS(description="Automatically detected fault reason") %},
  manual_reason              STRING    {% OPTIONS(description="Manually entered reason for the entry") %},
  description                STRING    {% OPTIONS(description="Description of the logbook entry") %},
  notes                      STRING    {% OPTIONS(description="Additional notes for the entry") %},
  on_site                    BOOL      {% OPTIONS(description="Whether the entry was created on-site") %},
  edi_legacy_faultlog_uuid   STRING    {% OPTIONS(description="UUID from legacy EDI fault log system") %},
  edi_legacy_faultlog_type   STRING    {% OPTIONS(description="Type from legacy EDI fault log system") %},
  activity_category          STRING    {% OPTIONS(description="Category of activity for this entry") %}
)
{% PARTITION BY DATE(timestamp) CLUSTER BY organization_uuid, location_uuid, device_uuid %};
