------------------------------ Integration Testing Setup (identical in all environments) ------------------------------
INSERT INTO {{Organization}} (Id, Name, Description, OrgTypeIdentifier) VALUES
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_org'), 'Integration Testing Organization', 'Organization for integration testing', 'municipality')
ON CONFLICT (Id) DO UPDATE SET
  Name = EXCLUDED.Name,
  Description = EXCLUDED.Description,
  OrgTypeIdentifier = EXCLUDED.OrgTypeIdentifier;

-- Custom Roles for Integration Testing Organization
INSERT INTO {{CustomRole}} (Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier, Name, Description, IsDeletable) VALUES
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_role_admin'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_org'), 'mun_admin', 'municipality', 'Admin', 'Administrative permissions for integration testing', false),
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_role_manager'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_org'), 'mun_manager', 'municipality', 'Manager', 'Manager permissions for integration testing', true),
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_role_technician'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_org'), 'mun_technician', 'municipality', 'Technician', 'Technician permissions for integration testing', true),
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_role_anonymous'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_org'), 'mun_anonymous', 'municipality', 'Anonymous', 'Deny all permissions', false)
ON CONFLICT (Id) DO UPDATE SET
  OrganizationId = EXCLUDED.OrganizationId,
  TemplateRoleIdentifier = EXCLUDED.TemplateRoleIdentifier,
  OrgTypeIdentifier = EXCLUDED.OrgTypeIdentifier,
  Name = EXCLUDED.Name,
  Description = EXCLUDED.Description,
  IsDeletable = EXCLUDED.IsDeletable;

-- Gateway Config Template for Integration Testing
INSERT INTO {{GatewayConfigTemplate}} (Id, Name, OrganizationId, Description) VALUES
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_gateway_config_template'), 'Integration Test Gateway Config Template', uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_org'), 'Config template for integration test gateway with endpoint override')
ON CONFLICT (Id) DO UPDATE SET
  Name = EXCLUDED.Name,
  OrganizationId = EXCLUDED.OrganizationId,
  Description = EXCLUDED.Description;

-- Add base settings to the integration test template
INSERT INTO {{GatewayConfigTemplateSettings}} (gatewayConfigTemplateId, setting, value)
SELECT
  uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_gateway_config_template'),
  b.Setting,
  b.DefaultValue
FROM {{GatewayConfigTemplateBaseSettings}} b
ON CONFLICT (gatewayConfigTemplateId, setting) DO UPDATE SET
  value = EXCLUDED.value;

-- Override log_level to debug for integration testing
UPDATE {{GatewayConfigTemplateSettings}}
SET value = 'warn'
WHERE gatewayConfigTemplateId = uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_gateway_config_template')
  AND setting = 'log_level';

-- Override log_file_max_size_mb to 8 for integration testing
UPDATE {{GatewayConfigTemplateSettings}}
SET value = '8'
WHERE gatewayConfigTemplateId = uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_gateway_config_template')
  AND setting = 'log_file_max_size_mb';
  
-- Override gateway_proxy_port_for_eccom_communication to ":10001" (still a string :/) for integration testing
-- UPDATE {{GatewayConfigTemplateSettings}}
-- SET value = ':10001'
-- WHERE gatewayConfigTemplateId = uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_gateway_config_template')
--   AND setting = 'gateway_proxy_port_for_eccom_communication';

-- Software Gateway for Integration Testing
INSERT INTO {{SoftwareGateway}} (Id, MachineKey, OrganizationId, APIKey, Token, Name, Description, Config, IsEnabled, TemplateId) VALUES
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_gateway'), 'INTEGRATION_TEST', uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_org'), 'integrationtest_0123456789abcdef', '', 'Integration Test Gateway', 'Gateway for integration testing', '', true, uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_gateway_config_template'))
ON CONFLICT (Id) DO UPDATE SET
  MachineKey = EXCLUDED.MachineKey,
  OrganizationId = EXCLUDED.OrganizationId,
  APIKey = EXCLUDED.APIKey,
  Token = EXCLUDED.Token,
  Name = EXCLUDED.Name,
  Description = EXCLUDED.Description,
  Config = EXCLUDED.Config,
  IsEnabled = EXCLUDED.IsEnabled,
  TemplateId = EXCLUDED.TemplateId;

-- Locations for Integration Test Devices
INSERT INTO {{Location}} (Id, Name, Description, Latitude, Longitude, OrganizationId)
SELECT
  uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_location_' || i::text),
  'Integration Test Location ' || i::text,
  'Location for integration test device ' || i::text,
  33.0200 + (i * 0.0001), -- Slightly vary latitude
  -96.7300 + (i * 0.0001), -- Slightly vary longitude
  uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_org')
FROM generate_series(1, 256) AS i
ON CONFLICT (Id) DO UPDATE SET
  Name = EXCLUDED.Name,
  Description = EXCLUDED.Description,
  Latitude = EXCLUDED.Latitude,
  Longitude = EXCLUDED.Longitude,
  OrganizationId = EXCLUDED.OrganizationId;

-- Devices for Integration Testing
-- All devices use 127.0.0.1 IP with ports starting at 20001
INSERT INTO {{Device}} (Id, SoftwareGatewayId, LocationId, OrganizationId, Name, Description, IpAddress, Port, Type, FlushConnectionMs, EnableRealtime, IsEnabled)
SELECT
  uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_device_' || i::text),
  uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_gateway'),
  uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_location_' || i::text),
  uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_org'),
  'Integration Test Device ' || i::text,
  'Legacy device for integration testing (port ' || (20000 + i)::text || ')',
  '127.0.0.1',
  20000 + i, -- Ports from 20001 to 20020
  'EDI_LEGACY',
  400,
  true,
  true
FROM generate_series(1, 256) AS i
ON CONFLICT (Id) DO UPDATE SET
  SoftwareGatewayId = EXCLUDED.SoftwareGatewayId,
  LocationId = EXCLUDED.LocationId,
  OrganizationId = EXCLUDED.OrganizationId,
  Name = EXCLUDED.Name,
  Description = EXCLUDED.Description,
  IpAddress = EXCLUDED.IpAddress,
  Port = EXCLUDED.Port,
  Type = EXCLUDED.Type,
  FlushConnectionMs = EXCLUDED.FlushConnectionMs,
  EnableRealtime = EXCLUDED.EnableRealtime,
  IsEnabled = EXCLUDED.IsEnabled;

-- Device Groups for Integration Testing
INSERT INTO {{DeviceGroups}} (Id, OrganizationId, Name) VALUES
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_device_group'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_org'), 'Integration Test Devices')
ON CONFLICT (Id) DO UPDATE SET
  OrganizationId = EXCLUDED.OrganizationId,
  Name = EXCLUDED.Name;
 
-- Device Group Devices for Integration Testing
INSERT INTO {{DeviceGroupDevices}} (DeviceGroupId, DeviceId)
SELECT
  uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_device_group'),
  uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_device_' || i::text)
FROM generate_series(1, 256) AS i
ON CONFLICT (DeviceGroupId, DeviceId) DO NOTHING;

-- User for Integration Testing
INSERT INTO {{User}} (Id, FirstName, LastName, Mobile, NotificationSmsEnabled, IanaTimezone, Description, IsDeleted) VALUES
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_user_admin'), 'Integration Test', 'Admin', '******-574-2528', true, 'America/Chicago', 'Admin user for integration testing organization', false),
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_user_technician'), 'Integration Test', 'Technician', '', false, 'America/Chicago', 'Technician user for integration testing organization', false)
ON CONFLICT (Id) DO UPDATE SET
  FirstName = EXCLUDED.FirstName,
  LastName = EXCLUDED.LastName,
  Mobile = EXCLUDED.Mobile,
  NotificationSmsEnabled = EXCLUDED.NotificationSmsEnabled,
  IanaTimezone = EXCLUDED.IanaTimezone,
  Description = EXCLUDED.Description,
  IsDeleted = EXCLUDED.IsDeleted;

-- AuthMethod for Integration Test User
INSERT INTO {{AuthMethod}} (Id, UserId, Type, UserName, Email, PasswordHash, IsEnabled) VALUES 
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_auth_admin'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_user_admin'), 'USERNAME_PASSWORD', 'integration_test_admin', '<EMAIL>', '$sha256$hex:7235fc60d3c6c4a61275d396b89848628d8e83b7c7546edad071f69de1b4da85', true),
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_auth_technician'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_user_technician'), 'USERNAME_PASSWORD', 'integration_test_technician', '<EMAIL>', '$sha256$hex:49b50f5e72a67d4db974f1b80c95d474461a017e1027313abbb8aed3a47f768e', true)
ON CONFLICT (Id) DO UPDATE SET
  UserId = EXCLUDED.UserId,
  Type = EXCLUDED.Type,
  UserName = EXCLUDED.UserName,
  Email = EXCLUDED.Email,
  PasswordHash = EXCLUDED.PasswordHash,
  IsEnabled = EXCLUDED.IsEnabled;

-- Membership for Integration Test User
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId) VALUES
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_membership_admin'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_auth_admin'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_org')),
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_membership_technician'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_auth_technician'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_org'))
ON CONFLICT (Id) DO UPDATE SET
  AuthMethodId = EXCLUDED.AuthMethodId,
  OrganizationId = EXCLUDED.OrganizationId;

-- Organization Role Assignment for Integration Test User (Admin access)
INSERT INTO {{OrgRoleAssignments}} (MembershipId, RoleId) VALUES
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_membership_admin'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_role_admin')),
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_membership_technician'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_role_technician'))
ON CONFLICT (MembershipId, RoleId) DO NOTHING;

-- Device Group Role Assignments for Integration Test User (Admin access to both device groups)
INSERT INTO {{DeviceGroupRoleAssignments}} (MembershipId, DeviceGroupId, RoleId) VALUES
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_membership_admin'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_device_group'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_role_admin')),
  (uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_membership_technician'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_device_group'), uuid_generate_v5(uuid_nil(), 'INTEGRATION_TEST_role_technician'))
ON CONFLICT (MembershipId, DeviceGroupId, RoleId) DO NOTHING;