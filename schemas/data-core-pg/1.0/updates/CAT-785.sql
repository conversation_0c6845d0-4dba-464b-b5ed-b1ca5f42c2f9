-- ================================================
-- CAT-785: Update UserInvites status check constraint
-- Add new status to valid status values
-- ================================================

-- Drop the existing status check constraint
ALTER TABLE {{UserInvites}} 
  DROP CONSTRAINT IF EXISTS {{UserInvites_status_CHK}};

-- Add the updated status check constraint with new status
ALTER TABLE {{UserInvites}} 
  ADD CONSTRAINT {{UserInvites_status_CHK}} CHECK (status IN ('new user','pending','redeemed','revoked','expired','rejected','approved'));

-- Add comment to document the change
COMMENT ON CONSTRAINT {{UserInvites_status_CHK}} ON {{UserInvites}} IS 'Status must be one of: new user, pending, redeemed, revoked, expired, rejected, approved';
