-- Updated dev data for restructured schema
-- Additional test data for comprehensive permission testing

-- Base organization (already exists in DEV and QA)
INSERT INTO {{Organization}} (Id, Name, Description, OrgTypeIdentifier) VALUES
  ('c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Synapse-Plano-Demo', 'Synapse-Plano-Demo', 'municipality');
-- Base roles (already exists in DEV and QA)
INSERT INTO {{CustomRole}} (Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier, Name, Description, IsDeletable) VALUES
  ('12dc75d1-13ac-5bbe-810c-0c40bcddca21', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'mun_admin', 'municipality', 'Admin', 'Administrative permissions', false),
  ('49f5bc0b-f26f-5ab6-96a0-10f2c177a7b6', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'mun_manager', 'municipality', 'Manager', 'Manager permissions', true),
  ('7a838232-87c5-519d-9147-e4f27d4f03ea', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'mun_technician', 'municipality', 'Technician', 'Technician permissions', true),
  ('58925ea8-2cfb-5e7b-9fd3-d1debac814e6', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'mun_anonymous', 'municipality', 'Anonymous', 'Deny all permissions', false);
-- Base softwaregateway (already exists in DEV and QA)
INSERT INTO {{SoftwareGateway}} (Id, MachineKey, OrganizationId, ApiKey, Token, DateLastCheckedIn, PushConfigOnNextCheck, IsEnabled, Config, Name, Description) VALUES
  ('f55c66ea-e279-5961-a0b1-b212dca33dc9', '4777b483831293d620b96ba410d14894086104f83cede6bc8477ac5828bbdf2e', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', '2025-06-30 21:51:40', false, true, '', 'Synapse-Plano-Demo', 'Synapse-Plano-Demo');
-- Base locations (some already exists in DEV and QA)
INSERT INTO {{Location}} (Id, OrganizationId, Name, Description, Latitude, Longitude) VALUES
  ('ced1aca1-bd42-5a04-a6fb-40feb81c5a41', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0227, -96.7353),
  ('37a51eb2-3cb0-5505-a94c-4e94ad2718d1', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0227, -96.7517),
  ('8c616bb0-439d-5863-a619-e50f8dd7fee4', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0173, -96.7026),
  ('b184a6bc-2eb4-58ae-a91e-10da5538065b', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0285, -96.7025),
  ('4a22b16d-8b1d-56b0-b357-f92aadfe7e65', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0285, -96.7353),
  ('6746f2d7-5d8a-52f8-9dc3-10bb137c42d1', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0285, -96.7517),
  ('145c1fd1-5a2e-589a-bc7f-431bd9228082', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0408, -96.7354),
  ('c28a137b-30b7-5df5-914b-1f2110934d6d', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0408, -96.7026),
  ('f1e3685a-03b2-52a2-aaac-60c269cb0d99', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Device Location: Mock Device', 'Location for device Mock Device', 33.0408, -96.7517);
-- Base devices (some already exists in DEV and QA). Port ranges are 10000-10999 to avoid collisions with the dev/qa/prod gateways running on the same machine.
INSERT INTO {{Device}} (Id, OrigId, SoftwareGatewayId, LocationId, OrganizationId, Name, Description, IpAddress, Port, Type, FlushConnectionMs, EnableRealtime) VALUES
  ('87d94e14-e804-58b3-9f8c-a02e5de90aeb', 7000, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', 'ced1aca1-bd42-5a04-a6fb-40feb81c5a41', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 10010, 'EDI_LEGACY', 400, true),
  ('5a7f98cd-aaa1-5b16-be30-d2f9fb86ed74', 7001, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', '37a51eb2-3cb0-5505-a94c-4e94ad2718d1', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 10011, 'EDI_LEGACY', 400, true),
  ('fb2ba632-80af-5836-a82f-d7d329ef924f', 7002, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', '8c616bb0-439d-5863-a619-e50f8dd7fee4', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 10012, 'EDI_LEGACY', 400, true),
  ('1858185e-cc6e-5a74-ac9a-5a4b3d9a7845', 7003, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', 'b184a6bc-2eb4-58ae-a91e-10da5538065b', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 10013, 'EDI_LEGACY', 400, true),
  ('99c66f8b-a82f-5e85-bf4a-031d0b2be0bc', 7004, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', '4a22b16d-8b1d-56b0-b357-f92aadfe7e65', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 10014, 'EDI_LEGACY', 400, true),
  ('51d49dad-7891-5f19-b4f6-37fb69660dcc', 7005, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', '6746f2d7-5d8a-52f8-9dc3-10bb137c42d1', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 10015, 'EDI_LEGACY', 400, true),
  ('c465bd6a-3bad-5d5d-9e79-c2ecd8f61ea3', 7006, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', '145c1fd1-5a2e-589a-bc7f-431bd9228082', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 10016, 'EDI_LEGACY', 400, true),
  ('a907c9ff-8227-59e3-b66f-61e513c8bf5e', 7007, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', 'c28a137b-30b7-5df5-914b-1f2110934d6d', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 10017, 'EDI_LEGACY', 400, true),
  ('e21b9d8e-d64b-5e12-a1f8-8aa7e2445395', 7008, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', 'f1e3685a-03b2-52a2-aaac-60c269cb0d99', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'Mock Device', 'NA', '127.0.0.1', 10018, 'EDI_LEGACY', 400, true);
-- Base device group (already exists in DEV and QA). All legacy devices are in the same device group.
INSERT INTO {{DeviceGroups}} (Id, OrganizationId, Name) VALUES
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'migrated group: 0a0840b7-2528-576a-94c2-6a9d31efb31f');
-- Base device group devices (some already exists in DEV and QA)
INSERT INTO {{DeviceGroupDevices}} (DeviceGroupId, DeviceId) VALUES
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', '87d94e14-e804-58b3-9f8c-a02e5de90aeb'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', '5a7f98cd-aaa1-5b16-be30-d2f9fb86ed74'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', 'fb2ba632-80af-5836-a82f-d7d329ef924f'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', '1858185e-cc6e-5a74-ac9a-5a4b3d9a7845'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', '99c66f8b-a82f-5e85-bf4a-031d0b2be0bc'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', '51d49dad-7891-5f19-b4f6-37fb69660dcc'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', 'c465bd6a-3bad-5d5d-9e79-c2ecd8f61ea3'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', 'a907c9ff-8227-59e3-b66f-61e513c8bf5e'),
  ('0a0840b7-2528-576a-94c2-6a9d31efb31f', 'e21b9d8e-d64b-5e12-a1f8-8aa7e2445395');
-- Base SynapsePlano user (already exists in DEV and QA)
INSERT INTO {{User}} (Id, OrigId, FirstName, LastName, Mobile, NotificationSmsEnabled, IanaTimezone, Description) VALUES
  ('45627c04-8d87-595a-a31b-2e675e22417a', 25, 'Demo', 'Account', '', false, 'America/Chicago', 'Synapse - Plano Demo Account');
-- Base SynapsePlano auth method (already exists in DEV and QA)
INSERT INTO {{AuthMethod}} (Id, UserId, Type, Sub, Issuer, UserName, PasswordHash, Email, Metadata, LastLogin, FailedLoginAttempts, IsEnabled, ForcePasswordChange) VALUES
  ('692d620e-7dbf-5e7b-bb66-0167c3498f51', '45627c04-8d87-595a-a31b-2e675e22417a', 'USERNAME_PASSWORD', NULL, NULL, 'SynapsePlano', '5a6fd3906328e22b6e6df22e811f8d40038a92e98520a8199183841de3889564', NULL, NULL, '2025-07-01 01:31:10', 0, true, false);
-- Base SynapsePlano membership (already exists in DEV and QA)
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId) VALUES
  ('********-34a5-50f7-82d5-9039a8cf214b', '692d620e-7dbf-5e7b-bb66-0167c3498f51', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3');
-- Base SynapsePlano device group role assignment (already exists in DEV and QA)
INSERT INTO {{DeviceGroupRoleAssignments}} (MembershipId, DeviceGroupId, RoleId) VALUES
  ('********-34a5-50f7-82d5-9039a8cf214b', '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea');

--- New Configuration ---

-- New locations
INSERT INTO {{Location}} (Id, Name, Description, Latitude, Longitude, OrganizationId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), '4th and Main', 'ALPHA', '33.7517577', '-84.41253', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_2'), '5th and Main', 'BRAVO', '33.748893', '-84.40453', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_3'), '6th and Main', 'CHARLIE', '33.7508979', '-84.4031675', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_4'), '2360', 'PLANO MMU 188:110', '33.7537577', '-84.3981712', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_5'), '2361', 'PLANO MMU 188:995', '33.7517577', '-84.3961712', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_6'), '2362', 'PLANO MMU 057:110', '33.7517577', '-84.3974713', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_7'), '2363', 'PLANO MMU 057:995', '33.7517577', '-84.3974713', 'c469a554-f7a8-5de5-a57e-e1ba16f970d3');
-- Next gen devices. IsEnabled is false because we don't want the gateway to attempt to talk to them. They will show up in API calls. Device flag is used by FSA to identify the devices.
INSERT INTO {{Device}} (Id, OrigId, SoftwareGatewayId, LocationId, OrganizationId, DeviceFlag, Name, Description, IpAddress, Port, Type, FlushConnectionMs, EnableRealtime, SerialNumber, IsEnabled) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_ALPHA'), 7100, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'ALPHA', 'ALPHA Next Gen Device', 'NA', '*************', 110, 'EDI_NEXT_GEN', 400, true, '101010', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_BETA'), 7101, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'BETA', 'BETA Next Gen Device', 'NA', '*************', 110, 'EDI_NEXT_GEN', 400, true, '202020', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_CHARLIE'), 7102, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'CHARLIE', 'CHARLIE Next Gen Device', 'NA', '*************', 110, 'EDI_NEXT_GEN', 400, true, '303030', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_188_110'), 7200, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'PLANO MMU 188:110', 'PLANO MMU 188:110 Next Gen Device', 'NA', '10.20.5.188', 110, 'EDI_NEXT_GEN', 400, true, '404040', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_188_995'), 7201, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'PLANO MMU 188:995', 'PLANO MMU 188:995 Next Gen Device', 'NA', '10.20.5.188', 995, 'EDI_NEXT_GEN', 400, true, '404040', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_057_110'), 7202, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'PLANO MMU 057:110', 'PLANO MMU 057:110 Next Gen Device', 'NA', '10.20.5.57', 110, 'EDI_NEXT_GEN', 400, true, '505050', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_057_995'), 7203, 'f55c66ea-e279-5961-a0b1-b212dca33dc9', uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_location_1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'PLANO MMU 057:995', 'PLANO MMU 057:995 Next Gen Device', 'NA', '10.20.5.57', 995, 'EDI_NEXT_GEN', 400, true, '505050', false);
-- Seed next gen monitor_id and user_id. This is because these devices are not communicating with the gateway and hence not sending data to the cloud.
INSERT INTO {{DeviceMonitorName}} (DeviceId, MonitorId, MonitorName, PubsubTimestamp, UpdatedAt) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_ALPHA'), 11111111, '4th and Main', NOW(), NOW()),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_BETA'), 22222222, '5th and Main', NOW(), NOW()),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_CHARLIE'), 33333333, '6th and Main', NOW(), NOW()),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_188_110'), 44444444, '2360', NOW(), NOW()),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_188_995'), 55555555, '2361', NOW(), NOW()),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_057_110'), 66666666, '2362', NOW(), NOW()),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_057_995'), 77777777, '2363', NOW(), NOW());
-- New device groups. A new one is created for each device for grunular permissions.
INSERT INTO {{DeviceGroups}} (Id, OrganizationId, Name) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'ALPHA Device Group'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'BETA Device Group'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', 'CHARLIE Device Group'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', '188:110 Device Group'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', '188:995 Device Group'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', '057:110 Device Group'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3', '057:995 Device Group');
-- New device group devices
INSERT INTO {{DeviceGroupDevices}} (DeviceGroupId, DeviceId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_ALPHA')), 
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_BETA')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_CHARLIE')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_188_110')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_188_995')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_057_110')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_d_057_995'));
-- New users. All users have sms disabled and will need to be enabled per user.
INSERT INTO {{User}} (Id, OrigId, FirstName, LastName, Mobile, NotificationSmsEnabled, IanaTimezone, Description, IsDeleted) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jason'), 50001, 'Jason', 'DeVillier', '******-218-6057', false, 'America/Chicago', 'Synapse-Plano-Demo user jason', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jeff'), 50002, 'Jeff', 'Cornelius', '******-574-2528', false, 'America/Chicago', 'Synapse-Plano-Demo user jeff', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_brian'), 50003, 'Brian', 'Beckwith', '', false, 'America/Chicago', 'Synapse-Plano-Demo user brian', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_nicolas'), 50004, 'Nicolas', 'Chasteler', '', false, 'America/Chicago', 'Synapse-Plano-Demo user nicolas', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_daniel'), 50005, 'Daniel', 'Warunek', '', false, 'America/Chicago', 'Synapse-Plano-Demo user daniel', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_corey'), 50006, 'Corey', 'Pennycuff', '', false, 'America/Chicago', 'Synapse-Plano-Demo user corey', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_dan'), 50007, 'Dan', 'Skites', '', false, 'America/Chicago', 'Synapse-Plano-Demo user dan', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_walt'), 50008, 'Walt', 'Kicinski', '', false, 'America/Chicago', 'Synapse-Plano-Demo user walt', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_huy'), 50009, 'Huy', 'Dang', '', false, 'America/Chicago', 'Synapse-Plano-Demo user huy', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_mason'), 50010, 'Mason', 'Lambert', '', false, 'America/Chicago', 'Synapse-Plano-Demo user mason', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_allen'), 50011, 'Allen', 'Jacobs', '', false, 'America/Chicago', 'Synapse-Plano-Demo user allen', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jae'), 50012, 'Jae', 'Oh', '', false, 'America/Chicago', 'Synapse-Plano-Demo user jae', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jun'), 50013, 'Jun', 'Fu', '', false, 'America/Chicago', 'Synapse-Plano-Demo user jun', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_offshore'), 50014, 'Offshore', '', '', false, 'America/Chicago', 'Synapse-Plano-Demo user offshore', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_show1'), 50100, 'Show1', '1', '', false, 'America/Chicago', 'Synapse-Plano-Demo user show1', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_show2'), 50101, 'Show2', '2', '', false, 'America/Chicago', 'Synapse-Plano-Demo user show2', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_show3'), 50102, 'Show3', '3', '', false, 'America/Chicago', 'Synapse-Plano-Demo user show3', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_appreview'), 50200, 'App', 'Review', '', false, 'America/Chicago', 'Synapse-Plano-Demo user app review', false);
-- New auth methods
INSERT INTO {{AuthMethod}} (Id, UserId, Type, UserName, Email, PasswordHash, IsEnabled) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jason'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jason'), 'USERNAME_PASSWORD', 'jdevillier', '<EMAIL> ', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jeff'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jeff'), 'USERNAME_PASSWORD', 'jcornelius', '<EMAIL> ', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_brian'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_brian'), 'USERNAME_PASSWORD', 'bbeckwith', '<EMAIL>', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_nicolas'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_nicolas'), 'USERNAME_PASSWORD', 'nchasteler', '<EMAIL>', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_daniel'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_daniel'), 'USERNAME_PASSWORD', 'dwarunek', '<EMAIL>', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_corey'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_corey'), 'USERNAME_PASSWORD', 'cpennycuff', '<EMAIL>', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_dan'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_dan'), 'USERNAME_PASSWORD', 'dskites', '<EMAIL>', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_walt'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_walt'), 'USERNAME_PASSWORD', 'wkicinski', '<EMAIL>', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_huy'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_huy'), 'USERNAME_PASSWORD', 'hdang', '<EMAIL>', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_mason'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_mason'), 'USERNAME_PASSWORD', 'mlambert', '<EMAIL>', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_allen'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_allen'), 'USERNAME_PASSWORD', 'ajacobs', '<EMAIL>', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jae'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jae'), 'USERNAME_PASSWORD', 'joh', '<EMAIL>', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jun'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_jun'), 'USERNAME_PASSWORD', 'jfu', '<EMAIL>', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true),  
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_offshore'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_offshore'), 'USERNAME_PASSWORD', 'offshore', '<EMAIL>', 'ac7d09c486f4ffe9ad930c844992a41f8b92713458237b2dab5d73513a8e1eb8', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_show1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_show1'), 'USERNAME_PASSWORD', 'show1', '<EMAIL>', '7d8c61ea70cf808b87ad4fa5f596b609c2cff72df200a770385ea29f4b02606d', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_show2'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_show2'), 'USERNAME_PASSWORD', 'show2', '<EMAIL>', '7d8c61ea70cf808b87ad4fa5f596b609c2cff72df200a770385ea29f4b02606d', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_show3'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_show3'), 'USERNAME_PASSWORD', 'show3', '<EMAIL>', '7d8c61ea70cf808b87ad4fa5f596b609c2cff72df200a770385ea29f4b02606d', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_appreview'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_appreview'), 'USERNAME_PASSWORD', 'appreview', '<EMAIL>', '60b4807e798624c086a55e0bc21f3ff1b31296346698951d48ad51a89f42f103', true);
-- New memberships
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jason'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jeff'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_brian'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_nicolas'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_daniel'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_corey'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_dan'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_walt'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_huy'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_mason'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_allen'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jae'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_jun'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_offshore'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_show1'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_show2'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_show3'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_appreview_plano'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_appreview'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3');
-- New device group role assignments. This is based on a matrix of users and devices so we know who can access what. All permissions are ready only through the cloud.
INSERT INTO {{DeviceGroupRoleAssignments}} (MembershipId, DeviceGroupId, RoleId) VALUES
-- Legacy device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_appreview_plano'), '0a0840b7-2528-576a-94c2-6a9d31efb31f', '7a838232-87c5-519d-9147-e4f27d4f03ea'), -- App review only has access to legacy devices
-- Alpha device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_ALPHA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
-- Beta device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_BETA'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
-- Charlie device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_CHARLIE'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
-- Plano 188:110 device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
-- Plano 188:995 device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_188_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
-- Plano 057:110 device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_110'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
-- Plano 057:995 device access
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jeff_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_brian_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_nicolas_plano'),   uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_daniel_plano'),    uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_corey_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_dan_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_walt_plano'),      uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_huy_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_mason_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_allen_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jae_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_jun_plano'),       uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_offshore_plano'),  uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea')
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show1_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show2_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea'),
  --(uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_show3_plano'),     uuid_generate_v5(uuid_nil(), 'SYNAPSE_ng_dg_057_995'), '7a838232-87c5-519d-9147-e4f27d4f03ea')
;

-- Test Organizations for permission testing
INSERT INTO {{Organization}} (Id, Name, Description, OrgTypeIdentifier) VALUES
  ('550e8400-e29b-41d4-a716-************', 'Test Organization 1', 'First test organization for permissions', 'municipality'),
  ('550e8400-e29b-41d4-a716-************', 'Test Organization 2', 'Second test organization for permissions', 'municipality');

-- Test SoftwareGateways for integration testing
INSERT INTO {{SoftwareGateway}} (Id, MachineKey, OrganizationId, APIKey, Token, Name, Description, Config, IsEnabled) VALUES
  -- Test gateways for Test Organization 1
  ('550e8400-e29b-41d4-a716-************', 'TESTGATEWAY1', '550e8400-e29b-41d4-a716-************', 'testapi1_qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Test Gateway 1', 'Integration test gateway for Test Organization 1', '{"log_level": "info", "log_filename": "log/gateway-app.log", "log_max_backups": 5, "application_version": "deprecated", "log_max_age_in_days": 7, "log_compress_backups": true, "log_file_max_size_mb": 5, "rest_api_device_endpoint": "http://localhost:8080/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": false, "edi_device_persist_connection": true, "edi_device_processing_retries": 3, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 30, "config_change_check_frequency_seconds": 15, "send_gateway_performance_stats_to_cloud": false, "software_update_check_frequency_seconds": 43200, "channel_state_send_frequency_milliseconds": 1000, "gateway_performance_stats_output_frequency_seconds": 60, "ws_active": true, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 1, "ws_send_frequency_milliseconds": 3000, "ws_heartbeat_send_frequency_milliseconds": 30000, "threshold_device_error_seconds": 45}', true),
  -- Test gateways for Test Organization 2  
  ('550e8400-e29b-41d4-a716-446655440200', 'TESTGATEWAY2', '550e8400-e29b-41d4-a716-************', 'testapi2_qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Test Gateway 2', 'Integration test gateway for Test Organization 2', '{"log_level": "debug", "log_filename": "log/gateway-app.log", "log_max_backups": 3, "application_version": "deprecated", "log_max_age_in_days": 3, "log_compress_backups": false, "log_file_max_size_mb": 2, "rest_api_device_endpoint": "http://localhost:8080/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": true, "edi_device_persist_connection": false, "edi_device_processing_retries": 10, "record_http_requests_to_folder": true, "device_state_send_frequency_seconds": 120, "config_change_check_frequency_seconds": 60, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 43200, "channel_state_send_frequency_milliseconds": 250, "gateway_performance_stats_output_frequency_seconds": 30, "ws_active": false, "ws_port": "8080", "ws_endpoint": "/test", "ws_max_connections": 5, "ws_send_frequency_milliseconds": 1000, "ws_heartbeat_send_frequency_milliseconds": 15000, "threshold_device_error_seconds": 180}', true),
  -- Additional test gateways for specific testing scenarios
  ('550e8400-e29b-41d4-a716-************', 'TESTGATEWAY1_DISABLED', '550e8400-e29b-41d4-a716-************', 'testapi1_disabled_qiLkND6fYR2zVIQbgifvR2VbyOST6qt6OgzPZTd', '', 'Test Gateway 1 (Disabled)', 'Disabled integration test gateway for Test Organization 1', '{"log_level": "error"}', false),
  ('550e8400-e29b-41d4-a716-************', 'TESTGATEWAY2_MINIMAL', '550e8400-e29b-41d4-a716-************', 'testapi2_minimal_qiLkND6fYR2zVIQbgifvR2VbyOST6qt6OgzPZTd', '', 'Test Gateway 2 (Minimal Config)', 'Minimal config test gateway for Test Organization 2', '{"log_level": "info", "rest_api_device_endpoint": "http://localhost:8080/api/v3/gateway/ingest"}', true);

-- Test Users for permission testing
INSERT INTO {{User}} (Id, OrigId, FirstName, LastName, Mobile, NotificationSmsEnabled, IanaTimezone, Description, IsDeleted) VALUES
  ('550e8400-e29b-41d4-a716-************', 9001, 'Admin', 'User', '******-555-1001', false, 'America/Chicago', 'Test admin user with full permissions', false),
  ('550e8400-e29b-41d4-a716-************', 9002, 'Manager', 'User', '******-555-1002', false, 'America/Chicago', 'Test manager user with limited permissions', false),
  ('550e8400-e29b-41d4-a716-************', 9003, 'Technician', 'User', '******-555-1003', false, 'America/Chicago', 'Test technician user with device-only permissions', false),
  ('550e8400-e29b-41d4-a716-446655440004', 9004, 'Custom', 'User', '******-555-1004', false, 'America/Chicago', 'Test user with custom role permissions', false),
  ('550e8400-e29b-41d4-a716-446655440005', 9005, 'MultiOrg', 'User', '******-555-1005', false, 'America/Chicago', 'Test user with multiple organization access', false),
  ('550e8400-e29b-41d4-a716-446655440006', 9006, 'Anonymous', 'User', '******-555-1006', false, 'America/Chicago', 'Test user with anonymous role (no permissions)', false),
  ('550e8400-e29b-41d4-a716-************', 9007, 'Disabled', 'User', '******-555-1007', false, 'America/Chicago', 'Test disabled user', true),
  ('550e8400-e29b-41d4-a716-************', 9008, 'DeviceOnly', 'User', '******-555-1008', false, 'America/Chicago', 'Test user with device group permissions only', false),
  ('550e8400-e29b-41d4-a716-************', 9009, 'Reports', 'User', '******-555-1009', false, 'America/Chicago', 'Test user with reports permissions only', false),
-- Add Keycloak admin user
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_keycloak_admin'), 9999, 'Admin', 'Synapse', null, false, 'America/Chicago', 'Keycloak admin user for Synapse organization', false),
-- Add onramp_it user for integration testing
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_onramp_it'), 9010, 'Onramp', 'IT', '******-555-1010', false, 'America/Chicago', 'Onramp Integration Test User with full permissions', false)
ON CONFLICT (Id) DO NOTHING;

-- Test AuthMethods for permission test users
INSERT INTO {{AuthMethod}} (Id, UserId, Type, UserName, Email, PasswordHash, IsEnabled) VALUES 
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440004', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
  ('550e8400-e29b-41d4-a716-446655440105', '550e8400-e29b-41d4-a716-446655440005', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
  ('550e8400-e29b-41d4-a716-446655440106', '550e8400-e29b-41d4-a716-446655440006', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
  ('550e8400-e29b-41d4-a716-446655440107', '550e8400-e29b-41d4-a716-************', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', false),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true),
-- Add onramp_it user auth method
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_onramp_it'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_onramp_it'), 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true)
ON CONFLICT (Id) DO NOTHING;

-- Test Location Groups
INSERT INTO {{LocationGroups}} (Id, OrganizationId, Name) VALUES
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Test Location Group 1'),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Test Location Group 2');

-- Test Device Groups
INSERT INTO {{DeviceGroups}} (Id, OrganizationId, Name) VALUES
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Test Device Group 1'),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Test Device Group 2');

-- Test Memberships for permission test users
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId) VALUES
  -- Admin user: member of both test organizations
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'),
  -- Manager user: member of test org 1 only
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'),
  -- Technician user: member of test org 1 only
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'),
  -- Custom user: member of test org 1 only
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'),
  -- Multi-org user: member of both test organizations
  ('550e8400-e29b-41d4-a716-446655440206', '550e8400-e29b-41d4-a716-446655440105', '550e8400-e29b-41d4-a716-************'),
  ('550e8400-e29b-41d4-a716-446655440207', '550e8400-e29b-41d4-a716-446655440105', '550e8400-e29b-41d4-a716-************'),
  -- Anonymous user: member of test org 1 only
  ('550e8400-e29b-41d4-a716-446655440208', '550e8400-e29b-41d4-a716-446655440106', '550e8400-e29b-41d4-a716-************'),
  -- Disabled user: member of test org 1 only (but user is disabled)
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440107', '550e8400-e29b-41d4-a716-************'),
  -- Device-only user: member of both test organizations
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'),
  -- Reports user: member of test org 1 only
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'),
-- onramp_it user: member of Synapse organization
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_onramp_it'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_onramp_it'), '55d832bf-cd0d-5c8d-b06c-c0f73ae8b7cd')
ON CONFLICT (Id) DO NOTHING;

-- Test Custom Roles for permission testing
INSERT INTO {{CustomRole}} (Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier, Name, Description, IsDeletable) VALUES
  -- Admin roles for test organizations
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'mun_admin', 'municipality', 'Test Org 1 Admin', 'Admin role for test organization 1', false),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'mun_admin', 'municipality', 'Test Org 2 Admin', 'Admin role for test organization 2', false),
  -- Manager roles for test organizations
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'mun_manager', 'municipality', 'Test Org 1 Manager', 'Manager role for test organization 1', true),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'mun_manager', 'municipality', 'Test Org 2 Manager', 'Manager role for test organization 2', true),
  -- Technician roles for test organizations
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'mun_technician', 'municipality', 'Test Org 1 Technician', 'Technician role for test organization 1', true),
  ('550e8400-e29b-41d4-a716-446655440306', '550e8400-e29b-41d4-a716-************', 'mun_technician', 'municipality', 'Test Org 2 Technician', 'Technician role for test organization 2', true),
  -- Custom role based on technician template with overrides
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'mun_technician', 'municipality', 'Test Custom Role', 'Custom role with permission overrides', true),
  -- Anonymous roles for test organizations
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'mun_anonymous', 'municipality', 'Test Org 1 Anonymous', 'Anonymous role for test organization 1', false),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'mun_anonymous', 'municipality', 'Test Org 2 Anonymous', 'Anonymous role for test organization 2', false),
-- onramp_it custom role based on syn_admin template
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_onramp_it_admin'), '55d832bf-cd0d-5c8d-b06c-c0f73ae8b7cd', 'syn_admin', 'synapse', 'Onramp IT Admin', 'Full admin permissions for onramp integration tests', true);

-- Custom Role Permission Overrides for the custom role user
INSERT INTO {{CustomRolePermission}} (CustomRoleId, PermissionIdentifier, Value) VALUES
  -- Override for custom role: enable org_manage_devices (normally false for technician)
  ('550e8400-e29b-41d4-a716-************', 'org_manage_devices', true),
  -- Override for custom role: disable device_group_manage_devices (normally true for technician)
  ('550e8400-e29b-41d4-a716-************', 'device_group_delete_devices', false);

-- Add org_aps_factory_reset <NAME_EMAIL> user (Cornelius admin role)
INSERT INTO {{CustomRolePermission}} (CustomRoleId, PermissionIdentifier, Value) VALUES
  ('550e8400-e29b-41d4-a716-************', 'org_aps_factory_reset', true);

-- Add syn_admin permissions for onramp_it custom role
INSERT INTO {{CustomRolePermission}} (CustomRoleId, PermissionIdentifier, Value) 
SELECT uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_onramp_it_admin'), trp.permissionidentifier, true 
FROM {{TemplateRolePermission}} trp 
WHERE trp.templateroleidentifier = 'syn_admin';

-- Organization Role Assignments for permission test users
INSERT INTO {{OrgRoleAssignments}} (MembershipId, RoleId) VALUES
  -- Admin user gets admin roles in both organizations
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Org 1 Admin
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Org 2 Admin
  -- Manager user gets manager role in test org 1
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Org 1 Manager
  -- Technician user gets technician role in test org 1
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Org 1 Technician
  -- Custom user gets custom role in test org 1
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Custom Role
  -- Multi-org user gets manager role in test org 1, technician role in test org 2
  ('550e8400-e29b-41d4-a716-446655440206', '550e8400-e29b-41d4-a716-************'), -- Test Org 1 Manager
  ('550e8400-e29b-41d4-a716-446655440207', '550e8400-e29b-41d4-a716-446655440306'), -- Test Org 2 Technician
  -- Anonymous user gets anonymous role in test org 1
  ('550e8400-e29b-41d4-a716-446655440208', '550e8400-e29b-41d4-a716-************'), -- Test Org 1 Anonymous
  -- Disabled user gets admin role in test org 1 (but user is disabled)
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Org 1 Admin
  -- Reports user gets admin role in test org 1 (but only has reports permissions via custom overrides)
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Org 1 Admin
-- onramp_it user gets onramp IT admin role in Synapse organization
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_onramp_it'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_onramp_it_admin')); -- Onramp IT Admin

-- Device Group Role Assignments for permission test users
INSERT INTO {{DeviceGroupRoleAssignments}} (MembershipId, DeviceGroupId, RoleId) VALUES
  -- Admin user gets admin access to both device groups
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Device Group 1, Test Org 1 Admin
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Device Group 2, Test Org 2 Admin
  -- Manager user gets manager access to device group 1
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Device Group 1, Test Org 1 Manager
  -- Technician user gets technician access to device group 1
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Device Group 1, Test Org 1 Technician
  -- Custom user gets custom role access to device group 1
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Device Group 1, Test Custom Role
  -- Multi-org user gets manager access to device group 1 only
  ('550e8400-e29b-41d4-a716-446655440206', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Device Group 1, Test Org 1 Manager
  -- Device-only user gets technician access to both device groups (no org-level roles)
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Device Group 1, Test Org 1 Technician
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440306'); -- Test Device Group 2, Test Org 2 Technician

-- LocationGroupRoleAssignments: Give test users access to location groups
INSERT INTO {{LocationGroupRoleAssignments}} (MembershipId, LocationGroupId, RoleId) VALUES
  -- Admin user gets admin access to both test location groups
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Location Group 1, Test Org 1 Admin
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Location Group 2, Test Org 2 Admin
  -- Manager user gets manager access to location group 1
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Location Group 1, Test Org 1 Manager
  -- Technician user gets technician access to location group 1
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Location Group 1, Test Org 1 Technician
  -- Custom user gets custom role access to location group 1
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Location Group 1, Test Custom Role
  -- Multi-org user gets manager access to location group 1 only
  ('550e8400-e29b-41d4-a716-446655440206', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Location Group 1, Test Org 1 Manager
  -- Device-only user gets technician access to both location groups (but this is location-only)
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'), -- Test Location Group 1, Test Org 1 Technician
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440306'); -- Test Location Group 2, Test Org 2 Technician

-- Custom Role Permission Overrides for reports user (disable all except reports)
INSERT INTO {{CustomRolePermission}} (CustomRoleId, PermissionIdentifier, Value) VALUES
  -- Disable all organization permissions for reports user except reports
  ('550e8400-e29b-41d4-a716-************', 'org_view_users', false),
  ('550e8400-e29b-41d4-a716-************', 'org_manage_users', false),
  ('550e8400-e29b-41d4-a716-************', 'org_delete_users', false),
  ('550e8400-e29b-41d4-a716-************', 'org_view_settings', false),
  ('550e8400-e29b-41d4-a716-************', 'org_manage_settings', false),
  ('550e8400-e29b-41d4-a716-************', 'org_manage_device_groups', false),
  ('550e8400-e29b-41d4-a716-************', 'org_manage_location_groups', false),
  ('550e8400-e29b-41d4-a716-************', 'org_view_devices', false),
  ('550e8400-e29b-41d4-a716-************', 'org_manage_devices', false),
  ('550e8400-e29b-41d4-a716-************', 'org_delete_devices', false),
  ('550e8400-e29b-41d4-a716-************', 'device_group_manage_devices', false),
  ('550e8400-e29b-41d4-a716-************', 'device_group_delete_devices', false);
  -- Note: location_group permissions removed in CAT-555

-- Note: Reports permissions (org_view_reports, org_view_admin_reports) are enabled by default in mun_admin template

---------------------------------

-- Data for the gateway developers. Do not edit unless required to!!!
-- Organizations: Updated structure with UUID Id and OrigID
INSERT INTO {{Organization}} (Id, Name, Description, OrgTypeIdentifier) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Cornelius''s Organization', 'Cornelius Dev Organization', 'municipality'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam'), 'Tam''s Organization', 'Tam Dev Organization', 'municipality'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc'), 'Duc''s Organization', 'Duc Dev Organization', 'municipality'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh'), 'Minh''s Organization', 'Minh Dev Organization', 'municipality');

-- SoftwareGateways: Updated with UUID OrganizationId and MachineKey
INSERT INTO {{SoftwareGateway}} (Id, MachineKey, OrganizationId, APIKey, Token, Name, Description, Config) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), 'd5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Cornelius - Gateway', 'Dev Gateway', '{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "https://broker-dev.caterpillar.synapse-its.app/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": true, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_10'), 'TAMDEVGATEWAY', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam'), 'qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Tam - Dev Gateway', 'Dev Gateway', '{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "https://broker-dev.caterpillar.synapse-its.app/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": true, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_11'), 'DUCDEVGATEWAY', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc'), 'qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Duc - Dev Gateway', 'Dev Gateway', '{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "https://broker-dev.caterpillar.synapse-its.app/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": true, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_12'), 'MINHDEVGATEWAY', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh'), 'qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Minh - Dev Gateway', 'Dev Gateway', '{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "https://broker-dev.caterpillar.synapse-its.app/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": true, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}');

-- Locations: Create all locations before inserting devices
INSERT INTO {{Location}} (Id, Name, Description, Latitude, Longitude, OrganizationId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_1'), 'Westheimer @ Briargreen', 'Westheimer @ Briargreen', '29.7601', '-95.3701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_2'), 'Westheimer @ Highway 6', 'Westheimer @ Highway 6', '29.7601', '-95.5701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_3'), 'Westheimer @ Westhollow Dr', 'Westheimer @ Westhollow Dr', '29.7601', '-95.4701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_5'), 'Westheimer @ Panagard Dr', 'Westheimer @ Panagard Dr', '29.7601', '-95.2701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_6'), 'Westheimer @ Windchase Blvd', 'Westheimer @ Windchase Blvd', '29.7601', '-95.6701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_7'), 'Westheimer @ Polk Rd', 'Westheimer @ Polk Rd', '29.7601', '-95.7701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_8'), 'Westheimer @ Eldridge Rd', 'Westheimer @ Eldridge Rd', '29.7601', '-95.8701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_9'), 'Westheimer @ Briargreen', 'Westheimer @ Briargreen', '29.7601', '-95.9701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10'), 'Westheimer @ Briarwest Blvd', 'Westheimer @ Briarwest Blvd', '29.7601', '-96.0701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11'), 'Westheimer @ S. Dairy Ashford', 'Westheimer @ S. Dairy Ashford', '29.7601', '-96.1701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12'), 'Westheimer @ Shadow Briar Dr', 'Westheimer @ Shadow Briar Dr', '29.7601', '-96.2701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_13'), 'Westheimer @ W Houston Center Blvd', 'Westheimer @ W Houston Center Blvd', '29.7601', '-96.3701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_14'), 'Westheimer @ Kirkwood', 'Westheimer @ Kirkwood', '29.7601', '-96.4701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_15'), 'Westheimer @ Crescent Park Dr', 'Westheimer @ Crescent Park Dr', '29.7601', '-96.5701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_16'), 'Westheimer @ Woodland Park Dr', 'Westheimer @ Woodland Park Dr', '29.7601', '-96.6701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_17'), 'Westheimer @ Hayes Rd', 'Westheimer @ Hayes Rd', '29.7601', '-96.7701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_18'), 'Westheimer @ Wilcrest Dr', 'Westheimer @ Wilcrest Dr', '29.7601', '-96.8701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_19'), 'Westheimer @ Walnut Bend Ln', 'Westheimer @ Walnut Bend Ln', '29.7601', '-96.9701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_20'), 'Westheimer @ Rogersdale', 'Westheimer @ Rogersdale', '29.7601', '-97.0701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_21'), 'Westheimer @ Beltway 8', 'Westheimer @ Beltway 8', '29.7601', '-97.1701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_22'), '2018KCL Plano Lab Device', '2018KCL Plano Lab Device', '32.8888', '-96.7470', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_23'), '2018KCL Mock Device', '2018KCL Mock Device', '29.7601', '-95.2701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_MMU2-16LEip_Lab'), 'Plano Lab - MMU2-16LEip', 'Plano Lab - MMU2-16LEip', '32.7767', '32.7767', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_ECL2010_TypeF+_Lab'), 'Plano Lab - ECL2010', 'Plano Lab - ECL2010', '33.0918', '-96.6989', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_CMU2212_Lab'), 'Plano Lab - CMU2212', 'Plano Lab - CMU2212', '32.8888', '-96.7470', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10000'), 'Tam - MMU2-16LEip', 'Tam - MMU2-16LEip', '29.7601', '-95.3701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10001'), 'Tam - ECL2010', 'Tam - ECL2010', '29.7601', '-95.4701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10002'), 'Tam - CMU2212', 'Tam - CMU2212', '29.7601', '-95.5701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11000'), 'Duc - MMU2-16LEip', 'Duc - MMU2-16LEip', '29.7601', '-95.3701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11001'), 'Duc - ECL2010', 'Duc - ECL2010', '29.7601', '-95.4701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11002'), 'Duc - CMU2212', 'Duc - CMU2212', '29.7601', '-95.5701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12000'), 'Minh - MMU2-16LEip', 'Minh - MMU2-16LEip', '29.7601', '-95.3701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12001'), 'Minh - ECL2010', 'Minh - ECL2010', '29.7601', '-95.4701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12002'), 'Minh - CMU2212', 'Minh - CMU2212', '29.7601', '-95.5701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99100'), 'Test Device Location 1', 'Location for test device 1', '29.7601', '-95.3701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99101'), 'Test Device Location 2', 'Location for test device 2', '29.7601', '-95.4701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99102'), 'Test Device Location 3', 'Location for test device 3', '29.7601', '-95.5701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99103'), 'Test Device Location 4', 'Location for test device 4', '29.7601', '-95.6701', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'));

-- Devices: Updated structure with communication fields merged from DeviceCommunicationConfig
INSERT INTO {{Device}} (Id, OrigId, SoftwareGatewayId, LocationId, OrganizationId, Name, Description, IpAddress, Port, FlushConnectionMs, Type, EnableRealtime, IsEnabled) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_1'), 1, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Briargreen', 'Stuttgart', '127.0.0.1', 8081, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_2'), 2, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_2'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Highway 6', 'Windows', '127.0.0.1', 8082, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_3'), 3, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_3'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Westhollow Dr', 'RaspberryPi', '127.0.0.1', 8083, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_5'), 5, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_5'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Panagard Dr', 'NA', '127.0.0.1', 8085, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_6'), 6, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_6'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Windchase Blvd', 'NA', '127.0.0.1', 8086, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_7'), 7, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_7'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Polk Rd', 'NA', '127.0.0.1', 8087, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_8'), 8, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_8'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Eldridge Rd', 'NA', '127.0.0.1', 8088, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_9'), 9, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_9'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Briargreen', 'NA', '127.0.0.1', 8089, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10'), 10, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Briarwest Blvd', 'NA', '127.0.0.1', 8010, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11'), 11, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ S. Dairy Ashford', 'NA', '127.0.0.1', 8011, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12'), 12, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Shadow Briar Dr', 'NA', '127.0.0.1', 8012, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_13'), 13, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_13'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ W Houston Center Blvd', 'NA', '127.0.0.1', 8013, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_14'), 14, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_14'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Kirkwood', 'NA', '127.0.0.1', 8014, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_15'), 15, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_15'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Crescent Park Dr', 'NA', '127.0.0.1', 8015, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_16'), 16, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_16'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Woodland Park Dr', 'NA', '127.0.0.1', 8016, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_17'), 17, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_17'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Hayes Rd', 'NA', '127.0.0.1', 8017, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_18'), 18, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_18'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Wilcrest Dr', 'NA', '127.0.0.1', 8018, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_19'), 19, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_19'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Walnut Bend Ln', 'NA', '127.0.0.1', 8019, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_20'), 20, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_20'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Rogersdale', 'NA', '127.0.0.1', 8020, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_21'), 21, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_21'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Westheimer @ Beltway 8', 'NA', '127.0.0.1', 8021, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_22'), 22, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_22'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), '2018KCL Plano Lab Device', 'Lab', '**********', 10001, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_23'), 23, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_23'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), '2018KCL Mock Device', 'Mock', '127.0.0.1', 8084, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_MMU2-16LEip_Lab'), 5000, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_MMU2-16LEip_Lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Plano Lab - MMU2-16LEip', 'Mock', '**********', 10001, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_ECL2010_TypeF+_Lab'), 5001, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_ECL2010_TypeF+_Lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Plano Lab - ECL2010', 'Mock', '**********', 10001, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_CMU2212_Lab'), 5002, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_CMU2212_Lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Plano Lab - CMU2212', 'Mock', '**********', 10001, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10000'), 10000, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_10'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10000'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam'), 'Tam - MMU2-16LEip', 'NA', '127.0.0.1', 8091, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10001'), 10001, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_10'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10001'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam'), 'Tam - ECL2010', 'NA', '127.0.0.1', 8092, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10002'), 10002, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_10'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10002'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam'), 'Tam - CMU2212', 'NA', '127.0.0.1', 8093, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11000'), 11000, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_11'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11000'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc'), 'Duc - MMU2-16LEip', 'NA', '127.0.0.1', 8101, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11001'), 11001, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_11'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11001'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc'), 'Duc - ECL2010', 'NA', '127.0.0.1', 8102, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11002'), 11002, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_11'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11002'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc'), 'Duc - CMU2212', 'NA', '127.0.0.1', 8103, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12000'), 12000, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_12'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12000'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh'), 'Minh - MMU2-16LEip', 'NA', '127.0.0.1', 8201, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12001'), 12001, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_12'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12001'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh'), 'Minh - ECL 2010', 'NA', '127.0.0.1', 8202, 500, 'EDI_LEGACY', true, true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12002'), 12002, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_12'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12002'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh'), 'Minh - CMU2212', 'NA', '127.0.0.1', 8203, 500, 'EDI_LEGACY', true, true),
  ('fdf164e6-4899-4725-bfae-5141d78887d4', 99100, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99100'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Device for test1', 'test1', '127.0.0.1', 9000, 500, 'EDI_LEGACY', true, true),
  ('3fbe6018-9eef-4bb6-ab9c-03663cc7b3ba', 99101, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99101'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Device for test2', 'test2', '127.0.0.1', 9001, 500, 'EDI_LEGACY', true, true),
  ('426b0919-812b-490d-8b48-b0ac788b9712', 99102, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99102'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Device for test3', 'test3', '127.0.0.1', 9002, 500, 'EDI_LEGACY', true, true),
  ('550e8400-e29b-41d4-a716-************', 99103, uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99103'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Device for test4', 'test4', '127.0.0.1', 9003, 500, 'EDI_LEGACY', true, true);

-- Device instructions: DeviceId references Device.ID
INSERT INTO {{SoftwareGatewayInstruction}} (UserId, DeviceId, Instruction, DateQueued, DateReceived, Status) VALUES
  ('550e8400-e29b-41d4-a716-************', uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_1'), 'get_device_logs', CURRENT_TIMESTAMP, null, 'queued'),
  ('550e8400-e29b-41d4-a716-************', uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_1'), 'get_device_logs', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'received'),
  ('550e8400-e29b-41d4-a716-************', uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_2'), 'get_device_logs', CURRENT_TIMESTAMP, null, 'queued');

-- DeviceMonitorName: DeviceId now UUID, references Device.Id
INSERT INTO {{DeviceMonitorName}} (DeviceId, MonitorId, MonitorName, PubsubTimestamp, UpdatedAt) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_1'), 272, 'Test device 1', '2025-06-01 00:00:00', '2025-06-01 00:00:00'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_3'), 999, 'Test device 2', '2025-06-01 00:00:00', '2025-06-01 00:00:00'),
  ('fdf164e6-4899-4725-bfae-5141d78887d4', 123, 'User Set Name 1', '2025-06-01 00:00:00', '2025-06-01 00:00:00'),
  ('3fbe6018-9eef-4bb6-ab9c-03663cc7b3ba', 456, 'Very long user set name which causes truncation', '2025-06-01 00:00:00', '2025-06-01 00:00:00'),
  ('426b0919-812b-490d-8b48-b0ac788b9712', 789, 'Short', '2025-06-01 00:00:00', '2025-06-01 00:00:00'),
  ('550e8400-e29b-41d4-a716-************', 555, '', '2025-06-01 00:00:00', '2025-06-01 00:00:00');

-- DeviceRMSEngine: DeviceId now UUID, references Device.Id
INSERT INTO {{DeviceRMSEngine}} (DeviceId, EngineVersion, EngineRevision, PubsubTimestamp, UpdatedAt) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_1'), '0.1', '0.2', '2025-06-01 00:00:00', '2025-06-01 00:00:00'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_3'), '0.3', '0.4', '2025-06-01 00:00:00', '2025-06-01 00:00:00');

-- DeviceLog: DeviceId now UUID, references Device.Id
INSERT INTO {{DeviceLog}} (DeviceId, DateUploaded, LogId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_1'), '2025-05-19T23:55:50Z', '550e8400-e29b-41d4-a716-************'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_3'), '2025-06-19T23:55:50Z', '992e8400-e29b-41d4-a716-************');

-- Users: Updated structure with UUID Id and OrigID
INSERT INTO {{User}} (Id, OrigId, FirstName, LastName, Mobile, NotificationSmsEnabled, IanaTimezone, Description) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_test'), 1, 'Test', 'User', '******-555-1234', true, 'America/Chicago', 'Primary test user'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_test1'), 2, 'Test1', 'User', '******-555-1234', false, 'America/Chicago', 'Secondary test user');

-- UserToken: UserId references User.Id
INSERT INTO {{UserToken}} (UserId, JwtToken, JwtTokenSha256, Created, Expiration) VALUES 
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_test1'), 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XFgUWHHtITccNQc-hnZRqXRkPLGz2eY0Q9SQANGCTp4Xv1oWaIZcSk_lxRe-Szd0p5kw0qe05KdMDZD2zF-zJ7-L9enMBOg-UmcdgRlogTq7TP5Is2a9EJ1KWgDCNHMvUi_u8U8qjR1Hvrn6lXBad5DfbhyTJ-HZNwAbfaAT0h_YJ73rHRCAAG0jXXo44u4m5CnHP0HhHXkGCznntsu0bgcsS5fbyqnSIhYxcXg14G2o0eh8GO3QCijr_HwcdutAjGqIL5zEBcBKceWvt4WTENHMH-0HHeMLjn1xpbXMJixm4xhvdcGH3pmDyj8lVT7uKWc6YnRPHPTf7KJdmRaLkw', 'd2cbd89168cfcf5d950ce54d85e848550c8ba9d0cbd838279e4b7adb92c49645', '2025-06-05 15:55:44.391', '2030-09-03 15:55:44.389'); 

-- AuthMethods for users
INSERT INTO {{AuthMethod}} (Id, UserId, Type, UserName, Email, PasswordHash) VALUES 
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_test'), 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_test1'), 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29');

-- Additional users for broker integration tests (use password: test2pass)
INSERT INTO {{User}} (Id, OrigId, FirstName, LastName, Mobile, NotificationSmsEnabled, IanaTimezone, Description, IsDeleted) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_test3'), 3, 'Test3', 'User', '******-555-1234', false, 'America/Chicago', 'Integration test user 3', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_test4'), 4, 'Test4', 'User', '******-555-1234', false, 'America/Chicago', 'Integration test user 4', false);

-- Auth methods for test3/test4 with sha256('test2pass')
INSERT INTO {{AuthMethod}} (Id, UserId, Type, UserName, Email, PasswordHash, IsEnabled) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test3'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_test3'), 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '88b34ac9554dfff4afaa5fa3a47540b9137eb219e40c5ca7f635157fd684aafa', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test4'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_test4'), 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '88b34ac9554dfff4afaa5fa3a47540b9137eb219e40c5ca7f635157fd684aafa', true);

-- Ensure these users are members of the demo organization used in tests
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test3_demo'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test3'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test4_demo'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test4'), 'c469a554-f7a8-5de5-a57e-e1ba16f970d3');

-- Assign demo Admin role to test users to enable data/device and instruction permissions
INSERT INTO {{OrgRoleAssignments}} (MembershipId, RoleId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test3_demo'), '12dc75d1-13ac-5bbe-810c-0c40bcddca21'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test4_demo'), '12dc75d1-13ac-5bbe-810c-0c40bcddca21');

-- Give device group access in demo org
INSERT INTO {{DeviceGroupRoleAssignments}} (MembershipId, DeviceGroupId, RoleId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test3_demo'), '0a0840b7-2528-576a-94c2-6a9d31efb31f', '12dc75d1-13ac-5bbe-810c-0c40bcddca21'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test4_demo'), '0a0840b7-2528-576a-94c2-6a9d31efb31f', '12dc75d1-13ac-5bbe-810c-0c40bcddca21');

-- AuthMethod for keycloak user
INSERT INTO {{AuthMethod}} (Id, UserId, Type, Sub, Issuer, Email, IsEnabled) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_keycloak_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_keycloak_admin'), 'OIDC', '1b08b49c-804c-4d73-98e8-b23f885a3613', 'http://keycloak:8080/realms/onramp-dev', '<EMAIL>', true),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_keycloak_admin2'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_keycloak_admin'), 'OIDC', '1b08b49c-804c-4d73-98e8-b23f885a3613', 'http://localhost:8091/realms/onramp-dev', '<EMAIL>', true);

-- Add USERNAME_PASSWORD auth method for synapse admin user for testing
INSERT INTO {{AuthMethod}} (Id, UserId, Type, UserName, Email, PasswordHash, IsEnabled) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_synapse_admin_password'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_keycloak_admin'), 'USERNAME_PASSWORD', 'synapse_admin', '<EMAIL>', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true);

-- Memberships: AuthMethodId references AuthMethod.Id, OrganizationId references Organization.Id
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId) VALUES
  -- Test user memberships in all organizations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_test'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test'), '550e8400-e29b-41d4-a716-************'), -- test org
  -- Test1 user memberships in all organizations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_test1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh')),
  -- Keycloak admin user membership in Synapse organization
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_keycloak_admin_synapse'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_keycloak_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_keycloak_admin_synapse2'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_keycloak_admin2'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse')),
  -- Username/password auth method membership for synapse admin user testing
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_synapse_admin_password'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_synapse_admin_password'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse'));

-- DeviceGroups: Create device groups for each organization
INSERT INTO {{DeviceGroups}} (Id, OrganizationId, Name) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Cornelius Dev Devices'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam'), 'Tam Dev Devices'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc'), 'Duc Dev Devices'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh'), 'Minh Dev Devices');

-- LocationGroups: Create location groups for each organization
INSERT INTO {{LocationGroups}} (Id, OrganizationId, Name) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Cornelius Westheimer Corridor'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Cornelius Lab Locations'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam'), 'Tam Dev Locations'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc'), 'Duc Dev Locations'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh'), 'Minh Dev Locations'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_test_group_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Test Location Group 1'),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_test_group_2'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'Test Location Group 2');

-- DeviceGroupDevices: Assign devices to their respective device groups
INSERT INTO {{DeviceGroupDevices}} (DeviceGroupId, DeviceId) VALUES
  -- Cornelius's devices (SoftwareGateway 1)
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_1')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_2')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_3')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_5')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_6')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_7')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_8')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_9')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_13')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_14')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_15')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_16')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_17')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_18')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_19')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_20')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_21')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_22')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_23')),
  -- Tam's devices (SoftwareGateway 10)
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10000')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10001')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_10002')),
  -- Duc's devices (SoftwareGateway 11)
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11000')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11001')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_11002')),
  -- Minh's devices (SoftwareGateway 12)
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12000')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12001')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_12002'));

-- LocationGroupLocations: Assign locations to their respective location groups
INSERT INTO {{LocationGroupLocations}} (LocationGroupId, LocationId) VALUES
  -- Cornelius Westheimer Corridor locations (main street locations)
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_1')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_2')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_3')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_5')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_6')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_7')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_8')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_9')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_13')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_14')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_15')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_16')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_17')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_18')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_19')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_20')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_21')),
  -- Cornelius Lab locations (lab and test devices)
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_22')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_23')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_MMU2-16LEip_Lab')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_ECL2010_TypeF+_Lab')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_CMU2212_Lab')),
  -- Tam's locations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10000')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10001')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_10002')),
  -- Duc's locations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11000')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11001')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_11002')),
  -- Minh's locations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12000')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12001')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_12002')),
  -- Test location groups for permission testing
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_test_group_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99100')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_test_group_1'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99101')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_test_group_2'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99102')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_test_group_2'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_99103'));

-- CustomRoles: Create admin roles for each organization based on mun_admin template
INSERT INTO {{CustomRole}} (Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier, Name, Description, IsDeletable) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_cornelius'), 'mun_admin', 'municipality', 'Cornelius Org Admin', 'Admin role for Cornelius organization', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_tam'), 'mun_admin', 'municipality', 'Tam Org Admin', 'Admin role for Tam organization', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_duc'), 'mun_admin', 'municipality', 'Duc Org Admin', 'Admin role for Duc organization', false),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_minh'), 'mun_admin', 'municipality', 'Minh Org Admin', 'Admin role for Minh organization', false);

-- OrgRoleAssignments: Assign test users to organization admin roles
INSERT INTO {{OrgRoleAssignments}} (MembershipId, RoleId) VALUES
  -- Test User (<EMAIL>) gets admin access to all organizations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_test'), '550e8400-e29b-41d4-a716-************'),
  -- Test1 User (<EMAIL>) gets admin access to all organizations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin')),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin')),
  -- Keycloak admin user gets admin access to Synapse organization
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_keycloak_admin_synapse'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_synapse_admin')),
  -- Username/password auth method gets synapse admin role for testing
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_synapse_admin_password'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_synapse_admin'));

-- DeviceGroupRoleAssignments: Give test users access to all device groups
INSERT INTO {{DeviceGroupRoleAssignments}} (MembershipId, DeviceGroupId, RoleId) VALUES
  -- Test User gets admin access to all device groups
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')), -- Cornelius devices
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin')), -- Tam devices
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin')), -- Duc devices
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin')), -- Minh devices
  -- Test1 User gets admin access to all device groups
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')), -- Cornelius devices
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin')), -- Tam devices
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin')), -- Duc devices
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin')); -- Minh devices

-- LocationGroupRoleAssignments: Give test users access to location groups
INSERT INTO {{LocationGroupRoleAssignments}} (MembershipId, LocationGroupId, RoleId) VALUES
  -- Test User gets admin access to all location groups
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')), -- Cornelius Westheimer
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')), -- Cornelius Lab
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin')), -- Tam locations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin')), -- Duc locations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin')), -- Minh locations
  -- Test1 User gets admin access to all location groups
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_westheimer'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')), -- Cornelius Westheimer
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_cornelius'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_cornelius_lab'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_cornelius_admin')), -- Cornelius Lab
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_tam'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_tam_admin')), -- Tam locations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_duc'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_duc_admin')), -- Duc locations
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_test1_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_minh'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_minh_admin')); -- Minh locations

WITH orgs AS (
  SELECT
   	Id   AS organizationId,
    Name AS organizationName
  FROM {{Organization}}
)
INSERT INTO {{GatewayConfigTemplate}}
  (Id, Name, OrganizationId, Description)
SELECT
  -- deterministic UUID per org
  uuid_generate_v5(
    uuid_nil(),
    format('SYNAPSE_softwaregatewayconfigtemplate_%s', organizationId::text)
  )                                                       AS I,
  format('Default Software Gateway Template for %s', organizationName) AS Name,
  organizationId,
  'Default configuration for software gateways'           AS Description
FROM orgs
WHERE NOT EXISTS (
  SELECT 1
  FROM {{GatewayConfigTemplate}} t
  WHERE t.OrganizationId = orgs.organizationId
);

WITH all_templates AS (
  SELECT Id AS templateId
  FROM {{GatewayConfigTemplate}}
)
INSERT INTO {{GatewayConfigTemplateSettings}}
  (gatewayConfigTemplateId, setting, value)
SELECT
  t.templateId,
  b.Setting,
  b.DefaultValue
FROM all_templates t
CROSS JOIN {{GatewayConfigTemplateBaseSettings}} b
WHERE NOT EXISTS (
  SELECT 1
  FROM {{GatewayConfigTemplateSettings}} s
  WHERE s.gatewayConfigTemplateId = t.templateId
    AND s.setting               = b.Setting
);

-- Update test gateways with their organization's default template IDs
UPDATE {{SoftwareGateway}} 
SET TemplateId = uuid_generate_v5(
  uuid_nil(), 
  format('SYNAPSE_softwaregatewayconfigtemplate_%s', OrganizationId::text)
)
WHERE TemplateId IS NULL;

WITH chosen_gateway AS (
  SELECT Id AS softwareGatewayId
  FROM {{SoftwareGateway}}
  WHERE Id = '550e8400-e29b-41d4-a716-************'
  LIMIT 1
)
INSERT INTO {{GatewayConfigTemplateSettingOverrides}}
  (softwareGatewayId, setting, value)
SELECT
  cg.softwareGatewayId,
  s.setting,
  s.value
FROM
  chosen_gateway cg
CROSS JOIN LATERAL
  (VALUES
    ('log_level',                              'debug'),
    ('device_state_send_frequency_seconds',    '120')
  ) AS s(setting, value);

INSERT INTO {{GatewayConfigTemplateBaseSettings}} (Setting, DefaultValue, Name, Description, Format)
VALUES
('rest_api_device_endpoint',
   'https://api.dev.synapse-its.app/api/v3/gateway/ingest',
   'Rest API Device Endpoint',
   'The cloud endpoint for ingesting device data.',
   '{"type": "string", "versions": ["v3"]}'
  );

-- Gateway Endpoint Verification Organization and User
INSERT INTO {{Organization}} (Id, Name, Description, OrgTypeIdentifier) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_gateway_endpoint_verify'), 'Gateway Endpoint Verification', 'Organization for testing gateway endpoint modifications', 'municipality');

-- Software Gateway for endpoint verification
INSERT INTO {{SoftwareGateway}} (Id, MachineKey, OrganizationId, APIKey, Token, Name, Description, Config, IsEnabled) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_endpoint_verify'), 'ENDPOINT_VERIFY_GATEWAY_KEY', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_gateway_endpoint_verify'), 'endpoint_verify_qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Gateway Endpoint Verify Gateway', 'Gateway for testing endpoint modifications', '{"log_level": "info", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "https://broker-dev.caterpillar.synapse-its.app/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": true, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}', true);

-- User for endpoint verification
INSERT INTO {{User}} (Id, OrigId, FirstName, LastName, Mobile, NotificationSmsEnabled, IanaTimezone, Description, IsDeleted) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_gateway_endpoint_verify'), 60001, 'Gateway', 'EndpointVerify', '', false, 'America/Chicago', 'User for verifying gateway endpoint modifications', false);

-- Auth method for endpoint verification user
INSERT INTO {{AuthMethod}} (Id, UserId, Type, UserName, Email, PasswordHash, IsEnabled) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_gateway_endpoint_verify'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_gateway_endpoint_verify'), 'USERNAME_PASSWORD', 'gateway_endpoint_verify', '<EMAIL>', '252666d427fda40b3cb068d5fd8879788c0036b5c37b62b8bdfdb8bc0b2c430a', true);

-- Membership for endpoint verification user
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_gateway_endpoint_verify'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_gateway_endpoint_verify'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_gateway_endpoint_verify'));

-- Custom admin role for endpoint verification organization
INSERT INTO {{CustomRole}} (Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier, Name, Description, IsDeletable) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_gateway_endpoint_verify_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_gateway_endpoint_verify'), 'mun_admin', 'municipality', 'Gateway Endpoint Verify Admin', 'Admin role for gateway endpoint verification organization', false);

-- Organization role assignment for endpoint verification user
INSERT INTO {{OrgRoleAssignments}} (MembershipId, RoleId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_gateway_endpoint_verify'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_gateway_endpoint_verify_admin'));

-- Add synapse_manage_gateway_settings permission for gateway endpoint verification admin role
INSERT INTO {{CustomRolePermission}} (CustomRoleId, PermissionIdentifier, Value) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_gateway_endpoint_verify_admin'), 'synapse_manage_gateway_settings', true);

-- Create gateway config template for the new organization
INSERT INTO {{GatewayConfigTemplate}} (Id, Name, OrganizationId, Description) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregatewayconfigtemplate_gateway_endpoint_verify'), 'Default Software Gateway Template for Gateway Endpoint Verification', uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_gateway_endpoint_verify'), 'Default configuration for gateway endpoint verification');

-- Add base settings to the new template
INSERT INTO {{GatewayConfigTemplateSettings}} (gatewayConfigTemplateId, setting, value)
SELECT
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregatewayconfigtemplate_gateway_endpoint_verify'),
  b.Setting,
  b.DefaultValue
FROM {{GatewayConfigTemplateBaseSettings}} b;

-- Update the new gateway with its template ID
UPDATE {{SoftwareGateway}} 
SET TemplateId = uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregatewayconfigtemplate_gateway_endpoint_verify')
WHERE Id = uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_endpoint_verify');

-- Local Testing Organization Setup
-- Organization for local development and testing
INSERT INTO {{Organization}} (Id, Name, Description, OrgTypeIdentifier) VALUES
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org'), 'Local Testing Organization', 'Organization for local development and testing', 'municipality');

-- Custom Roles for Local Testing Organization
INSERT INTO {{CustomRole}} (Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier, Name, Description, IsDeletable) VALUES
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_role_admin'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org'), 'mun_admin', 'municipality', 'Admin', 'Administrative permissions for local testing', false),
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_role_manager'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org'), 'mun_manager', 'municipality', 'Manager', 'Manager permissions for local testing', true),
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_role_technician'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org'), 'mun_technician', 'municipality', 'Technician', 'Technician permissions for local testing', true),
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_role_anonymous'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org'), 'mun_anonymous', 'municipality', 'Anonymous', 'Deny all permissions', false);

-- Gateway Config Template for Local Testing
INSERT INTO {{GatewayConfigTemplate}} (Id, Name, OrganizationId, Description) VALUES
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_gateway_config_template'), 'Local Test Gateway Config Template', uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org'), 'Config template for local test gateway with endpoint override');

-- Add base settings to the local test template
INSERT INTO {{GatewayConfigTemplateSettings}} (gatewayConfigTemplateId, setting, value)
SELECT
  uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_gateway_config_template'),
  b.Setting,
  b.DefaultValue
FROM {{GatewayConfigTemplateBaseSettings}} b;

-- Override rest_api_device_endpoint for local testing
UPDATE {{GatewayConfigTemplateSettings}}
SET value = 'http://127.0.0.1:8080/api/v3/gateway/ingest'
WHERE gatewayConfigTemplateId = uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_gateway_config_template')
  AND setting = 'rest_api_device_endpoint';

-- Override log_level to debug for local testing
UPDATE {{GatewayConfigTemplateSettings}}
SET value = 'debug'
WHERE gatewayConfigTemplateId = uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_gateway_config_template')
  AND setting = 'log_level';

-- Override log_file_max_size_mb to 8 for local testing
UPDATE {{GatewayConfigTemplateSettings}}
SET value = '8'
WHERE gatewayConfigTemplateId = uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_gateway_config_template')
  AND setting = 'log_file_max_size_mb';

-- Software Gateway for Local Testing
INSERT INTO {{SoftwareGateway}} (Id, MachineKey, OrganizationId, APIKey, Token, Name, Description, Config, IsEnabled, TemplateId) VALUES
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_gateway'), 'LOCAL_TEST_GATEWAY_KEY', uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org'), 'localtest_qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Local Test Gateway', 'Gateway for local testing', '', true, uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_gateway_config_template'));

-- Locations for Local Test Devices
INSERT INTO {{Location}} (Id, Name, Description, Latitude, Longitude, OrganizationId)
SELECT
  uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_location_' || i::text),
  'Local Test Location ' || i::text,
  'Location for local test device ' || i::text || ' (stress test)',
  33.0200 + (i * 0.0001), -- Slightly vary latitude
  -96.7300 + (i * 0.0001), -- Slightly vary longitude
  uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org')
FROM generate_series(1, 500) AS i;

-- Devices for Local Testing
-- All devices use 127.0.0.1 IP with ports starting at 10010
INSERT INTO {{Device}} (Id, OrigId, SoftwareGatewayId, LocationId, OrganizationId, Name, Description, IpAddress, Port, Type, FlushConnectionMs, EnableRealtime, IsEnabled)
SELECT
  uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_device_' || i::text),
  8000 + i, -- OrigId starts at 8001
  uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_gateway'),
  uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_location_' || i::text),
  uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org'),
  'Local Test Device ' || i::text,
  'Legacy device for local testing (port ' || (10009 + i)::text || ') - stress test',
  '127.0.0.1',
  10009 + i, -- Ports from 10010 to 10509
  'EDI_LEGACY',
  400,
  true,
  true
FROM generate_series(1, 500) AS i;

-- Locations for Next Gen Devices
INSERT INTO {{Location}} (Id, Name, Description, Latitude, Longitude, OrganizationId) VALUES
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_location_ng_1'), 'Local Test NG Location 1', 'Location for local test next gen device 1', 33.0600, -96.7600, uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org')),
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_location_ng_2'), 'Local Test NG Location 2', 'Location for local test next gen device 2', 33.0610, -96.7610, uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org'));

-- Next Gen Devices for Local Testing (disabled for now)
INSERT INTO {{Device}} (Id, OrigId, SoftwareGatewayId, LocationId, OrganizationId, Name, Description, IpAddress, Port, Type, FlushConnectionMs, EnableRealtime, IsEnabled) VALUES
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_device_ng_1'), 8501, uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_gateway'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_location_ng_1'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org'), 'Local Test Next Gen Device 1', 'Next Gen device for local testing (**********:110)', '**********', 110, 'EDI_NEXT_GEN', 400, true, false),
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_device_ng_2'), 8502, uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_gateway'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_location_ng_2'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org'), 'Local Test Next Gen Device 2', 'Next Gen device for local testing (**********:995)', '**********', 995, 'EDI_NEXT_GEN', 400, true, false);

-- Device Groups for Local Testing
INSERT INTO {{DeviceGroups}} (Id, OrganizationId, Name) VALUES
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_device_group_stress_test'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org'), 'Local Test Stress Test Devices'),
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_device_group_nextgen'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org'), 'Local Test Next Gen Devices');

-- Device Group Devices for Local Testing
INSERT INTO {{DeviceGroupDevices}} (DeviceGroupId, DeviceId)
SELECT
  uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_device_group_stress_test'),
  uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_device_' || i::text)
FROM generate_series(1, 500) AS i;

-- Device Group Devices for Next Gen Devices
INSERT INTO {{DeviceGroupDevices}} (DeviceGroupId, DeviceId) VALUES
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_device_group_nextgen'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_device_ng_1')),
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_device_group_nextgen'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_device_ng_2'));

-- User for Local Testing
INSERT INTO {{User}} (Id, OrigId, FirstName, LastName, Mobile, NotificationSmsEnabled, IanaTimezone, Description, IsDeleted) VALUES
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_user_admin'), 8000, 'Local Test', 'Admin', '******-555-8000', false, 'America/Chicago', 'Admin user for local testing organization', false);

-- AuthMethod for Local Test User
INSERT INTO {{AuthMethod}} (Id, UserId, Type, UserName, Email, PasswordHash, IsEnabled) VALUES 
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_auth_admin'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_user_admin'), 'USERNAME_PASSWORD', '<EMAIL>', '<EMAIL>', '336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29', true);

-- Membership for Local Test User
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId) VALUES
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_membership_admin'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_auth_admin'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_org'));

-- Organization Role Assignment for Local Test User (Admin access)
INSERT INTO {{OrgRoleAssignments}} (MembershipId, RoleId) VALUES
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_membership_admin'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_role_admin'));

-- Device Group Role Assignments for Local Test User (Admin access to both device groups)
INSERT INTO {{DeviceGroupRoleAssignments}} (MembershipId, DeviceGroupId, RoleId) VALUES
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_membership_admin'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_device_group_stress_test'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_role_admin')),
  (uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_membership_admin'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_device_group_nextgen'), uuid_generate_v5(uuid_nil(), 'LOCAL_TEST_role_admin'));

-- THESE MUST ALWAYS BE LAST
SELECT setval('{{Device_OrigId_SEQ}}', coalesce((select max(origid) from {{Device}}),1));
SELECT setval('{{User_OrigId_SEQ}}', coalesce((select max(origid) from {{User}}),1));


UPDATE {{AuthMethod}}
SET PasswordHash = CONCAT('$sha256$hex:', PasswordHash)
WHERE Type = 'USERNAME_PASSWORD'
  AND PasswordHash IS NOT NULL
  AND PasswordHash != ''
  AND PasswordHash NOT LIKE '$sha256$hex:%'
  AND PasswordHash NOT LIKE '$argon2id$%';