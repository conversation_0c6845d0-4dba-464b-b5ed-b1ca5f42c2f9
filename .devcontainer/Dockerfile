# .devcontainer/Dockerfile
FROM golang:1.24

# Install OS tools, Node.js 24 (with npm), and Google Cloud SDK
RUN apt-get update \
  && apt-get install -y --no-install-recommends \
    git \
    curl \
    ca-certificates \
    build-essential \
    apt-transport-https \
    gnupg \
    less \
  \
  # ─── Install Node.js 24 (includes npm) ─────────────────────────────────────
  && curl -fsSL https://deb.nodesource.com/setup_24.x | bash - \
  && apt-get install -y nodejs \
  \
  # ─── Add Google Cloud SDK repo & install ─────────────────────────────────
  && curl https://packages.cloud.google.com/apt/doc/apt-key.gpg \
    | gpg --dearmor -o /usr/share/keyrings/cloud.google.gpg \
  && echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] \
    https://packages.cloud.google.com/apt cloud-sdk main" \
    | tee /etc/apt/sources.list.d/google-cloud-sdk.list \
  && apt-get update \
  && apt-get install -y google-cloud-cli \
  \
  && rm -rf /var/lib/apt/lists/*

# Install Angular CLI
RUN npm install -g @angular/cli@latest

# Install Go language tooling
# TODO: Need to update the gopls version when we update the golang version
RUN go install golang.org/x/tools/gopls@v0.18.1 \
  && go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest \
  && go install github.com/go-delve/delve/cmd/dlv@latest

# Configure gcloud defaults
RUN gcloud config set core/disable_usage_reporting true \
  && gcloud config set component_manager/disable_update_check true \
  && gcloud config configurations create gcp \
  && gcloud config set project caterpillar-sandbox \
  && gcloud config configurations create emulator \
  && gcloud config set project test-project \
  && gcloud config set api_endpoint_overrides/bigquery http://bigquery:9050/ --quiet

# Ensure the custom scripts are on PATH
ENV PATH="/workspace/.devcontainer/scripts:${PATH}"

WORKDIR /workspace

# Keep container alive for VS Code client to attach
CMD ["sh", "-c", "git config --global --add safe.directory /workspace && sleep infinity"]
