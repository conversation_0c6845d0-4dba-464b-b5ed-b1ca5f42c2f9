#!/usr/bin/env bash
set -euo pipefail

# only use colors if stdout is a TTY
if [[ -t 1 ]]; then
  BOLD='\033[1m'
  CMDCOL='\033[1;34m'   # bright blue
  DSCCOL='\033[0;33m'   # bold yellow
  RESET='\033[0m'
else
  BOLD='' CMDCOL='' DSCCOL='' RESET=''
fi

prog=$(basename "$0")        # "bqe"
args=("$@")                  # all user args

# Try longest sub-command first, then shorter prefixes
for (( i=${#args[@]}; i>0; i-- )); do
  # build candidate name: bqe-foo-bar-baz
  cmd="$prog"
  for (( j=0; j<i; j++ )); do
    cmd+="-${args[j]}"
  done

  # is it on PATH?
  if command -v "$cmd" >/dev/null 2>&1; then
    # hand off control, shifting off the pieces we consumed
    exec "$cmd" "${args[@]:i}"
  fi
done

# no sub-commands matched
# Help
if [[ "${args[0]:-}" == "help" || $# -eq 0 ]]; then
  echo "Usage: $prog <command> [args...]"
  echo
  echo "Available commands:"
  for full in $(compgen -c | grep -E "^${prog}-" | sort); do
    name=${full#${prog}-}
    file=$(command -v "$full")

    # Try to read Description, or default to "(no description)"
    desc=$(
      grep -m1 '^# Description:' "$file" 2>/dev/null \
      | cut -d: -f2- \
      | sed 's/^ *//'
    )
    [[ -z "$desc" ]] && desc="(no description)"

    # print the command+desc in color, then reset
    printf "  ${BOLD}${CMDCOL}%-6s${RESET} ${DSCCOL}%s${RESET}\n" "$name" "$desc"

    # Print the help text below the Description
    # (if it exists)
    if grep -q '^# Description:' "$file" 2>/dev/null; then
      awk -v namecol=6 -v leftpad=2 '
        BEGIN {
          # compute how many spaces before the 2nd column:
          col = leftpad + namecol + 1
          prefix = sprintf("%*s", col, "")
        }
        # first line: skip "Description" line, because it is already printed
        /^# Description:/ {
          printing = 1
          next
        }
        # subsequent lines that still start with "#"
        printing && /^#/ {
          sub(/^#[ \t]/, "")
          print prefix $0
          next
        }
        # as soon as we hit something that isn’t a comment, stop
        printing { exit }
      ' "$file"
    fi
  done
  exit 0
fi

echo "Error: unknown command '$prog ${args[*]:0:1}'" >&2
exit 1
