#!/usr/bin/env bash
# Description: Execute an arbitrary SQL query, replacing {{table}} placeholders with fully-qualified table names
#   bqe q [--format <format>] <SQL query>
#     Example: bqe q "SELECT * FROM {{table}} WHERE id = 1"
#     Example: bqe q --format csv "SELECT * FROM {{table}} WHERE id = 1"

set -euo pipefail

# Parse command line arguments
format_arg=""
sql_args=()

while [[ $# -gt 0 ]]; do
  case $1 in
    --format)
      if [[ $# -lt 2 ]]; then
        echo "Error: --format requires a value" >&2
        exit 1
      fi
      format_arg="--format=$2"
      shift 2
      ;;
    --help|-h)
      echo "Usage: bqe q [--format <format>] <SQL query>" >&2
      echo "  --format <format>  Output format (e.g., csv, json, prettyjson, etc.)" >&2
      echo "  <SQL query>        The SQL query to execute" >&2
      echo "" >&2
      echo "Examples:" >&2
      echo "  bqe q \"SELECT * FROM {{table}} WHERE id = 1\"" >&2
      echo "  bqe q --format csv \"SELECT * FROM {{table}} WHERE id = 1\"" >&2
      exit 0
      ;;
    *)
      sql_args+=("$1")
      shift
      ;;
  esac
done

if [[ ${#sql_args[@]} -lt 1 ]]; then
  echo "Usage: bqe q [--format <format>] <SQL query>" >&2
  echo "Run 'bqe q --help' for more information." >&2
  exit 1
fi

# 1) fetch namespace
ns=$(bqe-get namespace)
if [[ -z "$ns" ]]; then
  echo "Error: no namespace set. Run 'bqe namespace' to list or 'bqe namespace <name>' to set one." >&2
  exit 1
fi

dataset=$(bqe-dataset)
if [[ -z "$dataset" ]]; then
  echo "Error: no dataset set. Run 'bqe dataset' to list or 'bqe dataset <name>' to set one." >&2
  exit 1
fi
raw_sql="${sql_args[*]}"

# 2) replace every {{foo}} with `dataset.NS__foo`
#    single-quoting around the sed script means backticks are literal;
#    we break out to insert $dataset and $ns into the replacement.
final_sql=$(
  printf '%s\n' "$raw_sql" |
  sed -E 's/\{\{([[:alnum:]_]+)\}\}/`'"$dataset"'.'"$ns"'__\1`/g'
)

# 3) hand off to BigQuery
bq query --use_legacy_sql=false $format_arg "$final_sql"
