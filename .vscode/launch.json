{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Tests",
      "type": "go",
      "request": "launch",
      "mode": "test",
      "envFile": "${workspaceFolder}/infra/.env"
    },
    {
      "name": "Debug Onramp (Docker)",
      "type": "go",
      "request": "attach",
      "mode": "remote",
      "host": "onramp",
      "port": 40000,
      "showLog": true,
      "trace": "verbose",
    }
  ]
}