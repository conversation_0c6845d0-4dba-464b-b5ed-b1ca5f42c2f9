# Password Policy Implementation Guide

## Overview
This document provides a step-by-step implementation guide for updating the Change Password endpoint to enforce password policy as specified in the requirements. The implementation includes three main components:

1. **Validate Current Password** - Returns status indicating whether current password is correct
2. **Get Password Policy** - Returns regex and conditions for BE/FE validation  
3. **Update Change-Password API** - Enhanced validation on backend side

## Current State Analysis

### Existing Components
- **Password validation logic**: `shared/api/password/password.go` - `ValidatePassword()` function
- **Password update handler**: `shared/rest/onramp/user/auth-methods/authmethods.go` - `UpdatePasswordHandler`
- **Password schemas**: `shared/api/password/schemas.go` - Constants and request structures
- **Password errors**: `shared/api/password/errors.go` - Error definitions

### Current Password Policy Rules
- Minimum length: 8 characters (`MinUserPasswordLength`)
- Maximum length: 256 characters (`MaxUserPasswordLength`)
- Must contain: uppercase (A-Z), lowercase (a-z), number (0-9), special character (!@#$%^&*)

## Implementation Steps

### Step 1: Create Password Policy Response Structure

**File**: `shared/api/password/schemas.go`

Add new structures for password policy responses:

```go
// PasswordPolicy represents the password validation rules
type PasswordPolicy struct {
    MinLength        int      `json:"minLength"`
    MaxLength        int      `json:"maxLength"`
    RequireUppercase bool     `json:"requireUppercase"`
    RequireLowercase bool     `json:"requireLowercase"`
    RequireNumber    bool     `json:"requireNumber"`
    RequireSpecial   bool     `json:"requireSpecial"`
    SpecialChars     string   `json:"specialChars"`
    Regex            string   `json:"regex"`
    Rules            []string `json:"rules"`
}

// CurrentPasswordValidationRequest for validating current password
type CurrentPasswordValidationRequest struct {
    CurrentPassword string `json:"current_password"`
}

// CurrentPasswordValidationResponse for current password validation result
type CurrentPasswordValidationResponse struct {
    IsValid bool   `json:"isValid"`
    Message string `json:"message,omitempty"`
}
```

### Step 2: Create Password Policy Functions

**File**: `shared/api/password/password.go`

Add new functions for password policy management:

```go
// GetPasswordPolicy returns the current password policy configuration
func GetPasswordPolicy() *PasswordPolicy {
    return &PasswordPolicy{
        MinLength:        MinUserPasswordLength,
        MaxLength:        MaxUserPasswordLength,
        RequireUppercase: true,
        RequireLowercase: true,
        RequireNumber:    true,
        RequireSpecial:   true,
        SpecialChars:     "!@#$%^&*",
        Regex:            "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*]).{8,256}$",
        Rules: []string{
            "Password must be at least 8 characters long",
            "Password must not exceed 256 characters",
            "Password must contain at least one uppercase letter (A-Z)",
            "Password must contain at least one lowercase letter (a-z)",
            "Password must contain at least one number (0-9)",
            "Password must contain at least one special character (!@#$%^&*)",
        },
    }
}

// ValidateCurrentPasswordOnly validates only the current password without format requirements
func ValidateCurrentPasswordOnly(password string) error {
    trimmed := strings.TrimSpace(password)
    if trimmed == "" {
        return ErrPasswordEmpty
    }
    return nil
}
```

### Step 3: Create Password Policy Handler

**File**: `shared/rest/onramp/user/password-policy/handler.go` (New file)

```go
package passwordpolicy

import (
    "net/http"
    
    "synapse-its.com/shared/api/password"
    "synapse-its.com/shared/api/response"
)

// GetPasswordPolicyHandler returns the current password policy
// @Summary Get password policy
// @Description Returns password validation rules and regex patterns
// @Tags user, password
// @Produce json
// @Success 200 {object} response.SuccessResponse{data=password.PasswordPolicy}
// @Router /api/user/password-policy [get]
func GetPasswordPolicyHandler(w http.ResponseWriter, r *http.Request) {
    policy := password.GetPasswordPolicy()
    response.CreateSuccessResponse(policy, w)
}
```

### Step 4: Create Current Password Validation Handler

**File**: `shared/rest/onramp/user/auth-methods/current-password.go` (New file)

```go
package authmethods

import (
    "net/http"
    
    "github.com/google/uuid"
    "github.com/gorilla/mux"
    "synapse-its.com/shared/api/password"
    "synapse-its.com/shared/api/response"
    "synapse-its.com/shared/api/util"
    "synapse-its.com/shared/connect"
    "synapse-its.com/shared/logger"
)

// ValidateCurrentPasswordHandler validates the current password
// @Summary Validate current password
// @Description Validates if the provided current password is correct
// @Tags user, password
// @Accept json
// @Produce json
// @Param userId path string true "User ID"
// @Param authMethodId path string true "Authentication method ID"
// @Param request body password.CurrentPasswordValidationRequest true "Current password"
// @Success 200 {object} response.SuccessResponse{data=password.CurrentPasswordValidationResponse}
// @Failure 400 {object} response.BadRequestResponse
// @Failure 403 {object} response.BadRequestResponse
// @Router /api/user/{userId}/auth-methods/{authMethodId}/validate-current-password [post]
func ValidateCurrentPasswordHandler(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()
    
    // Get connections
    connections, err := connect.GetConnections(ctx)
    if err != nil {
        logger.Errorf("Error getting connections: %v", err)
        response.CreateInternalErrorResponse(w)
        return
    }
    defer connections.Close()
    
    pg := connections.PostgresConnection
    
    // Extract path parameters
    vars := mux.Vars(r)
    userID, err := uuid.Parse(vars["userId"])
    if err != nil {
        logger.Errorf("Invalid user ID: %v", err)
        response.CreateBadRequestResponse(w)
        return
    }
    
    authMethodID, err := uuid.Parse(vars["authMethodId"])
    if err != nil {
        logger.Errorf("Invalid auth method ID: %v", err)
        response.CreateBadRequestResponse(w)
        return
    }
    
    // Parse request body
    var req password.CurrentPasswordValidationRequest
    if err := util.ParseJSON(r.Body, &req); err != nil {
        logger.Errorf("Error parsing request body: %v", err)
        response.CreateBadRequestResponse(w)
        return
    }
    
    // Validate current password format (basic validation only)
    if err := password.ValidateCurrentPasswordOnly(req.CurrentPassword); err != nil {
        resp := &password.CurrentPasswordValidationResponse{
            IsValid: false,
            Message: "Current password is required",
        }
        response.CreateSuccessResponse(resp, w)
        return
    }
    
    // Validate auth method exists and belongs to user
    err = ValidateAuthMethod(pg, authMethodID, userID, string(authtypes.AuthMethodTypeUsernamePassword))
    if err != nil {
        logger.Errorf("Error validating auth method: %v", err)
        response.CreateForbiddenResponse(w)
        return
    }
    
    // Validate current password matches stored hash
    err = ValidateCurrentPassword(pg, authMethodID, req.CurrentPassword)
    if err != nil {
        resp := &password.CurrentPasswordValidationResponse{
            IsValid: false,
            Message: "Current password is incorrect",
        }
        response.CreateSuccessResponse(resp, w)
        return
    }
    
    // Password is valid
    resp := &password.CurrentPasswordValidationResponse{
        IsValid: true,
        Message: "Current password is valid",
    }
    response.CreateSuccessResponse(resp, w)
}
```

### Step 5: Update User Handler Routes

**File**: `microservices/onramp/modules/user/handler.go`

Add new routes to the user handler:

```go
import (
    // ... existing imports
    RestPasswordPolicy "synapse-its.com/shared/rest/onramp/user/password-policy"
)

func (h *Handler) RegisterRoutes(router *mux.Router) {
    // ... existing code ...
    
    // Routes that don't require user-specific authorization
    userRouter.HandleFunc("/permissions", RestUserPermissions.Handler).Methods(http.MethodGet)
    userRouter.HandleFunc("/password-policy", RestPasswordPolicy.GetPasswordPolicyHandler).Methods(http.MethodGet)
    
    // ... existing user-specific routes ...
    
    // Add current password validation route
    userSpecificRouter.HandleFunc("/auth-methods/{authMethodId}/validate-current-password", 
        RestAuthMethods.ValidateCurrentPasswordHandler).Methods(http.MethodPost)
}
```

### Step 6: Enhanced Password Update Handler

**File**: `shared/rest/onramp/user/auth-methods/authmethods.go`

The existing `UpdatePasswordHandler` already implements proper validation using `password.ExtractAndValidatePasswordUpdate()` which:
- Validates current password is not empty
- Validates new password meets policy requirements
- Validates confirm password matches new password
- Validates current password against stored hash

**No changes needed** - the current implementation already enforces password policy on the backend.

### Step 7: Add Required Imports

**File**: `shared/rest/onramp/user/auth-methods/authmethods.go`

Ensure these imports are present:

```go
import (
    // ... existing imports
    authtypes "synapse-its.com/shared/rest/domain/auth/types"
)
```

### Step 8: Create Tests

**File**: `shared/api/password/password_test.go`

Add tests for new functions:

```go
func TestGetPasswordPolicy(t *testing.T) {
    policy := GetPasswordPolicy()
    
    assert.Equal(t, 8, policy.MinLength)
    assert.Equal(t, 256, policy.MaxLength)
    assert.True(t, policy.RequireUppercase)
    assert.True(t, policy.RequireLowercase)
    assert.True(t, policy.RequireNumber)
    assert.True(t, policy.RequireSpecial)
    assert.Equal(t, "!@#$%^&*", policy.SpecialChars)
    assert.NotEmpty(t, policy.Regex)
    assert.Len(t, policy.Rules, 6)
}

func TestValidateCurrentPasswordOnly(t *testing.T) {
    tests := []struct {
        name     string
        password string
        wantErr  bool
    }{
        {"valid password", "somepassword", false},
        {"empty password", "", true},
        {"whitespace only", "   ", true},
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := ValidateCurrentPasswordOnly(tt.password)
            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }
        })
    }
}
```

## API Endpoints Summary

After implementation, the following endpoints will be available:

1. **GET** `/api/user/password-policy` - Returns password policy rules
2. **POST** `/api/user/{userId}/auth-methods/{authMethodId}/validate-current-password` - Validates current password
3. **PATCH** `/api/user/{userId}/auth-methods/{authMethodId}/password` - Updates password (existing, enhanced)

## Testing Strategy

1. **Unit Tests**: Test password policy functions and validation logic
2. **Integration Tests**: Test API endpoints with various scenarios
3. **Frontend Integration**: Verify frontend can consume policy endpoint
4. **Security Testing**: Ensure password validation cannot be bypassed

## Deployment Considerations

1. **Backward Compatibility**: All changes are additive, no breaking changes
2. **Database**: No database schema changes required
3. **Environment Variables**: Existing password configuration remains unchanged
4. **Monitoring**: Add logging for password policy requests and validation attempts

## Security Notes

- Current password validation uses constant-time comparison
- Password hashing uses Argon2id with configurable parameters
- All password validation errors return generic messages to prevent information disclosure
- Rate limiting should be considered for password validation endpoints
